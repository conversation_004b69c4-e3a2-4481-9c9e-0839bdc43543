# Next.js Foundation

A modern foundation for building products. Powerful, extensible, and designed for innovation.

## Features

- 🚀 **Next.js App Router** - Built with the latest Next.js features
- 🔐 **Supabase Auth & Database** - Secure authentication and real-time database
- 🌐 **Internationalization** - Multi-language support with next-intl (English & French)
- 🎨 **Modern UI Components** - Built with shadcn/ui and Tailwind CSS
- 🔄 **State Management** - React Query for efficient data fetching
- 📱 **Responsive Design** - Mobile-first approach
- 🛠️ **Developer Experience**
  - TypeScript for type safety
  - ESLint & Prettier for code formatting
  - Husky & lint-staged for git hooks
  - Commitlint for conventional commits

## Prerequisites

- Node.js 18+
- Supabase account
- bun run, npm, or yarn
- Docker (for local Supabase development)

## Getting Started

1. Clone the repository:

```bash
git clone [your-repo-url]
cd [your-project-name]
```

2. Install dependencies:

```bash
bun run install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Update the following variables in `.env.local`:

### 3.1 Setting up Supabase Locally

This project uses Supabase for authentication and database management. To run Supabase locally for development instead of connecting to a cloud instance, you need Docker installed and running.

Use the following command to start the local Supabase services:

```bash
bunx supabase start
```

This command utilizes the Supabase CLI (installed as a dev dependency) via `bunx` to manage the local Supabase instance defined in the `supabase/` directory. Ensure Docker is running before executing this command. The necessary local Supabase environment variables are usually pre-configured in `.env.example`.

```
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

#### Running Supabase Edge Functions Locally with Environment Variables

To serve Supabase Edge Functions locally and ensure they have access to environment variables (such as API keys in `.env.local`), use:

```bash
bunx supabase functions serve --env-file .env.local
```

This command will load environment variables from `.env.local` and make them available to your edge functions. This is required if you do not want to copy `.env.local` to `.env` or if you maintain multiple environment files for different setups.

4. Start the development server:

```bash
bun run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your application.

## Project Structure

```
├── src/
│   ├── app/                 # Next.js app router
│   ├── components/         # Reusable components
│   ├── i18n/              # Internationalization setup
│   └── providers/         # React context providers
├── public/                # Static assets
├── messages/             # Translation files
├── supabase/            # Supabase configuration
└── ...
```

## Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run lint` - Run ESLint
- `bun run format` - Format code with Prettier
- `bun run type-check` - Run TypeScript checks

## Internationalization

The application supports multiple languages through next-intl. Currently supported:

- English (default)
- French

Add translations in the `messages/` directory.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
