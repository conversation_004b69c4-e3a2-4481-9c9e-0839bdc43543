{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "bun eslint .", "lint:fix": "bun eslint . --fix", "format": "bun prettier --write .", "format:check": "bun prettier --check .", "type-check": "bun type-check", "prepare": "husky", "functions": "supabase functions serve --env-file ./.env.local"}, "dependencies": {"@11labs/react": "^0.1.4", "@bprogress/next": "^3.2.11", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.72.0", "@tanstack/react-query-devtools": "^5.72.0", "@useparagon/connect": "^1.0.33", "autoprefixer": "10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "next": "latest", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "pretty-bytes": "^7.0.0", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-phone-number-input": "^3.4.12", "resend": "^4.2.0", "sonner": "^2.0.3", "timezones-list": "^3.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint-community/eslint-plugin-eslint-comments": "^4.5.0", "@eslint-react/eslint-plugin": "^1.47.2", "@next/eslint-plugin-next": "^15.2.5", "@tanstack/eslint-plugin-query": "^5.72.1", "@types/lodash": "^4.17.16", "@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-x": "^4.10.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "8.4.49", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}