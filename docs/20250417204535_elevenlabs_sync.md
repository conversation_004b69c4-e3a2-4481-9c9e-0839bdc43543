# Managing ElevenLabs Conversations & Agents with Supabase

This document outlines the process for managing agent data directly with ElevenLabs and synchronizing conversation data from ElevenLabs into our Supabase database. Agent creation and updates are handled via a dedicated Supabase Edge Function, triggered by actions within our application, ensuring direct consistency with ElevenLabs. Conversation data is synchronized periodically using `pg_cron` and time-based partitioning for the `conversations` table. All data is scoped to organizations and users, with strict authentication and authorization enforced by Row Level Security (RLS) policies in the database.

**Note:** This process involves:

1.  **Agent Management:** Directly creating/updating agents in ElevenLabs via an Edge Function when they are modified in our system.
2.  **Conversation Sync:** Periodically fetching conversation details from ElevenLabs for the agents managed by our system.

---

## Required Extensions

Before using the synchronization features described in this document, you must enable the following PostgreSQL extensions in your Supabase project:

- `pg_cron` (for scheduling background jobs)
- `pg_net` (for making HTTP requests from the database, e.g., to call Edge Functions)

You can enable these extensions from the Supabase dashboard (Database → Extensions), or by running the following SQL as a superuser:

```sql
-- Enable pg_cron for scheduling
create extension if not exists pg_cron with schema extensions;

-- Enable pg_net for HTTP requests
create extension if not exists pg_net with schema extensions;
```

---

## Node.js Integration for ElevenLabs Sync

To automate the synchronization of agents and conversations between ElevenLabs and your Supabase database, you can use the official ElevenLabs Node.js SDK. This SDK provides methods to fetch, create, and update agents and conversations, which can be integrated into Supabase Edge Functions or backend scripts.

### Setup

In Supabase Edge Functions (Deno), dependencies are managed via import maps or direct URL imports. The ElevenLabs SDK can be imported as a module, and the API key should be securely provided via environment variables.

**Example: Configure the client with your API key:**

```ts
import { ConversationalAi } from 'npm:elevenlabs' // or use your import map

const client = new ConversationalAi({
  apiKey: Deno.env.get('ELEVENLABS_API_KEY'), // Store securely in project env
})
```

### Core Functions

- **Get all agents:**

  ```js
  const agentsPage = await client.getAgents()
  const agents = agentsPage.agents // Array of agent objects
  ```

- **Get a single agent:**

  ```js
  const agent = await client.getAgent(agentId)
  ```

- **Create an agent:**

  ```js
  // Example conversation_config for creating an agent
  const conversationConfig = {
    agent_name: 'SupportBot',
    first_message_prompt: 'Hello! How can I help you today?',
    system_prompt: 'You are a helpful support assistant.',
    evaluation_criteria: {
      helpfulness: { description: 'Was the answer helpful?' },
    },
    data_collection_points: {
      collect_email: true,
      collect_feedback: true,
    },
    turn_timeout: 30,
    max_duration_seconds: 600,
    // Add any other fields required by your schema or ElevenLabs API
  }

  const response = await client.createAgent({
    conversation_config: conversationConfig,
  })
  const newAgentId = response.agent_id
  ```

- **Update an agent:**

  ```js
  // Example update config for updating an agent
  const updateConfig = {
    agent_name: 'SupportBot v2',
    system_prompt: 'You are a highly knowledgeable support assistant.',
    evaluation_criteria: {
      helpfulness: { description: 'Was the answer helpful and accurate?' },
    },
    // Only include fields you want to update; others will remain unchanged
  }

  await client.updateAgent(agentId, updateConfig)
  ```

- **Get all conversations (optionally for a specific agent):**

  ```js
  const conversationsPage = await client.getConversations({ agent_id: agentId })
  const conversations = conversationsPage.conversations // Array of conversation objects
  ```

- **Get a single conversation:**

  ```js
  const conversation = await client.getConversation(conversationId)
  ```

### Syncing with Supabase

These Node.js functions should be called from within Supabase Edge Functions or backend scripts. The typical sync flow is:

1. **Fetch agents and conversations from ElevenLabs using the SDK.**
2. **Upsert the results into your Supabase database** using the appropriate tables (`agents`, `conversations`) and following the schema and RLS policies described below.
3. **Handle incremental sync** by tracking the last sync timestamp and only fetching new/updated records.
4. **Use service role keys or privileged access** for the sync process to ensure it can write to all organizations' data.

**Example: Upserting an agent into Supabase (using the supabase-js client):**

```js
const { createClient } = require('@supabase/supabase-js')
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)

// Example: Inserting/Updating an agent in Supabase after successful ElevenLabs API call
// (This would typically happen within the Edge Function managing agent updates)
async function upsertAgentInSupabase(agentDataFromElevenLabs, localConfig) {
  await supabase.from('agents').upsert({
    agent_id: agentDataFromElevenLabs.agent_id, // Primary Key from ElevenLabs
    agent_name: localConfig.agent_name,
    first_message_prompt: localConfig.first_message_prompt,
    system_prompt: localConfig.system_prompt,
    evaluation_criteria: localConfig.evaluation_criteria,
    data_collection_points: localConfig.data_collection_points,
    turn_timeout: localConfig.turn_timeout,
    max_duration_seconds: localConfig.max_duration_seconds,
    organization_id: localConfig.organization_id,
    user_id: localConfig.user_id,
    // last_updated will be handled by DB trigger or application logic
  })
}
```

**Example: Upserting a conversation:**

```js
async function upsertConversation(conversation) {
  await supabase.from('conversations').upsert({
    conversation_id: conversation.id,
    agent_id: conversation.agent_id,
    // ...other fields
    created_at: conversation.created_at,
    // ...etc
  })
}
```

> **Note:** You can schedule these sync scripts using `pg_cron` or run them as Supabase Edge Functions triggered by HTTP requests or cron jobs.

---

## 1. Database Schema

We store both agent configurations and conversation data, scoped to organizations and users.

### `agents` Table

This table stores the configuration for each agent defined in our system and maps it to its corresponding ElevenLabs Agent ID. Each agent belongs to an organization and may be linked to a specific user.

```sql
CREATE TABLE public.agents (
    agent_id TEXT PRIMARY KEY, -- ID from ElevenLabs
    agent_name TEXT NOT NULL,
    first_message_prompt TEXT,
    system_prompt TEXT,
    evaluation_criteria JSONB,
    data_collection_points JSONB,
    turn_timeout INTEGER,
    max_duration_seconds INTEGER,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    last_updated TIMESTAMPTZ NOT NULL DEFAULT now()
    -- Note: Consider adding a DB trigger to update last_updated automatically
);

-- Add indexes (Primary Key index on agent_id is created automatically)
CREATE INDEX idx_agents_organization_id ON public.agents(organization_id);
CREATE INDEX idx_agents_user_id ON public.agents(user_id);

-- Enable RLS
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
-- See RLS policy section below.
```

### `conversations` Table (Partitioned)

This table stores the synchronized conversation data from ElevenLabs. It's partitioned by `created_at` for efficiency. Each conversation is linked to an agent and optionally a user. The organization is inferred by joining through the agent.

```sql
CREATE TABLE public.conversations (
    conversation_id TEXT NOT NULL,
    agent_id TEXT NOT NULL, -- References public.agents(agent_id)
    status TEXT,
    transcript JSONB,
    analysis JSONB,
    metadata JSONB,
    conversation_initiation_client_data JSONB,
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL,
    last_synced TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT conversations_pkey PRIMARY KEY (conversation_id, created_at)
) PARTITION BY RANGE (created_at);

-- Add indexes
CREATE INDEX idx_conversations_agent_id ON public.conversations(agent_id);
CREATE INDEX idx_conversations_status ON public.conversations(status);
CREATE INDEX idx_conversations_created_at_idx ON public.conversations(created_at);

-- Enable RLS
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
-- See RLS policy section below.
```

### Partitioning Strategy

Partition the `conversations` table by month (or week/day depending on volume).

**Example: Creating a partition for May 2025**

```sql
CREATE TABLE public.conversations_y2025m05 PARTITION OF public.conversations
    FOR VALUES FROM ('2025-05-01 00:00:00+00') TO ('2025-06-01 00:00:00+00');
```

Automate the creation of future partitions using `pg_cron` or another mechanism.

---

## 2. Authentication & Row Level Security (RLS)

All access to agent and conversation data is authenticated and strictly scoped by organization and user using RLS policies.

### Example RLS Policies

#### Agents Table

```sql
-- Users can view agents in their organizations
CREATE POLICY "Users can view agents in their organizations"
  ON public.agents FOR SELECT
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

-- Users can insert agents in their organizations (owner/admin only)
CREATE POLICY "Users can insert agents in their organizations"
  ON public.agents FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );

-- Users can update agents in their organizations (owner/admin only)
CREATE POLICY "Users can update agents in their organizations"
  ON public.agents FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );
```

#### Conversations Table

> **Note:** The conversations table does not have a direct organization_id column. Organization access is enforced by joining the agent_id to the agents table and checking the agent's organization_id.

```sql
-- Users can view conversations in their organizations
CREATE POLICY "Users can view conversations in their organizations"
  ON public.conversations FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
        )
    )
  );

-- Users can insert conversations in their organizations (owner/admin only)
CREATE POLICY "Users can insert conversations in their organizations"
  ON public.conversations FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );

-- Users can update conversations in their organizations (owner/admin only)
CREATE POLICY "Users can update conversations in their organizations"
  ON public.conversations FOR UPDATE
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );
```

> **Note:** The sync process (Edge Function or backend job) must use a Supabase service role or equivalent privileged access to read and write data across all organizations.

---

## 3. Agent Management via Edge Function

Agent creation and updates are handled directly via a dedicated Supabase Edge Function (e.g., `manage-elevenlabs-agent`) rather than through a periodic background sync. This function is triggered by your application logic whenever a user creates or modifies an agent's configuration.

**Workflow:**

1.  **Trigger:** Your application backend or frontend triggers the `manage-elevenlabs-agent` Edge Function when an agent needs to be created or updated. The trigger payload includes the full desired agent configuration and indicates whether it's a new agent or an update to an existing one (providing the `agent_id` if it's an update).
2.  **Edge Function Logic:**
    - Receives the agent configuration payload.
    - Authenticates the request (ensuring the user has permission to manage agents for the specified organization).
    - **If creating a new agent:**
      - Calls the ElevenLabs `POST /v1/convai/agents/create` endpoint using the provided configuration.
      - On success, receives the new `agent_id` from ElevenLabs.
      - Inserts a new record into the `public.agents` table using the received `agent_id` as the primary key and storing the rest of the configuration.
    - **If updating an existing agent:**
      - Calls the ElevenLabs `PATCH /v1/convai/agents/{agent_id}` endpoint, providing the `agent_id` and the fields to be updated.
      - On success, updates the corresponding record in the `public.agents` table with the modified configuration details and updates the `last_updated` timestamp.
    - Handles potential errors from the ElevenLabs API (e.g., validation errors, rate limits, 404 on update if the agent was deleted externally).
    - Returns a success or error response to the calling application.

**Benefits:**

- **Real-time Consistency:** Ensures that agent configurations in your database and ElevenLabs are always aligned immediately after an update.
- **Simplified Logic:** Eliminates the need for complex background comparison and synchronization logic for agents.
- **Clear Responsibility:** Centralizes the interaction with the ElevenLabs agent API within a single Edge Function.

---

## 4. Conversation Synchronization (`sync` Logic) - Incremental Approach

This process fetches **new or updated** conversation details for known agents since the last successful sync, aiming for near real-time updates.

### Enable pg_cron

Ensure `pg_cron` is enabled in your Supabase project settings (Database -> Extensions).

### Synchronization Logic (Conceptual Function/Edge Function)

A Supabase Edge Function (`sync-elevenlabs-conversations`) is recommended for handling external API calls and state management (like tracking the last sync time).

1.  **Determine Last Sync Timestamp:** Retrieve the timestamp of the most recently created conversation successfully processed in the _previous_ run. This could be stored in a dedicated metadata table or retrieved by querying the latest `created_at` from `public.conversations` (ensure this reflects ElevenLabs creation time). Let's call this `lastSyncTimestamp`.
2.  **Fetch Synced Agent IDs:** Query `public.agents` to get the list of `agent_id` (the primary key, which is the ElevenLabs agent ID) for agents managed by your system.
3.  **Iterate Through Agents:** For each `agentId`:
    a. **Fetch _New_ Conversations:** Call the ElevenLabs API to get conversations for the agent created _after_ `lastSyncTimestamp`. This requires API support for timestamp filtering (e.g., `GET https://api.elevenlabs.io/v1/convai/conversations?agent_id={agentId}&created_after={lastSyncTimestamp}`). **(Note: Verify API support for such filtering).** If timestamp filtering isn't available per agent, fetch conversations created after `lastSyncTimestamp` globally (if possible) or fall back to fetching the list and filtering locally (less efficient for high frequency).

    b. **Iterate Through _New/Updated_ Conversations:** For each conversation returned by the API:

    - Get the `conversationId` and `detailData` (the API might return details directly if filtered, or you might need to fetch details via `GET /v1/convai/conversations/{conversationId}`).

    - **Process Details:**
      _ Parse the JSON response (`detailData`).
      _ **Extract Score (Optional but recommended):** Process `detailData.analysis.evaluation_criteria_results` to extract scores from XML tags in the `rationale`, updating the `analysis` object.
      \_ Extract `status`, `transcript`, `metadata`, `analysis` (with updated scores), `conversation_initiation_client_data`, and the conversation's creation timestamp (`created_at`).

    - **Insert/Update Database:** Execute an `INSERT ... ON CONFLICT` statement for the `public.conversations` table. This handles both brand new conversations and updates to the status/analysis of ongoing ones fetched since the last sync.

    ```sql
    INSERT INTO public.conversations (
        conversation_id, agent_id, status, transcript, analysis, metadata,
        conversation_initiation_client_data, data, created_at, last_synced
    )
    VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW() -- Adjusted parameter count
    )
    ON CONFLICT (conversation_id, created_at)
    DO UPDATE SET
        status = EXCLUDED.status,
        transcript = EXCLUDED.transcript,
        analysis = EXCLUDED.analysis,
        metadata = EXCLUDED.metadata,
        conversation_initiation_client_data = EXCLUDED.conversation_initiation_client_data,
        data = EXCLUDED.data,
        last_synced = NOW();
    ```

    \_(Ensure the `ON CONFLICT` target columns match your primary key or unique constraint. Using `(conversation_id, created_at)` aligns with the suggested primary key for partitioning).\_

4.  **Update Last Sync Timestamp:** After successfully processing all new conversations in this run, record the timestamp of the _latest_ `created_at` among the processed conversations. This becomes the `lastSyncTimestamp` for the _next_ run. Store this reliably (e.g., in the metadata table).
5.  **Return/Log Stats:** Collect statistics (new conversations processed, updated, failed) for logging and monitoring.

### Scheduling the Job with pg_cron

With the faster incremental approach, scheduling can be much more frequent.

**Example: Schedule to run every 1 minute**

```sql
SELECT cron.schedule(
    'sync-elevenlabs-conversations-1min',
    '*/1 * * * *',
    $$ SELECT net.http_post(
           url:='https://<project_ref>.supabase.co/functions/v1/sync-elevenlabs-conversations',
           headers:='{"Authorization": "Bearer <supabase_service_role_key>", "Content-Type": "application/json"}'::jsonb,
           body:='{}'::jsonb
       ); $$
);
```

**Unscheduling:**

```sql
SELECT cron.unschedule('sync-elevenlabs-conversations-1min');
```

---

## 5. Considerations

- **API Keys:** Securely manage the ElevenLabs API key using Supabase Vault secrets, accessible by Edge Functions.
- **Error Handling:** Implement robust error handling (network errors, API errors, data parsing errors, DB errors) and logging within the Edge Function. Consider alerting on failures.
- **Rate Limiting:** Respect ElevenLabs API rate limits for both the agent management Edge Function calls and the conversation sync process. Implement appropriate error handling and potential backoff strategies.
- **Idempotency:** The `INSERT ... ON CONFLICT` logic makes the conversation sync largely idempotent. Ensure the agent management Edge Function handles potential duplicate creation attempts gracefully if necessary (though direct updates are usually less prone to this than sync jobs).
- **Incremental Sync State (Conversations):** Reliably storing and retrieving the `lastSyncTimestamp` for conversation sync is critical. Using a dedicated metadata table is often safer than relying solely on querying the latest record in `conversations`.
- **API Filtering Support (Conversations):** The efficiency of the incremental conversation sync depends on the ElevenLabs API supporting effective timestamp-based filtering (`created_after` or similar). **Verify this capability.** If not available, the fallback of fetching lists and filtering locally reduces the benefit of high-frequency cron jobs.
- **Agent Edge Function Reliability:** Ensure the `manage-elevenlabs-agent` Edge Function is robust, handles errors gracefully (e.g., network issues, API errors from ElevenLabs), and provides clear feedback to the calling application. Consider retry mechanisms for transient errors.
- **Triggering Agent Updates:** Ensure the application logic correctly triggers the agent management Edge Function on every relevant agent create/update action.
- **User & Organization Mapping:** Linking conversations and agents back to `profiles.id` and `organizations.id` is required and enforced by the schema and RLS policies. For conversations, organization access is enforced by joining through the agent.
- **Partition Management:** Crucial to automate the creation of future partitions (e.g., a monthly `pg_cron` job to create the next month's partition).
- **Backfilling:** Plan a strategy for initially populating historical conversations if required. This might involve a separate script or modifying the sync function temporarily.
- **Score Extraction:** The reliability of extracting scores from XML in the `rationale` depends on ElevenLabs consistently providing it in that format. Monitor this and adapt if the format changes.

---
