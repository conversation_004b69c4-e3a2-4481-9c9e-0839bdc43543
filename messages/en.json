{"Header": {"title": "Foundation", "about": "About", "docs": "Documentation", "blog": "Blog", "login": "<PERSON><PERSON>", "logout": "Sign Out", "organizations": "Organizations", "profile": "Profile", "settings": "Settings", "billing": "Billing", "notifications": "Notifications", "switchLanguage": "Switch language", "legal": {"privacy": "Privacy", "terms": "Terms"}}, "Footer": {"copyright": "Copyright", "rights": "All rights reserved", "privacy": "Privacy Policy", "terms": "Terms of Service", "links": {"twitter": "Twitter", "github": "GitHub", "privacy": "Privacy", "terms": "Terms"}}, "Home": {"hero": {"title": "Build Better Software Faster", "description": "A modern foundation for building products. Powerful, extensible, and designed for innovation.", "primaryCTA": "Get Started", "secondaryCTA": "Learn More"}, "features": {"feature1": {"title": "Lightning Fast", "description": "Built with performance in mind, ensuring your applications run at peak efficiency."}, "feature2": {"title": "Secure by <PERSON><PERSON><PERSON>", "description": "Enterprise-grade security features built-in, not bolted on."}, "feature3": {"title": "Developer Experience", "description": "Intuitive APIs and comprehensive documentation for a seamless development experience."}}, "cta": {"title": "Ready to get started?", "description": "Join thousands of developers building better software with Foundation.", "primaryCTA": "Start Free Trial", "secondaryCTA": "Contact Sales"}}, "auth": {"email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "login": {"title": "Welcome back", "description": "Sign in to your account", "submit": "Sign in", "success": "Successfully signed in", "error": "Failed to sign in", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "signupLink": "Sign up", "invalidEmail": "Invalid email address", "passwordMinLength": "Password must be at least 6 characters"}, "signup": {"title": "Create an account", "description": "Enter your details to get started", "submit": "Sign up", "success": "Check your email for confirmation link", "error": "Failed to create account", "haveAccount": "Already have an account?", "loginLink": "Sign in", "invalidEmail": "Invalid email address", "passwordMinLength": "Password must be at least 6 characters", "passwordMismatch": "Passwords don't match", "nameRequired": "Name is required"}, "social": {"or": "or continue with", "google": "Continue with Google", "microsoft": "Continue with Microsoft", "error": "Failed to sign in with social provider"}, "verify": {"title": "Verify your email", "description": "We've sent you a verification code. Please enter it below.", "emailSent": "We've sent a verification code to {email}", "codeLabel": "Verification Code", "submit": "<PERSON><PERSON><PERSON>", "verifying": "Verifying...", "resend": "Resend code", "resending": "Resending...", "resendCountdown": "Resend code in {seconds}s", "error": {"title": "Verification Error", "description": "No email address provided for verification. Please try signing up again."}, "success": "Email verified successfully!", "resendSuccess": "Verification email has been resent"}, "errors": {"InvalidCredentials": "Invalid email or password. Please try again.", "EmailNotConfirmed": "Please verify your email before signing in.", "InvalidEmail": "Please enter a valid email address.", "WeakPassword": "Password should be at least 6 characters long.", "EmailInUse": "This email is already registered. Try signing in instead.", "OtpExpired": "The verification code has expired. Please request a new one.", "Default": "An error occurred. Please try again."}, "signout": {"success": "Successfully signed out"}, "resetPassword": {"requestSuccess": "If an account exists, a password reset link will be sent.", "success": "Password has been reset successfully", "title": "Reset Password", "description": "Enter your new password below.", "submit": "Reset Password", "resetting": "Resetting...", "backToLogin": "Back to Login", "passwordMinLength": "Password must be at least 6 characters", "passwordMismatch": "Passwords don't match"}, "logout": {"title": "Signing Out", "description": "Please wait while we sign you out...", "signingOut": "Signing out..."}, "forgotPassword": {"title": "Forgot Password", "description": "Enter your email address and we'll send you a link to reset your password.", "submit": "Send reset email", "requesting": "Sending...", "backToLogin": "Back to Login", "success": {"title": "Check Your Email", "description": "Password reset instructions sent", "message": "If you registered using your email and password, you will receive a password reset email."}, "invalidEmail": "Invalid email address"}, "error": {"title": "Authentication Error", "description": "There was a problem with your request", "default": "An unexpected error occurred. Please try again.", "emailNotConfirmed": "Please verify your email address before signing in.", "invalidCredentials": "Invalid email or password. Please try again.", "emailExists": "An account with this email already exists.", "invalidToken": "Invalid or expired reset link. Please request a new one.", "expired": "This link has expired. Please request a new one.", "backToLogin": "Back to Login", "createAccount": "Create an Account"}}, "Organizations": {"title": "Organizations", "loading": "Loading organizations...", "createNew": "Create Organization", "noOrganizations": "No Organizations Yet", "createFirst": "Create your first organization to get started", "getStarted": "Get Started", "members": "{count, plural, =1 {# member} other {# members}}", "types": {"personal": "Personal", "education": "Education", "startup": "Startup", "agency": "Agency", "company": "Company"}, "roles": {"owner": "Owner", "admin": "Admin", "member": "Member"}, "create": {"title": "Create Organization", "formTitle": "Organization Details", "formDescription": "Fill in the details below to create your organization", "submitting": "Creating...", "submit": "Create Organization", "fields": {"name": {"label": "Organization Name", "description": "Enter a unique name for your organization"}, "description": {"label": "Description", "description": "Briefly describe your organization (optional)"}, "type": {"label": "Organization Type", "description": "Select the type that best describes your organization", "placeholder": "Select organization type"}, "size": {"label": "Company Size", "description": "Number of people in your organization", "placeholder": "Select company size"}}, "success": "Organization created successfully", "error": "Failed to create organization"}, "update": {"success": "Organization updated successfully", "error": "Failed to update organization"}, "delete": {"success": "Organization deleted successfully", "error": "Failed to delete organization", "confirm": "Are you sure you want to delete this organization?"}, "form": {"name": "Organization Name", "description": "Description", "type": "Organization Type", "companySize": "Company Size", "submit": "Create Organization"}, "navigation": {"dashboard": "Dashboard", "members": "Members", "settings": "Settings"}, "dashboard": {"greeting": "Good morning, {name}", "subtitle": "Here's what's happening with your agents", "filters": {"allAgents": "All agents", "today": "Today", "lastWeek": "Last week", "lastMonth": "Last month", "lastYear": "Last year"}, "stats": {"calls": "Number of calls", "numberOfCalls": "Total calls made", "duration": "Average duration", "averageDuration": "Average call length", "totalCost": "Total cost", "credits": "credits", "averageCost": "Average cost", "creditsPerCall": "credits/call"}, "metrics": {"title": "Analytics", "noMetrics": "No metrics", "noMetricsDescription": "There are no metrics for the specified period."}}, "switchOrganization": "Switch Organization", "breadcrumb": {"organizations": "Organizations", "loading": "Loading...", "agents": "Agents", "settings": "Settings", "dashboard": "Dashboard", "team": "Team", "general": "General", "history": "Call History"}, "platform": "Platform", "phones": {"title": "Phone Numbers", "description": "Manage your organization's phone numbers", "addPhone": "Add Phone", "noPhones": "No phone numbers yet", "addFirst": "Add your first phone number to get started", "getStarted": "Get Started", "loading": "Loading phone numbers...", "add": "Add", "cancel": "Cancel", "table": {"phoneNumber": "Phone Number", "label": "Label", "provider": "Provider", "createdAt": "Created At", "actions": {"title": "Actions", "open": "Open menu", "delete": "Delete phone"}}, "addDialog": {"title": "Add Phone Number", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "label": "Label", "labelPlaceholder": "Enter a label for this phone number", "provider": "Provider", "selectProvider": "Select a provider", "twilioSid": "<PERSON><PERSON><PERSON>", "twilioSidPlaceholder": "Enter your Twilio SID", "twilioToken": "<PERSON><PERSON><PERSON>", "twilioTokenPlaceholder": "Enter your <PERSON><PERSON><PERSON> auth token", "terminationUri": "Termination URI", "terminationUriPlaceholder": "Enter SIP termination URI", "username": "Username", "usernamePlaceholder": "Enter SIP username", "password": "Password", "passwordPlaceholder": "Enter SIP password", "outboundConfiguration": "Outbound Configuration", "outboundConfigurationDescription": "Configure where Foundational should send calls for your phone number", "authentication": "Authentication (Optional)", "authenticationDescription": "Provide digest authentication credentials if required by your SIP trunk provider. If left empty, ACL authentication will be used (you'll need to allowlist Foundational IPs).", "sipUsername": "SIP Trunk Username", "sipUsernamePlaceholder": "Username for SIP digest authentication", "sipPassword": "SIP Trunk Password", "sipPasswordPlaceholder": "Password for SIP digest authentication"}, "deleteDialog": {"title": "Delete phone number", "description": "Are you sure you want to delete the phone number {phone}? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}}, "agents": {"title": "Agents", "description": "Manage your conversational AI agents", "createAgent": "Create Agent", "noAgents": "No agents yet", "addFirst": "Create your first agent to get started", "getStarted": "Get Started", "table": {"name": "Name", "templateStatus": "Template", "createdAt": "Created At", "yes": "Yes", "no": "No", "actions": {"open": "Open menu", "edit": "Edit", "markTemplate": "<PERSON> as Temp<PERSON>", "unmarkTemplate": "Unmark as Template", "delete": "Delete Agent"}, "templateDialog": {"title": "Confirm Template Status Change", "markDescription": "Are you sure you want to mark agent \"{name}\" as a template?", "unmarkDescription": "Are you sure you want to remove agent \"{name}\" from templates?", "cancel": "Cancel", "confirm": "Confirm", "updating": "Updating..."}, "deleteDialog": {"title": "Delete Agent", "description": "Are you sure you want to delete agent \"{name}\"? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}}, "form": {"agentName": {"label": "Agent Name", "placeholder": "e.g., Customer Support Bot"}, "systemPrompt": {"label": "System Prompt", "placeholder": "Define the agent's persona and instructions...", "description": "The core instructions determining the agent's behavior."}, "firstMessagePrompt": {"label": "First Message Prompt", "placeholder": "e.g., Hello! How can I help you today?", "description": "The initial message the agent sends to start the conversation."}, "turnTimeout": {"label": "Turn Timeout (seconds)", "placeholder": "e.g., 30", "description": "Max silence duration before agent responds."}, "maxDuration": {"label": "Max Duration (seconds)", "placeholder": "e.g., 600", "description": "Maximum length of the conversation."}, "language": {"title": "Agent Language", "description": "Choose the default language the agent will communicate in."}, "temperature": {"title": "Temperature", "description": "Temperature controls the creativity or randomness of the responses generated by the LLM."}, "voice": {"title": "Voice", "description": "Select the voice you want to use for the agent.", "placeholder": "Select a voice"}, "modelType": {"title": "Voice Model", "description": "Flash is our new recommended model for low latency use cases.", "useFlash": "Use Flash v2", "useTurbo": "Use Turbo v2", "flashDescription": "Optimized for low latency with good quality", "turboDescription": "Higher quality but with increased latency"}, "audioFormat": {"title": "TTS Output Format", "description": "Select the audio format for the agent's voice.", "placeholder": "Select audio format"}, "latency": {"title": "Optimize Streaming Latency", "description": "Configure latency optimizations for the speech generation. Latency can be optimized at the cost of quality.", "quality": "Quality", "speed": "Speed"}, "stability": {"title": "Stability", "description": "Higher values will make speech more consistent, but it can also make it sound monotone. Lower values will make speech sound more expressive, but may lead to instabilities.", "expressive": "Expressive", "consistent": "Consistent"}, "speed": {"title": "Speed", "description": "Controls the speed of the generated speech. Values below 1.0 will slow down the speech, while values above 1.0 will speed it up. Extreme values may affect the quality of the generated speech.", "slower": "Slower", "faster": "Faster"}, "similarity": {"title": "Similarity Boost", "description": "Higher values will boost the overall clarity and consistency of the voice. Very high values may lead to artifacts. Adjusting this value to find the right balance is recommended.", "less": "Less", "more": "More"}, "testAIAgent": "Test AI agent", "maxTokens": {"title": "Limit token usage", "description": "Configure the maximum number of tokens that the LLM can predict. A limit will be applied if the value is greater than 0.", "placeholder": "Enter token limit", "hint": "Use -1 or leave empty for no limit"}, "llm": {"title": "LLM", "description": "Select which provider and model to use for the LLM.", "redirectNotice": "If your chosen LLM is not available at the moment or something goes wrong, we will redirect the conversation to another LLM.", "costNotice": "Currently, the LLM cost is covered by us. In the future, this cost will be passed onto you."}, "knowledge": {"title": "Knowledge Base", "description": "Provide the LLM with domain-specific information to help it answer questions more accurately.", "addDocuments": "Add Documents", "remove": "Remove", "noDocuments": "No documents added yet. Click 'Add Documents' to attach knowledge bases to this agent.", "useRag": "Use RAG", "ragDescription": "Retrieval-Augmented Generation (RAG) increases the agent's maximum Knowledge Base size. The agent will have access to relevant pieces of attached Knowledge Base during answer generation.", "indexing": "Indexing...", "indexed": "Indexed", "indexFailed": "Indexing failed"}}, "create": {"title": "Create New Agent", "description": "Configure a new conversational AI agent.", "submitButton": "Create Agent"}, "edit": {"title": "Edit Agent", "description": "Update the agent's configuration.", "submitButton": "Save Changes"}, "create_agent": "Create agent", "create_an_ai_agent": "Create an AI agent", "ai_agent_name": "AI Agent name"}, "templates": {"title": "Agent Templates", "description": "Browse and manage reusable agent templates.", "noTemplates": "No Templates Available", "createFirst": "Mark an existing agent as a template to see it here."}, "integrations": {"title": "Integrations", "description": "Connect your organization with external services", "crm": {"title": "CRM Integration", "description": "Connect your CRM to sync contacts and deals", "hubspot": {"title": "HubSpot", "description": "Connect your HubSpot account to sync contacts, companies, and deals", "connect": "Connect HubSpot", "disconnect": "Disconnect HubSpot", "connected": "Connected to HubSpot", "connecting": "Connecting to HubSpot...", "waitMessage": "Please wait while we complete the connection.", "errors": {"noCode": "No authorization code received from HubSpot", "connectionFailed": "Failed to connect to HubSpot", "generic": "An error occurred while connecting to HubSpot"}, "success": {"connected": "Successfully connected to HubSpot", "disconnected": "Successfully disconnected from HubSpot"}, "portal": {"id": "Portal ID", "type": "Account Type", "timezone": "Time Zone", "currency": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "contacts": {"title": "Contacts", "description": "Manage your organization's contacts", "addContact": "Add Contact", "noContacts": "No Contacts", "addFirst": "Add your first contact to get started", "getStarted": "Get Started", "noHubSpotConnection": "No HubSpot Connection", "connectHubSpotFirst": "You need to connect to HubSpot before you can manage contacts", "connectHubSpot": "Connect HubSpot", "table": {"name": "Name", "email": "Email", "phone": "Phone", "jobTitle": "Job Title", "actions": {"title": "Actions", "open": "Open menu", "edit": "Edit", "delete": "Delete"}}, "addDialog": {"title": "Add Contact", "description": "Add a new contact to your organization", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "jobTitle": "Job Title", "cancel": "Cancel", "submit": "Add Contact", "lifecycleStage": "Lifecycle Stage", "leadStatus": "Lead Status"}, "editContact": "Edit Contact", "editContactDescription": "Update contact information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "jobTitle": "Job Title", "cancel": "Cancel", "save": "Save", "delete": "Delete", "deleteDialog": {"title": "Delete Contact", "description": "Are you sure you want to delete this contact? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}, "success": {"created": "Contact created successfully", "updated": "Contact updated successfully", "deleted": "Contact deleted successfully"}, "errors": {"createFailed": "Failed to create contact", "updateFailed": "Failed to update contact", "deleteFailed": "Failed to delete contact"}, "phonePlaceholder": "Enter phone number", "lifecycleStage": "Lifecycle Stage", "leadStatus": "Lead Status", "selectLifecycleStage": "Select Lifecycle Stage", "selectLeadStatus": "Select Lead Status"}, "companies": {"title": "Companies", "description": "Manage your organization's companies", "addCompany": "Add Company", "editCompany": "Edit Company", "addCompanyDescription": "Add a new company to your organization", "editCompanyDescription": "Update company information", "noCompanies": "No Companies", "addFirst": "Add your first company to get started", "getStarted": "Get Started", "loading": "Loading companies...", "noHubSpotConnection": "No HubSpot Connection", "connectHubSpotFirst": "You need to connect to HubSpot before you can manage companies", "connectHubSpot": "Connect HubSpot", "add": "Add Company", "save": "Save Changes", "addSuccess": "Company added successfully", "updateSuccess": "Company updated successfully", "addError": "Failed to add company", "updateError": "Failed to update company", "form": {"name": "Company Name", "domain": "Domain", "industry": "Industry", "type": "Company Type", "description": "Description", "linkedin_url": "LinkedIn URL", "annual_revenue": "Annual Revenue", "employee_count": "Employee Count", "phone": "Phone", "street_address": "Street Address", "city": "City", "state": "State/Province", "postal_code": "Postal Code", "country": "Country", "timezone": "Timezone", "selectIndustry": "Select Industry", "selectTimezone": "Select Timezone", "selectLifecycleStage": "Select Lifecycle Stage", "selectLeadStatus": "Select Lead Status", "phonePlaceholder": "Enter phone number", "hs_lead_status": "HS Lead Status", "lifecyclestage": "Lifecycle Stage", "selectHsLeadStatus": "Select HS Lead Status", "selectType": "Select Type"}, "table": {"name": "Name", "domain": "Domain", "industry": "Industry", "type": "Type", "phone": "Phone", "city": "City", "country": "Country", "actions": {"title": "Actions", "open": "Open menu", "edit": "Edit company", "delete": "Delete company"}}, "addDialog": {"title": "Add Company", "description": "Add a new company to your organization", "cancel": "Cancel", "submit": "Add Company"}, "editDialog": {"title": "Edit Company", "description": "Update company information", "cancel": "Cancel", "save": "Save Changes"}, "success": {"created": "Company created successfully", "updated": "Company updated successfully", "deleted": "Company deleted successfully"}, "errors": {"createFailed": "Failed to create company", "updateFailed": "Failed to update company", "deleteFailed": "Failed to delete company"}, "deleteDialog": {"title": "Delete Company", "description": "Are you sure you want to delete this company? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}}, "salesforce": {"accounts": {"title": "Salesforce Accounts", "description": "Manage your Salesforce accounts", "addAccount": "Add Account", "noAccounts": "No accounts found", "addFirst": "Add your first Salesforce account to get started", "getStarted": "Get Started", "noSalesforceConnection": "Salesforce Not Connected", "connectSalesforceFirst": "Connect your Salesforce account to manage accounts", "connectSalesforce": "Connect Salesforce", "save": "Save Changes", "create": "Create Account", "cancel": "Cancel", "table": {"name": "Name", "phone": "Phone", "type": "Type", "website": "Website", "edit": "Edit", "actions": {"title": "Actions", "open": "Open menu", "edit": "Edit", "delete": "Delete"}}, "form": {"name": "Account Name", "type": "Type", "website": "Website", "phone": "Phone", "billingStreet": "Billing Street", "billingCity": "Billing City", "billingState": "Billing State/Province", "billingPostalCode": "Billing Zip/Postal Code", "billingCountry": "Billing Country", "shippingStreet": "Shipping Street", "shippingCity": "Shipping City", "shippingState": "Shipping State/Province", "shippingPostalCode": "Shipping Zip/Postal Code", "shippingCountry": "Shipping Country", "description": "Description"}, "add": {"title": "Add Salesforce Account", "success": "Account added successfully", "error": "Failed to add account"}, "edit": {"title": "Edit Salesforce Account", "success": "Account updated successfully", "error": "Failed to update account"}, "delete": {"title": "Delete Account", "description": "Are you sure you want to delete this account? This action cannot be undone.", "confirm": "Delete Account", "cancel": "Cancel", "success": "Account deleted successfully", "error": "Failed to delete account"}}, "contacts": {"title": "Salesforce Contacts", "description": "Manage your Salesforce contacts", "addContact": "Add Contact", "editContact": "Edit Contact", "editContactDescription": "Update contact information", "noContacts": "No contacts found", "addFirst": "Add your first Salesforce contact to get started", "getStarted": "Get Started", "noSalesforceConnection": "Salesforce Not Connected", "connectSalesforceFirst": "Connect your Salesforce account to manage contacts", "connectSalesforce": "Connect Salesforce", "add": "Add Contact", "save": "Save Changes", "cancel": "Cancel", "delete": "Delete", "addContactDescription": "Add a new contact to your organization", "form": {"basicInfo": "Basic Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "phonePlaceholder": "Enter phone number", "title": "Title", "account": "Account", "selectAccount": "Select an account", "mailingAddress": "Mailing Address", "mailingStreet": "Street", "mailingCity": "City", "mailingState": "State/Province", "mailingPostalCode": "Zip/Postal Code", "mailingCountry": "Country", "additionalInfo": "Additional Information", "description": "Description"}, "table": {"name": "Name", "email": "Email", "phone": "Phone", "title": "Title", "account": "Account", "actions": {"title": "Actions", "open": "Open menu", "edit": "Edit", "delete": "Delete"}}, "deleteDialog": {"title": "Delete Contact", "description": "Are you sure you want to delete this contact? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}, "success": {"created": "Contact created successfully", "updated": "Contact updated successfully", "deleted": "Contact deleted successfully"}, "errors": {"createFailed": "Failed to create contact", "updateFailed": "Failed to update contact", "deleteFailed": "Failed to delete contact"}}, "sales": {"title": "Salesforce Sales", "description": "Manage your Salesforce sales opportunities", "addLead": "Add Sales Opportunity", "noLeads": "No Sales Opportunities Found", "addFirst": "Add your first Salesforce sales opportunity to get started", "getStarted": "Get Started", "noSalesforceConnection": "Salesforce Not Connected", "connectSalesforceFirst": "Connect your Salesforce account to manage sales opportunities", "connectSalesforce": "Connect Salesforce", "add": {"title": "Add Salesforce Sales Opportunity", "success": "Sales opportunity added successfully", "error": "Failed to add sales opportunity"}, "edit": {"title": "Edit Salesforce Sales Opportunity", "success": "Sales opportunity updated successfully", "error": "Failed to update sales opportunity"}, "delete": {"title": "Delete Sales Opportunity", "description": "Are you sure you want to delete this sales opportunity? This action cannot be undone.", "confirm": "Delete Sales Opportunity", "cancel": "Cancel", "success": "Sales opportunity deleted successfully", "error": "Failed to delete sales opportunity"}, "form": {"about": "About", "firstName": "First Name", "lastName": "Last Name", "company": "Company", "title": "Title", "website": "Website", "description": "Description", "leadStatus": "Sales Status", "selectLeadStatus": "Select Sales Status", "getInTouch": "Get in Touch", "phone": "Phone", "phonePlaceholder": "Enter phone number", "email": "Email", "street": "Street", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "segment": "Segment", "employees": "Number of Employees", "annualRevenue": "Annual Revenue", "leadSource": "Sales Source", "selectLeadSource": "Select Sales Source", "industry": "Industry", "selectIndustry": "Select Industry"}, "table": {"name": "Name", "company": "Company", "email": "Email", "phone": "Phone", "status": "Status", "actions": {"open": "Open menu", "edit": "Edit", "delete": "Delete"}}, "cancel": "Cancel", "save": "Save Changes", "create": "Create Sales Opportunity"}}, "knowledgeBase": {"title": "Knowledge Base", "description": "Manage your organization's knowledge base for AI agents", "addUrl": "Add URL", "addFile": "Add File", "buttonCreateText": "Create Text", "createText": {"title": "Create Text Knowledge", "description": "Add text-based knowledge that your AI agents can use", "nameLabel": "Knowledge Name", "namePlaceholder": "Enter a name for this knowledge", "contentLabel": "Content", "contentPlaceholder": "Enter the text content that will be used by your AI agents", "success": "Text knowledge created successfully", "error": "Failed to create text knowledge"}, "cancel": "Cancel", "create": "Create", "creating": "Creating...", "search": "Search knowledge base...", "noKnowledgeBases": "No knowledge bases found", "noSearchResults": "No results found", "tryDifferentSearch": "Try a different search term", "addFirst": "Add your first knowledge base to get started", "getStarted": "Get Started", "name": "Name", "type": "Type", "createdBy": "Created By", "createdAt": "Created At", "actions": "Actions", "view": "View", "unknown": "Unknown", "types": {"file": "File", "url": "URL", "text": "Text"}, "createUrl": {"title": "Add Knowledge from URL", "description": "Import content from a website URL to your knowledge base", "urlLabel": "Website URL", "urlPlaceholder": "https://example.com/page", "success": "URL content added to knowledge base", "error": "Failed to add URL content"}, "openMenu": "Open menu", "delete": "Delete", "close": "Close", "viewDescription": "View the content of this {type} knowledge base", "sourceUrl": "Source URL", "extractedContent": "Extracted Content", "deleteTitle": "Delete Knowledge Base", "deleteDescription": "This action cannot be undone. This will permanently delete the knowledge base from your organization.", "deleteConfirm": "Are you sure you want to delete {name}?", "deleting": "Deleting...", "deleteSuccess": "Knowledge base deleted successfully", "deleteError": "Failed to delete knowledge base", "createFile": {"title": "Upload Document", "description": "Upload a document to your knowledge base", "nameLabel": "Knowledge Name", "namePlaceholder": "Enter a name for this knowledge", "fileLabel": "Document", "dragDrop": "Drag and drop a file here, or click to select", "fileTypes": "PDF, EPUB, DOCX, TXT, HTML files only", "maxSize": "Max file size: {size}", "remove": "Remove file", "success": "Document uploaded successfully", "error": "Failed to upload document", "fileTooLarge": "File is too large. Maximum size is {size}", "invalidFileType": "Invalid file type. Please upload a PDF, EPUB, DOCX, TXT, or HTML file"}, "fileDetails": "File Details", "fileName": "File Name", "fileType": "File Type", "fileSize": "File Size", "notAvailable": "Not available", "linkedAgentsWarning": "This knowledge base is linked to {count} {count, plural, one {agent} other {agents}}", "viewAgent": "View agent", "unlinkInstructions": "You should unlink this knowledge base from these agents before deleting it, or use force delete to remove it from all agents.", "forceDelete": "Force delete and remove from all agents", "forceDeleteConfirm": "Force delete", "selectKnowledgeBase": {"title": "Select Knowledge Base", "description": "Choose knowledge bases to add to your agent", "searchPlaceholder": "Search knowledge bases...", "noResults": "No knowledge bases found", "alreadyAdded": "Already added", "addSelected": "Add {count} {count, plural, one {document} other {documents}}"}}, "deals": {"title": "Deals", "description": "Manage your organization's deals", "addDeal": "Add Deal", "noDeals": "No Deals", "addFirst": "Add your first deal to get started", "getStarted": "Get Started", "noHubSpotConnection": "No HubSpot Connection", "connectHubSpotFirst": "You need to connect to HubSpot before you can manage deals", "connectHubSpot": "Connect HubSpot", "table": {"name": "Name", "amount": "Amount", "stage": "Stage", "pipeline": "Pipeline", "closeDate": "Close Date", "priority": "Priority", "actions": {"title": "Actions", "open": "Open menu", "edit": "Edit deal", "delete": "Delete deal"}}, "addDialog": {"title": "Add Deal", "description": "Add a new deal to your organization", "name": "Deal Name", "amount": "Amount", "stage": "Stage", "pipeline": "Pipeline", "closeDate": "Close Date", "priority": "Priority", "cancel": "Cancel", "submit": "Add Deal", "submitting": "Adding...", "nameRequired": "Deal name is required", "selectStage": "Select stage", "selectPipeline": "Select pipeline", "selectPriority": "Select priority", "amountPlaceholder": "Enter amount", "datePlaceholder": "Select close date"}, "editDialog": {"title": "Edit Deal", "description": "Update deal information", "cancel": "Cancel", "save": "Save Changes", "saving": "Saving...", "nameRequired": "Deal name is required", "selectStage": "Select stage", "selectPipeline": "Select pipeline", "selectPriority": "Select priority", "amountPlaceholder": "Enter amount", "datePlaceholder": "Select close date"}, "success": {"created": "Deal created successfully", "updated": "Deal updated successfully", "deleted": "Deal deleted successfully"}, "errors": {"createFailed": "Failed to create deal", "updateFailed": "Failed to update deal", "deleteFailed": "Failed to delete deal"}, "deleteDialog": {"title": "Delete Deal", "description": "Are you sure you want to delete this deal? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete", "deleting": "Deleting..."}, "stages": {"appointment_scheduled": "Appointment Scheduled", "qualified_to_buy": "Qualified to Buy", "presentation_scheduled": "Presentation Scheduled", "decision_maker_bought_in": "Decision Maker Bought-In", "contract_sent": "Contract Sent", "closed_won": "Closed Won", "closed_lost": "Closed Lost"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High"}, "pipelines": {"default": "De<PERSON>ult <PERSON>", "sales": "Sales Pipeline", "marketing": "Marketing Pipeline"}, "loading": "Loading deals...", "form": {"name": "Deal Name", "amount": "Amount", "stage": "Stage", "pipeline": "Pipeline", "closeDate": "Close Date", "priority": "Priority", "selectStage": "Select stage", "selectPipeline": "Select pipeline", "selectPriority": "Select priority", "cancel": "Cancel", "create": "Create", "save": "Save"}}}, "legal": {"terms": {"title": "Terms of Service", "description": "Please read these terms carefully before using our service.", "lastUpdated": "Last updated: January 1, 2024", "sections": {"acceptance": {"title": "1. Acceptance of Terms", "content": "By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement."}, "changes": {"title": "2. Changes to Terms", "content": "We reserve the right to modify these terms at any time. We will notify you of any changes by posting the new Terms of Service on this page."}, "privacy": {"title": "3. Privacy Policy", "content": "Your privacy is important to us. Please review our Privacy Policy to understand how we collect and use your information."}}}, "privacy": {"title": "Privacy Policy", "description": "This Privacy Policy describes how we collect, use, and handle your information.", "lastUpdated": "Last updated: January 1, 2024", "sections": {"collection": {"title": "1. Information Collection", "content": "We collect information that you provide directly to us when using our services."}, "use": {"title": "2. Use of Information", "content": "We use the information we collect to provide, maintain, and improve our services."}, "sharing": {"title": "3. Information Sharing", "content": "We do not share your personal information with third parties except as described in this policy."}}}}, "Navigation": {"dashboard": "Dashboard", "agents": {"title": "Agents", "all": "All Agents", "create": "Create Agent", "templates": "Templates"}, "phones": {"title": "Phone Management", "all": "All Numbers", "add": "Add Number", "history": "Call History"}, "settings": {"title": "Settings", "general": "General", "team": "Team"}, "history": "Call History", "contacts": "Contacts", "companies": "Companies", "accounts": "Accounts", "sales": "Sales", "knowledgeBase": "Knowledge Base", "deals": "Deals"}, "Settings": {"tabs": {"general": "General", "integrations": "Integrations"}, "team": {"title": "Team Members", "description": "Manage your organization's team members and their roles.", "inviteMember": "Invite Member", "pendingInvites": "Pending Invites", "pendingInvitesDescription": "These people have been invited but haven't accepted yet.", "table": {"member": "Member", "email": "Email", "role": "Role", "status": "Status", "expires": "Expires", "joinedAt": "Joined", "actions": {"open": "Open menu", "makeAdmin": "Make admin", "makeMember": "Make member", "remove": "Remove member", "cancelInvite": "Cancel Invite"}}, "roles": {"owner": "Owner", "admin": "Admin", "member": "Member"}, "status": {"active": "Active", "pending": "Pending", "inactive": "Inactive", "declined": "Declined"}, "invite": {"title": "Invite Team Member", "description": "Send an invitation to join your organization.", "fields": {"email": "Email address", "role": "Role"}, "cancel": "Cancel", "submit": "Send Invitation"}}, "crm": {"title": "CRM Settings", "hubspot": {"title": "HubSpot Integration", "description": "Connect and manage your HubSpot CRM integration", "connected": "Connected to HubSpot", "connect": "Connect HubSpot", "disconnect": "Disconnect", "sync": "Sync Data", "syncSuccess": "Successfully synced CRM data", "syncError": "Failed to sync CRM data", "syncedContacts": "Synced Contacts", "syncedCompanies": "Synced Companies", "syncedDeals": "Synced Deals", "connectSuccess": "Successfully connected to HubSpot", "connectError": "Failed to connect to HubSpot", "disconnectSuccess": "Successfully disconnected from HubSpot", "disconnectError": "Failed to disconnect from HubSpot"}, "salesforce": {"title": "Salesforce Integration", "description": "Connect and manage your Salesforce CRM integration", "connected": "Connected to Salesforce", "connect": "Connect Salesforce", "disconnect": "Disconnect", "sync": "Sync Data", "syncSuccess": "Successfully synced Salesforce data", "syncError": "Failed to sync Salesforce data", "syncedContacts": "Synced Contacts", "syncedLeads": "Synced Leads", "syncedOpportunities": "Synced Opportunities", "connectSuccess": "Successfully connected to Salesforce", "connectError": "Failed to connect to Salesforce"}, "errors": {"connectionFailed": "Failed to connect to CRM", "disconnectionFailed": "Failed to disconnect CRM", "noCode": "No authorization code received", "noOrganization": "No organization selected"}, "success": {"connected": "{type} connected successfully", "disconnected": "{type} disconnected successfully"}, "connecting": "Connecting to CRM...", "connectingDescription": "Please wait while we complete the connection.", "types": {"hubspot": "HubSpot", "salesforce": "Salesforce"}}, "organization": {"title": "Organization Settings", "description": "Manage your organization's basic settings", "nameLabel": "Organization Name", "save": "Save Changes", "updateSuccess": "Organization updated successfully", "updateError": "Failed to update organization", "permissionDenied": "You don't have permission to edit organization settings"}}, "invite": {"valid": {"title": "Organization Invitation", "description": "You've been invited to join {organization} as a {role}"}, "invalid": {"title": "Invalid Invitation", "description": "This invitation link is no longer valid or has expired", "returnHome": "Return to Home"}, "details": {"email": "Email", "organization": "Organization"}, "actions": {"accept": "Accept Invitation", "decline": "Decline"}, "roles": {"owner": "Owner", "admin": "Admin", "member": "Member"}, "wrongEmail": {"title": "Wrong Account", "description": "This invitation was sent to {inviteEmail}, but you're signed in with {currentEmail}. Please sign in with the correct account to accept this invitation.", "switchAccount": "Sign in with different account"}}, "profile": {"avatar": "Avatar", "profileInformation": "Profile Information", "profileDescription": "Update your profile information and manage your account", "name": "Name", "email": "Email", "updateProfile": "Update Profile", "nameRequired": "Name is required", "invalidEmail": "Invalid email address", "authentication": "Authentication", "authenticationDescription": "Manage your authentication method", "emailAuthentication": "You are using email and password to authenticate", "socialAuthentication": "You are authenticated via {provider}", "provider": {"email": "Email", "google": "Google", "azure": "Microsoft"}, "changePassword": "Change Password", "changePasswordDescription": "Ensure your account is using a strong password for security", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordMinLength": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "updatePassword": "Update Password"}, "common": {"file_size_error": "File size exceeds the limit of {size}MB", "change": "Change", "upload": "Upload"}, "Call": {"ready": "Ready to call", "connecting": "Connecting...", "connected": "Connected", "listening": "Agent is listening", "speaking": "Agent is speaking", "ending": "Ending call...", "startCall": "Start Call", "endCall": "End Call"}, "User": {"phoneUpdate": {"title": "Update Your Phone Number", "description": "Please provide your phone number to enhance account security and enable important notifications.", "phoneLabel": "Phone Number", "phonePlaceholder": "Enter your phone number", "update": "Update", "updating": "Updating...", "skip": "Skip for now", "success": "Phone number updated successfully", "error": "Failed to update phone number"}}}