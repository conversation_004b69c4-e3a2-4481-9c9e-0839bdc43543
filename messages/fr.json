{"Header": {"title": "Foundation", "about": "À propos", "docs": "Documentation", "blog": "Blog", "login": "Connexion", "logout": "Déconnexion", "organizations": "Organisations", "profile": "Profil", "settings": "Paramètres", "billing": "Facturation", "notifications": "Notifications", "switchLanguage": "Changer de langue", "legal": {"privacy": "Confidentialité", "terms": "Conditions"}}, "Footer": {"copyright": "Droits d'auteur", "rights": "Tous droits réservés", "privacy": "Politique de confidentialité", "terms": "Conditions d'utilisation", "links": {"twitter": "Twitter", "github": "GitHub", "privacy": "Confidentialité", "terms": "Conditions"}}, "Home": {"hero": {"title": "Créez de Meilleurs Logiciels Plus Rapidement", "description": "Une base moderne pour construire des produits. Puissante, extensible et conçue pour l'innovation.", "primaryCTA": "Commencer", "secondaryCTA": "En savoir plus"}, "features": {"feature1": {"title": "Ultra Rapide", "description": "Conçu en pensant à la performance, garantissant une efficacité maximale de vos applications."}, "feature2": {"title": "Sécurisé par Défaut", "description": "Fonctionnalités de sécurité de niveau entreprise intégrées, pas ajoutées après coup."}, "feature3": {"title": "Expérience Développeur", "description": "APIs intuitives et documentation complète pour une expérience de développement fluide."}}, "cta": {"title": "Prêt à commencer ?", "description": "Rejoignez des milliers de développeurs qui créent de meilleurs logiciels avec Foundation.", "primaryCTA": "<PERSON><PERSON><PERSON>", "secondaryCTA": "<PERSON><PERSON> les Ventes"}}, "auth": {"email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "name": "Nom", "login": {"title": "Bon retour", "description": "Connectez-vous à votre compte", "submit": "Se connecter", "success": "Connexion réussie", "error": "Échec de la connexion", "forgotPassword": "Mot de passe oublié ?", "noAccount": "Vous n'avez pas de compte ?", "signupLink": "S'inscrire", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "passwordMinLength": "Le mot de passe doit contenir au moins 6 caractères"}, "signup": {"title": "<PERSON><PERSON><PERSON> un compte", "description": "Entrez vos informations pour commencer", "submit": "S'inscrire", "success": "Vérifiez votre email pour le lien de confirmation", "error": "Échec de la création du compte", "haveAccount": "Vous avez déjà un compte ?", "loginLink": "Se connecter", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "passwordMinLength": "Le mot de passe doit contenir au moins 6 caractères", "passwordMismatch": "Les mots de passe ne correspondent pas", "nameRequired": "Le nom est requis"}, "social": {"or": "ou continuer avec", "google": "Continuer avec Google", "microsoft": "Continuer avec Microsoft", "error": "Échec de la connexion avec le fournisseur social"}, "verify": {"title": "Vérifiez votre email", "description": "Nous vous avons envoyé un code de vérification. Veuillez le saisir ci-dessous.", "emailSent": "Nous avons envoyé un code de vérification à {email}", "codeLabel": "Code de vérification", "submit": "Vérifier l'email", "verifying": "Vérification en cours...", "resend": "Renvoyer le code", "resending": "Renvoi en cours...", "resendCountdown": "Ren<PERSON>er le code dans {seconds}s", "error": {"title": "Erreur de vérification", "description": "Aucune adresse email fournie pour la vérification. Veuillez réessayer de vous inscrire."}, "success": "Email vérifié avec succès !", "resendSuccess": "L'email de vérification a été renvoyé"}, "errors": {"InvalidCredentials": "Email ou mot de passe invalide. Veuillez réessayer.", "EmailNotConfirmed": "Veuillez vérifier votre email avant de vous connecter.", "InvalidEmail": "Veu<PERSON>z entrer une adresse email valide.", "WeakPassword": "Le mot de passe doit contenir au moins 6 caractères.", "EmailInUse": "Cet email est déjà enregistré. Essayez de vous connecter.", "OtpExpired": "Le code de vérification a expiré. Veuillez en demander un nouveau.", "Default": "Une erreur s'est produite. Veuillez réessayer."}, "signout": {"success": "Déconnexion réussie"}, "forgotPassword": {"title": "Mot de passe oublié", "description": "Entrez votre adresse email et nous vous enverrons un lien pour réinitialiser votre mot de passe.", "submit": "Envoyer l'email de réinitialisation", "requesting": "Envoi en cours...", "backToLogin": "Retour à la connexion", "success": {"title": "Vérifiez votre email", "description": "Instructions de réinitialisation du mot de passe envoyées", "message": "Si vous vous êtes inscrit avec votre email et mot de passe, vous recevrez un email de réinitialisation."}, "invalidEmail": "<PERSON><PERSON><PERSON> email invalide"}}, "Organizations": {"title": "Organisations", "loading": "Chargement des organisations...", "createNew": "Créer une organisation", "noOrganizations": "Pas encore d'organisations", "createFirst": "Créez votre première organisation pour commencer", "getStarted": "Commencer", "members": "{count, plural, =1 {# membre} other {# membres}}", "types": {"personal": "Personnel", "education": "Éducation", "startup": "Startup", "agency": "Agence", "company": "Entreprise"}, "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrateur", "member": "Membre"}, "create": {"title": "Créer une organisation", "formTitle": "Détails de l'organisation", "formDescription": "Remplissez les détails ci-dessous pour créer votre organisation", "submitting": "Création en cours...", "submit": "Créer l'organisation", "fields": {"name": {"label": "Nom de l'organisation", "description": "Entrez un nom unique pour votre organisation"}, "description": {"label": "Description", "description": "Décrivez brièvement votre organisation (optionnel)"}, "type": {"label": "Type d'organisation", "description": "Sélectionnez le type qui décrit le mieux votre organisation", "placeholder": "Sélectionnez le type d'organisation"}, "size": {"label": "Taille de l'entreprise", "description": "Nombre de personnes dans votre organisation", "placeholder": "Sélectionnez la taille de l'entreprise"}}, "success": "Organisation créée avec succès", "error": "Échec de la création de l'organisation"}, "update": {"success": "Organisation mise à jour avec succès", "error": "Échec de la mise à jour de l'organisation"}, "delete": {"success": "Organisation supprimée avec succès", "error": "Échec de la suppression de l'organisation", "confirm": "Êtes-vous sûr de vouloir supprimer cette organisation ?"}, "navigation": {"dashboard": "Tableau de bord", "members": "Me<PERSON><PERSON>", "settings": "Paramètres"}, "dashboard": {"greeting": "<PERSON><PERSON><PERSON>, {name}", "subtitle": "Voici ce qui se passe avec vos agents", "filters": {"allAgents": "Tous les agents", "today": "<PERSON><PERSON><PERSON>'hui", "lastWeek": "<PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON>", "lastYear": "<PERSON><PERSON>"}, "stats": {"calls": "Nombre d'appels", "numberOfCalls": "Total des appels", "duration": "<PERSON><PERSON><PERSON> moyenne", "averageDuration": "<PERSON><PERSON>e moyenne d'appel", "totalCost": "Coût total", "credits": "crédits", "averageCost": "<PERSON><PERSON><PERSON> moyen", "creditsPerCall": "crédits/appel"}, "metrics": {"title": "Analytique", "noMetrics": "Pas de métriques", "noMetricsDescription": "Il n'y a pas de métriques pour la période spécifiée."}}, "switchOrganization": "Changer d'organisation", "breadcrumb": {"organizations": "Organisations", "loading": "Chargement...", "agents": "Agents", "settings": "Paramètres", "dashboard": "Tableau de bord", "team": "Équipe", "general": "Général"}, "platform": "Plateforme", "integrations": {"title": "Intégrations", "description": "Connectez votre organisation avec des services externes", "crm": {"title": "Intégration CRM", "description": "Connectez votre CRM pour synchroniser les contacts et les opportunités", "hubspot": {"title": "HubSpot", "description": "Connectez votre compte HubSpot pour synchroniser les contacts, les entreprises et les opportunités", "connect": "Connecter HubSpot", "disconnect": "Déconnecter HubSpot", "connected": "Connecté à HubSpot", "connecting": "Connexion à HubSpot...", "waitMessage": "Veuillez patienter pendant que nous finalisons la connexion.", "errors": {"noCode": "Aucun code d'autorisation reçu de HubSpot", "connectionFailed": "Échec de la connexion à HubSpot", "generic": "Une erreur s'est produite lors de la connexion à HubSpot"}, "success": {"connected": "Connexion à HubSpot réussie", "disconnected": "Déconnexion de HubSpot réussie"}, "portal": {"id": "ID du Portail", "type": "Type de Compte", "timezone": "<PERSON><PERSON>", "currency": "<PERSON><PERSON>"}}}}, "contacts": {"title": "Contacts", "description": "<PERSON><PERSON>rez les contacts de votre organisation", "addContact": "Ajouter un contact", "noContacts": "Aucun contact", "addFirst": "Ajoutez votre premier contact pour commencer", "getStarted": "Commencer", "noHubSpotConnection": "Pas de connexion HubSpot", "connectHubSpotFirst": "Vous devez vous connecter à HubSpot avant de pouvoir gérer les contacts", "connectHubSpot": "Connecter HubSpot", "table": {"name": "Nom", "email": "Email", "phone": "Téléphone", "jobTitle": "Titre du poste", "actions": {"title": "Actions", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "edit": "Modifier le contact", "delete": "Supprimer le contact"}}, "addDialog": {"title": "Ajouter un contact", "description": "Ajouter un nouveau contact à votre organisation", "firstName": "Prénom", "lastName": "Nom", "email": "Email", "phone": "Téléphone", "jobTitle": "Titre du poste", "cancel": "Annuler", "submit": "Ajouter un contact", "lifecycleStage": "Étape du cycle de vie", "leadStatus": "Statut du prospect"}, "editContact": "Modifier le contact", "editContactDescription": "Mettre à jour les informations du contact", "firstName": "Prénom", "lastName": "Nom", "email": "Email", "phone": "Téléphone", "jobTitle": "Titre du poste", "cancel": "Annuler", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteDialog": {"title": "Supprimer le contact", "description": "Êtes-vous sûr de vouloir supprimer ce contact ? Cette action ne peut pas être annulée.", "cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression en cours..."}, "success": {"created": "Contact créé avec succès", "updated": "Contact mis à jour avec succès", "deleted": "Contact supprimé avec succès"}, "errors": {"createFailed": "Échec de la création du contact", "updateFailed": "Échec de la mise à jour du contact", "deleteFailed": "Échec de la suppression du contact"}}, "salesforce": {"accounts": {"title": "Comptes Salesforce", "description": "Gérer vos comptes Salesforce", "addAccount": "Ajouter un Compte", "noAccounts": "Aucun compte trouvé", "addFirst": "Ajoutez votre premier compte Salesforce pour commencer", "getStarted": "Commencer", "noSalesforceConnection": "Salesforce Non Connecté", "connectSalesforceFirst": "Connectez votre compte Salesforce pour gérer les comptes", "connectSalesforce": "Connecter Salesforce", "table": {"name": "Nom", "industry": "Industrie", "type": "Type", "website": "Site Web", "edit": "Modifier"}, "form": {"name": "Nom du Compte", "industry": "Industrie", "type": "Type", "website": "Site Web", "phone": "Téléphone", "billingStreet": "Rue de Facturation", "billingCity": "Ville de Facturation", "billingState": "État/Province de Facturation", "billingPostalCode": "Code Postal de Facturation", "billingCountry": "Pays de Facturation", "description": "Description"}, "add": {"title": "Ajouter un Compte Salesforce", "success": "Compte a<PERSON> avec succès", "error": "Échec de l'ajout du compte"}, "edit": {"title": "Modifier le Compte Salesforce", "success": "Compte mis à jour avec succès", "error": "Échec de la mise à jour du compte"}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON> le Compte", "description": "Êtes-vous sûr de vouloir supprimer ce compte? Cette action ne peut pas être annulée.", "confirm": "<PERSON><PERSON><PERSON><PERSON> le Compte", "cancel": "Annuler", "success": "Compte supprimé avec succès", "error": "Échec de la suppression du compte"}}, "contacts": {"title": "Contacts Salesforce", "description": "Gérer vos contacts Salesforce", "addContact": "Ajouter un contact", "editContact": "Modifier le contact", "editContactDescription": "Mettre à jour les informations du contact", "noContacts": "Aucun contact trouvé", "addFirst": "Ajoutez votre premier contact Salesforce pour commencer", "getStarted": "Commencer", "noSalesforceConnection": "Salesforce Non Connecté", "connectSalesforceFirst": "Connectez votre compte Salesforce pour gérer les contacts", "connectSalesforce": "Connecter Salesforce", "add": "Ajouter un contact", "save": "Enregistrer les modifications", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "form": {"basicInfo": "Informations de base", "firstName": "Prénom", "lastName": "Nom", "email": "Email", "phone": "Téléphone", "phonePlaceholder": "Entrez un numéro de téléphone", "title": "Titre", "account": "<PERSON><PERSON><PERSON>", "selectAccount": "Sélectionnez un compte", "mailingAddress": "Adresse postale", "mailingStreet": "Rue", "mailingCity": "Ville", "mailingState": "État/Province", "mailingPostalCode": "Code postal", "mailingCountry": "Pays", "additionalInfo": "Informations supplémentaires", "description": "Description"}, "table": {"name": "Nom", "email": "Email", "phone": "Téléphone", "title": "Titre", "account": "<PERSON><PERSON><PERSON>", "actions": {"title": "Actions", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "edit": "Modifier le contact", "delete": "Supprimer le contact"}}, "deleteDialog": {"title": "Supprimer le contact", "description": "Êtes-vous sûr de vouloir supprimer ce contact ? Cette action ne peut pas être annulée.", "cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression en cours..."}, "success": {"created": "Contact créé avec succès", "updated": "Contact mis à jour avec succès", "deleted": "Contact supprimé avec succès"}, "errors": {"createFailed": "Échec de la création du contact", "updateFailed": "Échec de la mise à jour du contact", "deleteFailed": "Échec de la suppression du contact"}}, "sales": {"title": "Prospects Salesforce", "description": "Gérer vos prospects Salesforce", "addLead": "Ajouter un prospect", "noLeads": "Aucun prospect trouvé", "addFirst": "Ajoutez votre premier prospect Salesforce pour commencer", "getStarted": "Commencer", "noSalesforceConnection": "Salesforce Non Connecté", "connectSalesforceFirst": "Connectez votre compte Salesforce pour gérer les prospects", "connectSalesforce": "Connecter Salesforce", "add": {"title": "Ajouter un prospect Salesforce", "success": "Prospect ajouté avec succès", "error": "Échec de l'ajout du prospect"}, "edit": {"title": "Modifier le prospect Salesforce", "success": "Prospect mis à jour avec succès", "error": "Échec de la mise à jour du prospect"}, "delete": {"title": "Supprimer le prospect", "description": "Êtes-vous sûr de vouloir supprimer ce prospect ? Cette action ne peut pas être annulée.", "confirm": "Supprimer le prospect", "cancel": "Annuler", "success": "Prospect supprimé avec succès", "error": "Échec de la suppression du prospect"}, "form": {"about": "À propos", "firstName": "Prénom", "lastName": "Nom", "company": "Entreprise", "title": "Titre", "website": "Site Web", "description": "Description", "leadStatus": "Statut du prospect", "selectLeadStatus": "Sélectionner le statut du prospect", "getInTouch": "Contact", "phone": "Téléphone", "phonePlaceholder": "Entrez un numéro de téléphone", "email": "Email", "street": "Rue", "city": "Ville", "state": "État/Province", "postalCode": "Code postal", "country": "Pays", "segment": "Segment", "employees": "Nombre d'employés", "annualRevenue": "<PERSON><PERSON><PERSON> annuel", "leadSource": "Source du prospect", "selectLeadSource": "Sélectionner la source du prospect", "industry": "Industrie", "selectIndustry": "Sélectionner l'industrie"}, "table": {"name": "Nom", "company": "Entreprise", "email": "Email", "phone": "Téléphone", "status": "Statut", "actions": {"open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "edit": "Modifier le prospect", "delete": "Supprimer le prospect"}}, "cancel": "Annuler", "save": "Enregistrer les modifications", "create": "<PERSON><PERSON><PERSON> un prospect"}}, "agents": {"form": {"knowledge": {"indexing": "Indexation...", "indexed": "Indexé", "indexFailed": "Échec de l'indexation"}}}, "knowledgeBase": {"selectKnowledgeBase": {"indexingInfo": "Lorsque vous ajoutez une base de connaissances, elle sera indexée pour RAG. Ce processus peut prendre quelques minutes selon la taille du document."}}, "deals": {"title": "Affaires", "description": "<PERSON><PERSON>rez les affaires de votre organisation", "addDeal": "Ajouter une affaire", "noDeals": "Aucune affaire", "addFirst": "Ajou<PERSON>z votre première affaire pour commencer", "getStarted": "Commencer", "noHubSpotConnection": "Pas de connexion HubSpot", "connectHubSpotFirst": "Vous devez vous connecter à HubSpot avant de pouvoir gérer les affaires", "connectHubSpot": "Connecter HubSpot", "table": {"name": "Nom", "amount": "<PERSON><PERSON>", "stage": "Étape", "pipeline": "Pipeline", "closeDate": "Date de clôture", "priority": "Priorité", "actions": {"title": "Actions", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "edit": "Modifier l'affaire", "delete": "Supprimer l'affaire"}}, "addDialog": {"title": "Ajouter une affaire", "description": "Ajouter une nouvelle affaire à votre organisation", "name": "Nom de l'affaire", "amount": "<PERSON><PERSON>", "stage": "Étape", "pipeline": "Pipeline", "closeDate": "Date de clôture", "priority": "Priorité", "cancel": "Annuler", "submit": "Ajouter l'affaire", "submitting": "Ajout en cours...", "nameRequired": "Le nom de l'affaire est requis", "selectStage": "Sélectionner une étape", "selectPipeline": "Sélectionner un pipeline", "selectPriority": "Sélectionner une priorité", "amountPlaceholder": "<PERSON><PERSON><PERSON> le montant", "datePlaceholder": "Sélectionner la date de clôture"}, "editDialog": {"title": "Modifier l'affaire", "description": "Mettre à jour les informations de l'affaire", "cancel": "Annuler", "save": "Enregistrer les modifications", "saving": "Enregistrement en cours...", "nameRequired": "Le nom de l'affaire est requis", "selectStage": "Sélectionner une étape", "selectPipeline": "Sélectionner un pipeline", "selectPriority": "Sélectionner une priorité", "amountPlaceholder": "<PERSON><PERSON><PERSON> le montant", "datePlaceholder": "Sélectionner la date de clôture"}, "success": {"created": "Affaire c<PERSON><PERSON> avec succès", "updated": "Affaire mise à jour avec succès", "deleted": "Affaire supprimée avec succès"}, "errors": {"createFailed": "Échec de la création de l'affaire", "updateFailed": "Échec de la mise à jour de l'affaire", "deleteFailed": "Échec de la suppression de l'affaire"}, "deleteDialog": {"title": "Supprimer l'affaire", "description": "Êtes-vous sûr de vouloir supprimer cette affaire ? Cette action ne peut pas être annulée.", "cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression en cours..."}, "stages": {"appointment_scheduled": "<PERSON><PERSON><PERSON>vous planifié", "qualified_to_buy": "Qualifié pour acheter", "presentation_scheduled": "Présentation planifiée", "decision_maker_bought_in": "<PERSON><PERSON><PERSON><PERSON> conva<PERSON><PERSON>", "contract_sent": "Contrat envoy<PERSON>", "closed_won": "<PERSON><PERSON> gag<PERSON>", "closed_lost": "Affaire perdue"}, "priorities": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Élevée"}, "pipelines": {"default": "Pipeline par défaut", "sales": "<PERSON><PERSON><PERSON> de ventes", "marketing": "Pipeline marketing"}, "loading": "Chargement des affaires..."}}, "Settings": {"tabs": {"general": "Général", "integrations": "Intégrations"}, "team": {"title": "Membres de l'équipe", "description": "<PERSON><PERSON><PERSON> les membres de votre organisation et leurs rôles.", "inviteMember": "Inviter un membre", "pendingInvites": "Invitations en attente", "pendingInvitesDescription": "Ces personnes ont été invitées mais n'ont pas encore accepté.", "table": {"member": "Membre", "email": "Email", "role": "R<PERSON><PERSON>", "status": "Statut", "expires": "Expire", "joinedAt": "Rejoint le", "actions": {"open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "makeAdmin": "Promouvoir administrateur", "makeMember": "Rétrograder membre", "remove": "<PERSON><PERSON><PERSON> le membre", "cancelInvite": "Annuler l'invitation"}}, "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrateur", "member": "Membre"}, "status": {"active": "Actif", "pending": "En attente", "inactive": "Inactif", "declined": "<PERSON><PERSON><PERSON><PERSON>"}, "invite": {"title": "Inviter un membre", "description": "Envoyez une invitation à rejoindre votre organisation.", "fields": {"email": "<PERSON><PERSON><PERSON> email", "role": "R<PERSON><PERSON>"}, "cancel": "Annuler", "submit": "Envoyer l'invitation"}}, "organization": {"title": "Paramètres de l'Organisation", "description": "G<PERSON>rer les paramètres et préférences de votre organisation", "nameLabel": "Nom de l'Organisation", "save": "Enregistrer les Modifications", "updateSuccess": "Organisation mise à jour avec succès", "updateError": "Échec de la mise à jour de l'organisation", "permissionDenied": "Vous n'avez pas la permission de modifier les paramètres de l'organisation"}, "crm": {"title": "Paramètres CRM", "hubspot": {"title": "Intégration HubSpot", "description": "Connectez votre compte HubSpot pour gérer les contacts et les entreprises", "connected": "Connecté à HubSpot", "connect": "Connecter HubSpot", "disconnect": "Déconnecter", "sync": "Synchroniser les Données", "syncSuccess": "Données CRM synchronisées avec succès", "syncError": "Échec de la synchronisation des données CRM", "syncedContacts": "Contacts Synchroni<PERSON>", "syncedCompanies": "Entreprises Synchronisées", "connectSuccess": "Connexion à HubSpot réussie", "connectError": "Échec de la connexion à HubSpot"}, "salesforce": {"title": "Intégration Salesforce", "description": "Connectez votre compte Salesforce pour gérer les contacts, les prospects et les opportunités", "connected": "Connecté à Salesforce", "connect": "Connecter Salesforce", "disconnect": "Déconnecter", "sync": "Synchroniser les Données", "syncSuccess": "Données Salesforce synchronisées avec succès", "syncError": "Échec de la synchronisation des données Salesforce", "syncedContacts": "Contacts Synchroni<PERSON>", "syncedLeads": "Prospects Synchronisés", "syncedOpportunities": "Opportunités Synchronisées", "connectSuccess": "Connexion à Salesforce réussie", "connectError": "Échec de la connexion à Salesforce"}, "errors": {"connectionFailed": "Échec de la connexion au CRM", "disconnectionFailed": "Échec de la déconnexion du CRM", "noCode": "Aucun code d'autorisation reçu", "noOrganization": "Aucune organisation sélectionnée"}, "success": {"connected": "{type} connecté avec succès", "disconnected": "{type} déconnecté avec succès"}, "connecting": "Connexion en cours...", "connectingDescription": "Veuillez patienter pendant que nous finalisons la connexion.", "types": {"hubspot": "HubSpot", "salesforce": "Salesforce"}}}, "invite": {"valid": {"title": "Invitation à l'organisation", "description": "V<PERSON> avez été invité à rejoindre {organization} en tant que {role}"}, "invalid": {"title": "Invitation invalide", "description": "Ce lien d'invitation n'est plus valide ou a expiré", "returnHome": "Retour à l'accueil"}, "details": {"email": "Email", "organization": "Organisation"}, "actions": {"accept": "Accepter l'invitation", "decline": "Décliner"}, "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrateur", "member": "Membre"}, "wrongEmail": {"title": "<PERSON><PERSON><PERSON><PERSON> compte", "description": "Cette invitation a été envoyée à {inviteEmail}, mais vous êtes connecté avec {currentEmail}. Veuillez vous connecter avec le bon compte pour accepter cette invitation.", "switchAccount": "Se connecter avec un autre compte"}}, "Navigation": {"dashboard": "Tableau de bord", "agents": {"title": "Agents", "all": "Tous les agents", "create": "<PERSON><PERSON><PERSON> un agent", "templates": "<PERSON><PERSON><PERSON><PERSON>"}, "phones": {"title": "Gestion des téléphones", "all": "Tous les numéros", "add": "Ajouter un numéro", "history": "Historique des appels"}, "settings": {"title": "Paramètres", "general": "Général", "team": "Équipe"}}, "privacy": {"title": "Politique de confidentialité", "description": "Cette politique de confidentialité décrit comment nous collectons, utilisons et gérons vos informations.", "lastUpdated": "Dernière mise à jour : 1er janvier 2024", "sections": {"collection": {"title": "1. <PERSON><PERSON><PERSON> d'informations", "content": "Nous collectons les informations que vous nous fournissez directement lors de l'utilisation de nos services."}, "use": {"title": "2. Utilisation des informations", "content": "Nous utilisons les informations collectées pour fournir, maintenir et améliorer nos services."}, "sharing": {"title": "3. Partage d'informations", "content": "Nous ne partageons pas vos informations personnelles avec des tiers, sauf comme décrit dans cette politique."}}}, "Call": {"ready": "<PERSON><PERSON><PERSON><PERSON> à appeler", "connecting": "Connexion en cours...", "connected": "Connecté", "listening": "L'agent écoute", "speaking": "L'agent parle", "ending": "Fin de l'appel...", "startCall": "<PERSON><PERSON><PERSON><PERSON> l'appel", "endCall": "<PERSON><PERSON>iner l'appel"}, "User": {"phoneUpdate": {"title": "Mettre à jour votre numéro de téléphone", "description": "Veuillez fournir votre numéro de téléphone pour améliorer la sécurité du compte et activer les notifications importantes.", "phoneLabel": "Numéro de téléphone", "phonePlaceholder": "Entrez votre numéro de téléphone", "update": "Mettre à jour", "updating": "Mise à jour...", "skip": "Ignorer pour l'instant", "success": "Numéro de téléphone mis à jour avec succès", "error": "Échec de la mise à jour du numéro de téléphone"}}}