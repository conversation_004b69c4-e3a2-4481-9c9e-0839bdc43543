{"lockfile_version": "1", "packages": {"bun@1.2.8": {"last_modified": "2025-04-03T14:08:01Z", "resolved": "github:NixOS/nixpkgs/2bfc080955153be0be56724be6fa5477b4eefabb#bun", "source": "devbox-search", "version": "1.2.8", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/i51l405j3yjql3yr7b65aiwmnwi1marl-bun-1.2.8", "default": true}], "store_path": "/nix/store/i51l405j3yjql3yr7b65aiwmnwi1marl-bun-1.2.8"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/3d7pm5sw7dbkzj934606ldqaj76k3yhx-bun-1.2.8", "default": true}], "store_path": "/nix/store/3d7pm5sw7dbkzj934606ldqaj76k3yhx-bun-1.2.8"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/vc5q2nsdghhg9r8ycskrfq1m7pcrg5fm-bun-1.2.8", "default": true}], "store_path": "/nix/store/vc5q2nsdghhg9r8ycskrfq1m7pcrg5fm-bun-1.2.8"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/qv4zmrpmx9mkdx2nl6sdn0y97ls1c8jz-bun-1.2.8", "default": true}], "store_path": "/nix/store/qv4zmrpmx9mkdx2nl6sdn0y97ls1c8jz-bun-1.2.8"}}}, "deno@2.2.8": {"last_modified": "2025-04-07T13:23:10Z", "resolved": "github:NixOS/nixpkgs/b0b4b5f8f621bfe213b8b21694bab52ecfcbf30b#deno", "source": "devbox-search", "version": "2.2.8", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/n8vmn0gyla28z3ln25kvcfpkw9wcmbpz-deno-2.2.8", "default": true}], "store_path": "/nix/store/n8vmn0gyla28z3ln25kvcfpkw9wcmbpz-deno-2.2.8"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/iqk08qxxsm1vvyczni9rd44hrxm60m88-deno-2.2.8", "default": true}], "store_path": "/nix/store/iqk08qxxsm1vvyczni9rd44hrxm60m88-deno-2.2.8"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/zhy9mq8kcwin8n8fc5brfq8ax1nkbm0b-deno-2.2.8", "default": true}], "store_path": "/nix/store/zhy9mq8kcwin8n8fc5brfq8ax1nkbm0b-deno-2.2.8"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/jvp2j7xmnj3fa1b1cjf4drqaiwwnq94a-deno-2.2.8", "default": true}], "store_path": "/nix/store/jvp2j7xmnj3fa1b1cjf4drqaiwwnq94a-deno-2.2.8"}}}, "github:NixOS/nixpkgs/nixpkgs-unstable": {"last_modified": "2025-04-13T09:22:33Z", "resolved": "github:NixOS/nixpkgs/18dd725c29603f582cf1900e0d25f9f1063dbf11?lastModified=1744536153&narHash=sha256-awS2zRgF4uTwrOKwwiJcByDzDOdo3Q1rPZbiHQg%2FN38%3D"}, "nodejs@23.11.0": {"last_modified": "2025-04-02T13:00:31Z", "plugin_version": "0.0.2", "resolved": "github:NixOS/nixpkgs/384d1adab77a8d49172a3acc1d5c2f26d013a69a#nodejs_23", "source": "devbox-search", "version": "23.11.0", "systems": {"aarch64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/7z52lrjdf95sxdv5gmf1lkd6jkmbrk56-nodejs-23.11.0", "default": true}, {"name": "libv8", "path": "/nix/store/0ksknzxczr0kzbz6swnw9ib84hgi77s8-nodejs-23.11.0-libv8"}], "store_path": "/nix/store/7z52lrjdf95sxdv5gmf1lkd6jkmbrk56-nodejs-23.11.0"}, "aarch64-linux": {"outputs": [{"name": "out", "path": "/nix/store/yjnnd52kllrr7ydnz7ixw950fbgv014q-nodejs-23.11.0", "default": true}, {"name": "libv8", "path": "/nix/store/4i48prhfrkr2wbs06vs5g2aaicncpsfx-nodejs-23.11.0-libv8"}], "store_path": "/nix/store/yjnnd52kllrr7ydnz7ixw950fbgv014q-nodejs-23.11.0"}, "x86_64-darwin": {"outputs": [{"name": "out", "path": "/nix/store/m2wfafc3wszhrl6ymyjjjw2b20a0xbx3-nodejs-23.11.0", "default": true}, {"name": "libv8", "path": "/nix/store/5f0jci5bwflnxp2ysfisv5xwxis7wa45-nodejs-23.11.0-libv8"}], "store_path": "/nix/store/m2wfafc3wszhrl6ymyjjjw2b20a0xbx3-nodejs-23.11.0"}, "x86_64-linux": {"outputs": [{"name": "out", "path": "/nix/store/npy9r2i6v4r277y91vnf40l47gwpsdz4-nodejs-23.11.0", "default": true}, {"name": "libv8", "path": "/nix/store/0shcl7m044cdglpsf9nd61rgfndhx4qm-nodejs-23.11.0-libv8"}], "store_path": "/nix/store/npy9r2i6v4r277y91vnf40l47gwpsdz4-nodejs-23.11.0"}}}}}