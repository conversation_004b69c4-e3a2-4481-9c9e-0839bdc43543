import dayjs from 'dayjs'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// Extend dayjs with plugins
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(localizedFormat)
dayjs.extend(relativeTime)

export type TimeOfDay = 'morning' | 'afternoon' | 'evening' | 'night'

/**
 * Returns the appropriate greeting based on the current time
 * Morning: 5:00 AM - 11:59 AM
 * Afternoon: 12:00 PM - 4:59 PM
 * Evening: 5:00 PM - 9:59 PM
 * Night: 10:00 PM - 4:59 AM
 */
export const getTimeOfDay = (): TimeOfDay => {
  const hour = dayjs().hour()

  if (hour >= 5 && hour < 12) {
    return 'morning'
  } else if (hour >= 12 && hour < 17) {
    return 'afternoon'
  } else if (hour >= 17 && hour < 22) {
    return 'evening'
  } else {
    return 'night'
  }
}

/**
 * Formats a date using the specified format
 * @param date - The date to format
 * @param format - The format to use (defaults to 'L')
 * @returns The formatted date string
 */
export const formatDate = (date: Date | string, format = 'L'): string => {
  return dayjs(date).format(format)
}

/**
 * Returns true if the date is today
 * @param date - The date to check
 */
export const isToday = (date: Date | string): boolean => {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * Returns true if the date is in the past
 * @param date - The date to check
 */
export const isPast = (date: Date | string): boolean => {
  return dayjs(date).isBefore(dayjs())
}

/**
 * Returns true if the date is in the future
 * @param date - The date to check
 */
export const isFuture = (date: Date | string): boolean => {
  return dayjs(date).isAfter(dayjs())
}

/**
 * Returns a relative time string (e.g., "2 hours ago")
 * @param date - The date to format
 */
export const fromNow = (date: Date | string): string => {
  return dayjs(date).fromNow()
}

/**
 * Returns the start of the specified unit
 * @param unit - The unit to get the start of (day, week, month, year)
 */
export const startOf = (unit: 'day' | 'week' | 'month' | 'year'): Date => {
  return dayjs().startOf(unit).toDate()
}

/**
 * Returns the end of the specified unit
 * @param unit - The unit to get the end of (day, week, month, year)
 */
export const endOf = (unit: 'day' | 'week' | 'month' | 'year'): Date => {
  return dayjs().endOf(unit).toDate()
}
