import { createServerClient } from '@supabase/ssr'
import { type NextRequest, NextResponse } from 'next/server'

export async function updateSession(request: NextRequest, response: NextResponse) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
          cookiesToSet.forEach(({ name, value, options }) =>
            response.cookies.set(name, value, options),
          )
        },
      },
    },
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (
    !!user &&
    (request.nextUrl.pathname.includes('/login') ||
      request.nextUrl.pathname.includes('/signup') ||
      request.nextUrl.pathname.includes('/verify'))
  ) {
    const url = request.nextUrl.clone()
    url.pathname = '/organizations'
    return NextResponse.redirect(url)
  }

  // Check if this is a mobile HubSpot callback - allow it to proceed without auth
  if (
    request.nextUrl.pathname.includes('/organizations/hubspot/callback') &&
    request.nextUrl.searchParams.get('mode') === 'mobile'
  ) {
    return NextResponse.redirect(
      `com.foundational://hubspot/callback?code=${request.nextUrl.searchParams.get('code')}`,
    )
  }

  if (!user && request.nextUrl.pathname.includes('/organizations')) {
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  if (!user && request.nextUrl.pathname.includes('/invite')) {
    const url = request.nextUrl.clone()
    const invitePath = request.nextUrl.pathname
    url.pathname = '/login'
    url.searchParams.set('redirect_to', invitePath)
    return NextResponse.redirect(url)
  }

  return response
}
