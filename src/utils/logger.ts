import pino from 'pino'

const isDevelopment = process.env.NODE_ENV === 'development'

export const logger = pino({
  level: isDevelopment ? 'debug' : 'info',
  transport: isDevelopment
    ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          ignore: 'pid,hostname',
          translateTime: 'UTC:yyyy-mm-dd HH:MM:ss.l',
        },
      }
    : undefined,
  base: {
    env: process.env.NODE_ENV,
  },
})

// Create namespaced loggers for different parts of the application
export const createNamespacedLogger = (namespace: string) => {
  return logger.child({ namespace })
}

// Example usage:
export const authLogger = createNamespacedLogger('auth')
export const dbLogger = createNamespacedLogger('database')
export const apiLogger = createNamespacedLogger('api')
