import { createClient } from '@/utils/supabase/client'

import { ConnectCRMPayload, DisconnectCRMPayload } from './types'

const supabase = createClient()

export type CRMConnection = App.CRMConnection

export const crmService = {
  async getActiveHubSpotConnection(organizationId: string): Promise<App.CRMConnection | null> {
    const { data, error } = await supabase
      .from('crm_connections')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('crm_type', 'hubspot') // Ensure it's a HubSpot connection
      // Add any other conditions for 'active' if you have them (e.g., a status field)
      .order('created_at', { ascending: false }) // Get the latest if multiple
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      // PGRST116: no rows found, which is fine
      console.error('Error fetching active HubSpot connection:', error)
      throw new Error('Failed to fetch HubSpot connection.')
    }
    return data
  },

  async getHubSpotAuthUrl(): Promise<{ url: string }> {
    const { data, error } = await supabase.functions.invoke(`organizations/crm/hubspot`, {
      method: 'GET',
    })

    if (error) throw error
    return data
  },

  async connectHubSpot(organizationId: string, code: string): Promise<CRMConnection> {
    const { data, error } = await supabase.functions.invoke(
      `organizations/${organizationId}/crm/hubspot`,
      {
        method: 'POST',
        body: JSON.stringify({ code }),
      },
    )

    if (error) throw error
    return data.data
  },

  async getParagonToken(organizationId: string): Promise<{ token: string }> {
    const { data, error } = await supabase.functions.invoke(
      `organizations/${organizationId}/crm/paragon-token`,
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },

  async connectCRM(organizationId: string, payload: ConnectCRMPayload): Promise<CRMConnection> {
    const { data, error } = await supabase.functions.invoke(
      `organizations/${organizationId}/crm/integrations`,
      {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    )

    if (error) throw error
    return data.data
  },

  async disconnectCRM(organizationId: string, payload: DisconnectCRMPayload): Promise<void> {
    const { error } = await supabase.functions.invoke(
      `organizations/${organizationId}/crm/disconnect`,
      {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    )

    if (error) throw error
  },

  async deleteCRMConnection(connectionId: string): Promise<void> {
    const { error } = await supabase.from('crm_connections').delete().eq('id', connectionId)

    if (error) throw error
  },

  async getOrganizationCRMConnections(organizationId: string): Promise<CRMConnection[]> {
    const { data, error } = await supabase
      .from('crm_connections')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  async getIndustries(): Promise<App.CompanyIndustry[]> {
    const { data, error } = await supabase.from('company_industries').select('*').order('name')

    if (error) throw error
    return data
  },

  async getOrganizationCRMDeals(organizationId: string): Promise<App.CachedCRMDeal[]> {
    const { data: connections } = await supabase
      .from('crm_connections')
      .select('id')
      .eq('organization_id', organizationId)

    if (!connections?.length) return []

    const { data, error } = await supabase
      .from('cached_crm_deals')
      .select('*')
      .in(
        'crm_connection_id',
        connections.map(c => c.id),
      )
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  async createDeal(
    connectionId: string,
    dealData: {
      name: string
      amount?: number
      pipeline?: string
      stage?: string
      close_date?: string
      priority?: string
      type?: string
      properties?: Record<string, unknown>
    },
  ): Promise<App.CachedCRMDeal> {
    const { data, error } = await supabase.functions.invoke(`crm/${connectionId}/deals`, {
      method: 'POST',
      body: JSON.stringify(dealData),
    })

    if (error) throw error
    return data
  },

  async updateDeal(
    connectionId: string,
    dealId: string,
    dealData: {
      name: string
      amount?: number
      pipeline?: string
      stage?: string
      close_date?: string
      priority?: string
      type?: string
      properties?: Record<string, unknown>
    },
  ): Promise<App.CachedCRMDeal> {
    const { data, error } = await supabase.functions.invoke(`crm/${connectionId}/deals/${dealId}`, {
      method: 'PATCH',
      body: JSON.stringify(dealData),
    })

    if (error) throw error
    return data
  },

  async deleteDeal(connectionId: string, dealId: string): Promise<void> {
    const { error } = await supabase.functions.invoke(`crm/${connectionId}/deals/${dealId}`, {
      method: 'DELETE',
    })

    if (error) throw error
  },

  async getDealAssociatedContacts(
    connectionId: string,
    dealId: string,
  ): Promise<App.CachedCRMContact[]> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${connectionId}/deals/${dealId}/contacts`,
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },

  async getDealAssociatedCompanies(
    connectionId: string,
    dealId: string,
  ): Promise<App.CachedCRMCompany[]> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${connectionId}/deals/${dealId}/companies`,
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },
}
