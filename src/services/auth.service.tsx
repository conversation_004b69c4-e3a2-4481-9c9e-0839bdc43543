import { Provider, User } from '@supabase/supabase-js'

import { createClient } from '@/utils/supabase/client'

import { UpdatePasswordPayload, UpdateProfilePayload } from './types'

const supabase = createClient()

export type AuthError = {
  message: string
  status?: number
}

export const auth = {
  // Get user profile with proper typing
  async getProfile(userId: string): Promise<App.Profile> {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      if (!profile) throw new Error('Profile not found')

      return profile
    } catch (error) {
      throw error
    }
  },

  // Sync profile with proper typing
  async syncProfile(): Promise<{ user: User; profile: App.Profile } | null> {
    const {
      data: { user },
      error: sessionError,
    } = await supabase.auth.getUser()

    if (sessionError) {
      throw sessionError
    }

    if (!user) {
      return null
    }

    const profile = await this.getProfile(user.id)

    return { user, profile }
  },

  // Email & Password Sign Up with OTP verification
  async signUp(email: string, password: string, name: string) {
    // Step 1: Check if email already exists in profiles table
    const { data: existingUser, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      throw new Error('This email is already registered. Try signing in instead.')
    }

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError
    }

    // Step 2: Try to sign up the user
    const { data, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: name,
        },
      },
    })

    if (signUpError) throw signUpError
    if (!data.user) throw new Error('Failed to create user account')

    return data
  },

  // Verify OTP code
  async verifyOtp(email: string, token: string) {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email',
    })

    if (error) throw error
    if (!data.user) throw new Error('Failed to verify email')

    return data
  },

  // Email & Password Sign In
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error

    return data
  },

  // OAuth Sign In (Google, GitHub)
  async signInWithOAuth(provider: Provider, nextUrl?: string) {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        scopes:
          provider === 'google'
            ? 'email profile https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/contacts.readonly'
            : 'email',
        redirectTo: `${location.origin}/auth/callback?redirect_to=${nextUrl || '/organizations'}`,
      },
    })
    if (error) throw error
    return data
  },

  // Sign Out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw { message: error.message, status: error.status }
  },

  // Password Reset Request
  async resetPasswordRequest(email: string) {
    const resetLink = `${location.origin}/reset-password`
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: resetLink,
    })

    if (error) throw error

    return {
      success: true,
      message: 'If an account exists, a password reset link will be sent.',
    }
  },

  // Password Reset
  async resetPassword(password: string) {
    try {
      // Update the password and wait for the response
      const result = await supabase.auth.updateUser({
        password,
      })

      if (result.error) throw result.error
      return result.data
    } catch (error) {
      throw error
    }
  },

  // Resend verification email
  async resendVerificationEmail(email: string) {
    const { data, error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })

    if (error) throw error
    return data
  },

  async uploadAvatar(file: File, userId: string): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}-${Math.random()}.${fileExt}`
      const filePath = `avatars/${fileName}`

      const { error: uploadError } = await supabase.storage.from('avatars').upload(filePath, file)

      if (uploadError) throw uploadError

      const {
        data: { publicUrl },
      } = supabase.storage.from('avatars').getPublicUrl(filePath)

      return publicUrl
    } catch (error) {
      console.log('🚀 ~ uploadAvatar ~ error:', error)
      throw error
    }
  },

  async updateProfile(payload: UpdateProfilePayload): Promise<App.Profile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(payload.data)
        .eq('id', payload.id)
        .select()
        .single()

      if (error) throw error
      if (!data) throw new Error('Profile not found')

      return data
    } catch (error) {
      throw error
    }
  },

  async updatePassword(payload: UpdatePasswordPayload): Promise<void> {
    try {
      // First verify the current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: payload.email,
        password: payload.currentPassword,
      })

      if (signInError) {
        throw new Error('Current password is incorrect')
      }

      // If current password is correct, proceed with password update
      const { error } = await supabase.auth.updateUser({
        password: payload.newPassword,
      })

      if (error) throw error
    } catch (error) {
      throw error
    }
  },
}
