import { createClient } from '@/utils/supabase/client'

import { UpdateAgentPayload } from './api/types'
import { crmService } from './crm.service' // Added for HubSpot integration
import { AgentSignedUrlResponse } from './types'

const supabase = createClient()

const hubspotToolNames = [
  'hubspot_create_contact',
  'hubspot_get_contact_by_id',
  'hubspot_search_contacts',
  'hubspot_update_contact',
  // "hubspot_delete_contact",
  'hubspot_create_company',
  'hubspot_get_company_by_id',
  'hubspot_search_companies',
  'hubspot_update_company',
  // "hubspot_delete_company",
  'hubspot_create_deal',
  'hubspot_get_deal_by_id',
  'hubspot_search_deals',
  'hubspot_update_deal',
  // "hubspot_delete_deal",
  'hubspot_log_note',
  'hubspot_log_call',
]

const getHubspotToolInstructions = (crmConnectionId: string, organizationId: string) => `

---
**Critical HubSpot CRM Instructions**
You have access to HubSpot CRM tools. When using any 'hubspot_...' tool:
1. You MUST include the parameter 'crm_connection_id' with the exact value: '${crmConnectionId}'
2. You MUST include the parameter 'organization_id' with the exact value: '${organizationId}'

Before running ANY tool, always start with "Let me double check and confirm that ...".
Follow user requests to manage contacts, companies, deals, and log notes or calls in HubSpot.
ALWAYS search the relevant contact, companies, and deals before running other tools to get their unique id.

Be precise with names and details.
---
`

export const agentService = {
  async getOrganizationAgents(organizationId: string): Promise<App.Agent[]> {
    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching organization agents:', error)
      throw new Error('Failed to fetch agents.')
    }
    return data || []
  },

  async getOrganizationTemplates(organizationId: string): Promise<App.Agent[]> {
    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_template', true)
      .order('agent_name', { ascending: true })

    if (error) {
      console.error('Error fetching organization templates:', error)
      throw new Error('Failed to fetch templates.')
    }
    return data || []
  },

  async getAgent(organizationId: string, agentId: string): Promise<App.Agent | null> {
    const { data, error } = await supabase
      .from('agents')
      .select('*, knowledge_bases ( id, eleven_labs_id, name, type )')
      .eq('organization_id', organizationId)
      .eq('agent_id', agentId)
      .maybeSingle()

    if (error) {
      console.error(`Error fetching agent ${agentId}:`, error)
      throw new Error('Failed to fetch agent details.')
    }
    return data
  },

  async updateAgentTemplateStatus(
    organizationId: string,
    agentId: string,
    isTemplate: boolean,
  ): Promise<App.Agent> {
    const { data, error } = await supabase
      .from('agents')
      .update({ is_template: isTemplate, last_updated: new Date().toISOString() })
      .eq('organization_id', organizationId)
      .eq('agent_id', agentId)
      .select()
      .single()

    if (error) {
      console.error(`Error updating template status for agent ${agentId}:`, error)
      throw new Error('Failed to update agent template status.')
    }
    return data
  },

  async createAgent(
    organizationId: string,
    // Define a specific type for creation payload if needed, or use Partial<App.Agent>
    agentData: Partial<Omit<App.Agent, 'agent_id' | 'created_at' | 'last_updated'>>,
  ): Promise<App.Agent> {
    const configForSupabase = { ...agentData }

    try {
      const hubSpotConnection = await crmService.getActiveHubSpotConnection(organizationId)
      if (hubSpotConnection && hubSpotConnection.id) {
        const instructions = getHubspotToolInstructions(hubSpotConnection.id, organizationId)
        configForSupabase.system_prompt = (configForSupabase.system_prompt || '') + instructions

        const currentTools = new Set(configForSupabase.tools || [])
        hubspotToolNames.forEach(toolName => currentTools.add(toolName))
        configForSupabase.tools = Array.from(currentTools)
        console.log(
          `Augmented agent config for org ${organizationId} with HubSpot tools and prompt instructions.`,
        )
      } else {
        console.log(
          `No active HubSpot connection for org ${organizationId}. Proceeding without HubSpot specific config.`,
        )
      }
    } catch (e) {
      console.warn(
        `Failed to augment agent config with HubSpot details for org ${organizationId}:`,
        e,
      )
      // Proceed with agent creation without HubSpot specific tools/prompt if fetching connection failed
    }

    const { data, error } = await supabase.functions.invoke('manage-elevenlabs-agent', {
      method: 'POST',
      body: JSON.stringify({
        action: 'create',
        organization_id: organizationId,
        config: configForSupabase, // Pass the augmented agent configuration data
      }),
    })

    if (error) {
      console.error('Error creating agent via Edge Function:', error)
      // Attempt to parse Supabase Function error details if available
      let message = 'Failed to create agent.'
      try {
        const errorDetails = JSON.parse(error.context?.responseText || '{}')
        message = errorDetails.details || errorDetails.error || message
      } catch {
        // Ignore parsing error
      }
      throw new Error(message)
    }
    // Assuming the function returns { success: true, agent_id: string, ... }
    // We might need to fetch the full agent data again if the function doesn't return it
    // For now, let's assume it returns enough data or we rely on query invalidation
    // The edge function currently returns { success: true, agent_id: string, elevenlabs_response: any }
    // We need the full App.Agent object. Let's fetch it after creation.
    if (data?.success && data?.agent_id) {
      const newAgent = await this.getAgent(organizationId, data.agent_id)
      if (!newAgent) {
        throw new Error('Failed to retrieve newly created agent details.')
      }
      return newAgent
    } else {
      throw new Error('Agent creation function did not return expected success response.')
    }
  },

  async updateAgent(
    organizationId: string,
    agentId: string,
    // Define a specific type for update payload
    agentData: UpdateAgentPayload,
  ): Promise<App.Agent> {
    const configForSupabase = { ...agentData } // Shallow copy the payload

    try {
      const hubSpotConnection = await crmService.getActiveHubSpotConnection(organizationId)
      if (hubSpotConnection && hubSpotConnection.id) {
        // Only augment system_prompt if it's explicitly part of the update payload
        if (configForSupabase.system_prompt !== undefined) {
          const instructions = getHubspotToolInstructions(hubSpotConnection.id, organizationId)
          configForSupabase.system_prompt = (configForSupabase.system_prompt || '') + instructions
          console.log(
            `Augmented system_prompt for agent ${agentId} in org ${organizationId} with HubSpot instructions.`,
          )
        }

        // Only augment tools if they are explicitly part of the update payload
        if (configForSupabase.tools !== undefined) {
          const currentTools = new Set(configForSupabase.tools || [])
          hubspotToolNames.forEach(toolName => currentTools.add(toolName))
          configForSupabase.tools = Array.from(currentTools)
          console.log(
            `Augmented tools list for agent ${agentId} in org ${organizationId} with HubSpot tools.`,
          )
        } else if (configForSupabase.system_prompt !== undefined) {
          // If system_prompt was updated (and thus HubSpot instructions added),
          // ensure HubSpot tools are also present, even if tools array wasn't explicitly sent for update.
          // This covers the case where a prompt is updated to include tool usage instructions,
          // but the client didn't also send the tools array.
          const existingAgentFull = await this.getAgent(organizationId, agentId)
          const baseTools = new Set(existingAgentFull?.tools || [])
          hubspotToolNames.forEach(toolName => baseTools.add(toolName))
          configForSupabase.tools = Array.from(baseTools)
          console.log(
            `Implicitly added HubSpot tools for agent ${agentId} as system_prompt was updated with HubSpot instructions.`,
          )
        }
      } else {
        console.log(
          `No active HubSpot connection for org ${organizationId} during agent ${agentId} update. Proceeding without HubSpot specific config augmentation.`,
        )
      }
    } catch (e) {
      console.warn(
        `Failed to augment agent config with HubSpot details during update for agent ${agentId} in org ${organizationId}:`,
        e,
      )
      // Proceed with agent update without HubSpot specific tools/prompt if fetching connection failed
    }

    const { data, error } = await supabase.functions.invoke('manage-elevenlabs-agent', {
      method: 'POST', // Or PATCH if the function supports it
      body: JSON.stringify({
        action: 'update',
        organization_id: organizationId,
        agent_id: agentId,
        config: configForSupabase, // Pass the potentially augmented fields to update
      }),
    })

    if (error) {
      console.error(`Error updating agent ${agentId} via Edge Function:`, error)
      let message = 'Failed to update agent.'
      try {
        const errorDetails = JSON.parse(error.context?.responseText || '{}')
        message = errorDetails.details || errorDetails.error || message
      } catch {
        // Ignore parsing error
      }
      throw new Error(message)
    }

    // Similar to create, fetch the updated agent data
    if (data?.success && data?.agent_id) {
      const updatedAgent = await this.getAgent(organizationId, data.agent_id)
      if (!updatedAgent) {
        throw new Error('Failed to retrieve updated agent details.')
      }
      return updatedAgent
    } else {
      throw new Error('Agent update function did not return expected success response.')
    }
  },

  async deleteAgent(organizationId: string, agentId: string): Promise<void> {
    const { data, error } = await supabase.functions.invoke('manage-elevenlabs-agent', {
      method: 'POST',
      body: JSON.stringify({
        action: 'delete',
        organization_id: organizationId,
        agent_id: agentId,
        // No 'config' needed for delete action
      }),
    })

    if (error) {
      console.error(`Error deleting agent ${agentId} via Edge Function:`, error)
      let message = 'Failed to delete agent.'
      try {
        // Attempt to parse specific error message from the function response
        const errorDetails = JSON.parse(error.context?.responseText || '{}')
        message = errorDetails.details || errorDetails.error || message
      } catch {
        // Ignore parsing error, use generic message
      }
      throw new Error(message)
    }

    // Check if the function indicated success (optional, depends on function's return value for delete)
    if (!data?.success) {
      // Log potential issues even if no error was thrown
      console.warn(`Agent delete function for ${agentId} did not return success:`, data)
      // Depending on strictness, you might throw an error here too
      // throw new Error('Agent deletion function did not confirm success.');
    }

    // No agent data to return after deletion
  },

  async getAgentSignedUrl(agentId: string): Promise<AgentSignedUrlResponse> {
    const { data, error } = await supabase.functions.invoke(
      `manage-elevenlabs-agent/agents/${agentId}/signed-url`,
      {
        method: 'GET',
      },
    )

    if (error) {
      console.error(`Error fetching signed URL for agent ${agentId}:`, error)
      throw new Error('Failed to fetch signed URL.')
    }

    return data
  },
  async getVoices(): Promise<App.Voice[]> {
    const { data, error } = await supabase.functions.invoke('manage-elevenlabs-agent/voices', {
      method: 'GET',
    })

    if (error) {
      console.error(`Error fetching voices:`, error)
      throw new Error('Failed to fetch voices.')
    }

    return data.voices
  },
}
