import { createClient } from '@/utils/supabase/client'

import type { CreateCompanyData, UpdateCompanyData } from './types'

const supabase = createClient()

export const companyService = {
  async getOrganizationCompanies(organizationId: string): Promise<App.CachedCRMCompany[]> {
    const { data: connections } = await supabase
      .from('crm_connections')
      .select('id')
      .eq('organization_id', organizationId)

    if (!connections?.length) return []

    const { data, error } = await supabase
      .from('cached_crm_companies')
      .select('*')
      .in(
        'crm_connection_id',
        connections.map(c => c.id),
      )

    if (error) throw error
    return data
  },

  async createCompany(payload: CreateCompanyData): Promise<App.CachedCRMCompany> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${payload.connectionId}/companies`,
      {
        method: 'POST',
        body: JSON.stringify(payload.companyData),
      },
    )

    if (error) throw error
    return data
  },

  async updateCompany(payload: UpdateCompanyData): Promise<App.CachedCRMCompany> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${payload.connectionId}/companies/${payload.companyId}`,
      {
        method: 'PATCH',
        body: JSON.stringify(payload.companyData),
      },
    )

    if (error) throw error
    return data
  },

  async deleteCompany(connectionId: string, companyId: string): Promise<void> {
    const { error } = await supabase.functions.invoke(
      `crm/${connectionId}/companies/${companyId}`,
      {
        method: 'DELETE',
      },
    )

    if (error) throw error
  },

  async getCompanyAssociatedContacts(
    connectionId: string,
    companyId: string,
  ): Promise<App.CachedCRMCompany> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${connectionId}/companies/${companyId}/contacts`,
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },
}
