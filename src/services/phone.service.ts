import { createClient } from '@/utils/supabase/client'

const supabase = createClient()

export const phoneService = {
  async getOrganizationPhones(organizationId: string): Promise<App.Phone[]> {
    const { data, error } = await supabase.functions.invoke(
      'organizations/' + organizationId + '/phones',
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },

  async addPhone(
    organizationId: string,
    phoneData: {
      phone_number: string
      label: string
      provider: App.PhoneProvider
      sid?: string
      token?: string
      termination_uri?: string
      credentials?: {
        username?: string
        password?: string
      }
    },
  ): Promise<App.Phone> {
    const { data, error } = await supabase.functions.invoke(
      'organizations/' + organizationId + '/phones',
      {
        method: 'POST',
        body: JSON.stringify(phoneData),
      },
    )

    if (error) throw error
    return data.data
  },

  async deletePhone(organizationId: string, phoneId: string): Promise<App.Phone> {
    const { data, error } = await supabase.functions.invoke(
      'organizations/' + organizationId + '/phones/' + phoneId,
      {
        method: 'DELETE',
      },
    )

    if (error) throw error
    return data.data
  },
}
