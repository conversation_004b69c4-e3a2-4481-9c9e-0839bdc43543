import { createClient } from '@/utils/supabase/client'

import {
  CreateKnowledgeBaseFilePayload,
  CreateKnowledgeBaseTextPayload,
  CreateKnowledgeBaseUrlPayload,
  KnowledgeBaseRagIndexStatusResponse,
} from './types'

const supabase = createClient()

export const knowledgeBaseService = {
  async getOrganizationKnowledgeBases(organizationId: string): Promise<App.KnowledgeBase[]> {
    const { data, error } = await supabase
      .from('knowledge_bases')
      .select(
        `
        *,
        user:profiles(id, name, email),
        agents ( agent_id, agent_name )
      `,
      )
      .eq('organization_id', organizationId)
      .is('deleted_at', null)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching knowledge bases:', error)
      throw error
    }

    // Transform the data to match the expected format

    return data || []
  },

  async deleteKnowledgeBase(knowledgeBaseId: string): Promise<void> {
    const { error } = await supabase.functions.invoke(
      'manage-elevenlabs-agent/knowledge-base/' + knowledgeBaseId,
      {
        method: 'DELETE',
      },
    )

    if (error) throw error
  },

  async connectAgentToKnowledgeBase(agentId: string, knowledgeBaseId: string): Promise<void> {
    const { error } = await supabase.from('agent_knowledge_bases').insert({
      agent_id: agentId,
      knowledge_base_id: knowledgeBaseId,
    })

    if (error) {
      console.error('Error connecting agent to knowledge base:', error)
      throw error
    }
  },

  async disconnectAgentFromKnowledgeBase(agentId: string, knowledgeBaseId: string): Promise<void> {
    const { error } = await supabase
      .from('agent_knowledge_bases')
      .delete()
      .eq('agent_id', agentId)
      .eq('knowledge_base_id', knowledgeBaseId)

    if (error) {
      console.error('Error disconnecting agent from knowledge base:', error)
      throw error
    }
  },
  async createKnowledgeBaseText(
    payload: CreateKnowledgeBaseTextPayload,
  ): Promise<App.KnowledgeBase> {
    const { data, error } = await supabase.functions.invoke(
      'manage-elevenlabs-agent/knowledge-base/text',
      {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    )

    if (error) {
      console.error('Error creating knowledge base:', error)
      throw error
    }

    return data
  },
  async createKnowledgeBaseFile(
    payload: CreateKnowledgeBaseFilePayload,
  ): Promise<App.KnowledgeBase> {
    const formData = new FormData()
    formData.append('file', payload.file)

    const { data, error } = await supabase.functions.invoke(
      `manage-elevenlabs-agent/${payload.organization_id}/knowledge-base/file`,
      {
        method: 'POST',
        body: formData,
      },
    )

    if (error) {
      console.error('Error creating knowledge base:', error)
      throw error
    }

    return data
  },
  async createKnowledgeBaseUrl(payload: CreateKnowledgeBaseUrlPayload): Promise<App.KnowledgeBase> {
    const { data, error } = await supabase.functions.invoke(
      'manage-elevenlabs-agent/knowledge-base/url',
      {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    )

    if (error) {
      console.error('Error creating knowledge base:', error)
      throw error
    }

    return data
  },
  async knowledgeBaseRagIndexStatus(
    knowledgeBaseId: string,
  ): Promise<KnowledgeBaseRagIndexStatusResponse> {
    const { data, error } = await supabase.functions.invoke(
      `manage-elevenlabs-agent/knowledge-base/${knowledgeBaseId}/rag-index`,
      {
        method: 'GET',
      },
    )

    if (error) {
      console.error('Error getting knowledge base rag index status:', error)
      throw error
    }

    return data
  },
}
