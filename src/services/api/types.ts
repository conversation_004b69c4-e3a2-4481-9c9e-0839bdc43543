export type Response<T> = {
  data: T
  statusCode: number
  message: string
}

export type GetHubSpotContactsData = { records: App.ParagonHubSpotContact[] }

export type GetHubSpotContactsResponse = Response<GetHubSpotContactsData>

export type CreateHubSpotContactPayload = Partial<App.ParagonHubSpotContactProperties>

export type CreateHubSpotContactData = App.ParagonHubSpotContact

export type CreateHubSpotContactResponse = Response<CreateHubSpotContactData>

export type DeleteHubSpotContactPayload = {
  recordId: string
}

export type DeleteHubSpotContactData = App.ParagonHubSpotContact

export type DeleteHubSpotContactResponse = Response<DeleteHubSpotContactData>

export type UpdateHubSpotContactPayload = Partial<App.ParagonHubSpotContactProperties> & {
  recordId: string
}

export type UpdateHubSpotContactData = App.ParagonHubSpotContact

export type UpdateHubSpotContactResponse = Response<UpdateHubSpotContactData>

export type GetHubSpotContactByIdPayload = {
  recordId: string
}

export type GetHubSpotContactByIdData = App.ParagonHubSpotContact

export type GetHubSpotContactByIdResponse = Response<GetHubSpotContactByIdData>

//Company
export type GetHubSpotCompaniesData = { records: App.ParagonHubSpotCompany[] }

export type GetHubSpotCompaniesResponse = Response<GetHubSpotCompaniesData>

export type CreateHubSpotCompanyPayload = {
  name?: string
  domain?: string
  industry?: string
  type?: string
  phone?: string
  city?: string
  state?: string
  zip?: string
  numberofemployees?: number
  annualrevenue?: number
  timezone?: string
  description?: string
  linkedin_company_page?: string
  about_us?: string
  hs_additional_domains?: string
  hs_all_assigned_business_unit_ids?: string
  hs_csm_sentiment?: string
  closedate?: string
  hs_keywords?: string
  country?: string
  hs_country_code?: string
  hs_employee_range?: string
  facebook_company_page?: string
  facebookfans?: string
  googleplus_page?: string
  is_public?: boolean
  hs_lead_status?: string
  lifecyclestage?: string
  linkedinbio?: string
  website?: string
  address?: string
}

export type CreateHubSpotCompanyData = App.ParagonHubSpotCompany

export type CreateHubSpotCompanyResponse = Response<CreateHubSpotCompanyData>

export type DeleteHubSpotCompanyPayload = {
  recordId: string
}

export type DeleteHubSpotCompanyData = App.ParagonHubSpotCompany

export type DeleteHubSpotCompanyResponse = Response<DeleteHubSpotCompanyData>

export type UpdateHubSpotCompanyPayload = Partial<CreateHubSpotCompanyPayload> & {
  recordId: string
}

export type UpdateHubSpotCompanyData = App.ParagonHubSpotCompany

export type UpdateHubSpotCompanyResponse = Response<UpdateHubSpotCompanyData>

export type GetHubSpotCompanyByIdPayload = {
  recordId: string
}

export type GetSalesforceAccountsParameters = {
  paginationParameters: {
    pageCursor: string
  }
}

export type GetHubSpotCompanyByIdData = App.ParagonHubSpotCompany

export type GetHubSpotCompanyByIdResponse = Response<GetHubSpotCompanyByIdData>

//Salesforce
export type GetSalesforceAccountsData = {
  records: {
    done: boolean
    totalSize: number
    records: App.ParagonSalesforceAccount[]
    pageCursor?: string
  }
}

export type GetSalesforceAccountsResponse = Response<GetSalesforceAccountsData>

export type CreateSalesforceAccountPayload = {
  Name: string
  Website?: string
  Phone?: string
  Description?: string
  additionalFields?: Record<string, string>
}

export type CreateSalesforceAccountData = App.ParagonSalesforceAccount

export type CreateSalesforceAccountResponse = Response<CreateSalesforceAccountData>

export type DeleteSalesforceAccountPayload = {
  recordId: string
}

export type DeleteSalesforceAccountData = App.ParagonSalesforceAccount

export type DeleteSalesforceAccountResponse = Response<DeleteSalesforceAccountData>

export type UpdateSalesforceAccountPayload = Partial<CreateSalesforceAccountPayload> & {
  recordId: string
}

export type UpdateSalesforceAccountData = App.ParagonSalesforceAccount

export type UpdateSalesforceAccountResponse = Response<UpdateSalesforceAccountData>

export type GetSalesforceAccountByIdPayload = {
  recordId: string
}

export type GetSalesforceAccountByIdData = App.ParagonSalesforceAccount

export type GetSalesforceAccountByIdResponse = Response<GetSalesforceAccountByIdData>

export type GetSalesforceContactsParameters = {
  paginationParameters: {
    pageCursor: string
  }
}

export type GetSalesforceContactsData = {
  records: {
    done: boolean
    totalSize: number
    records: App.ParagonSalesforceContact[]
    pageCursor?: string
  }
}

export type GetSalesforceContactsResponse = Response<GetSalesforceContactsData>

export type CreateSalesforceContactPayload = {
  FirstName: string
  LastName: string
  accountId: string
  Email?: string
  Description?: string
  Title?: string
  additionalFields?: Record<string, string>
}

export type CreateSalesforceContactData = App.ParagonSalesforceContact

export type CreateSalesforceContactResponse = Response<CreateSalesforceContactData>

export type DeleteSalesforceContactPayload = {
  recordId: string
}

export type DeleteSalesforceContactData = App.ParagonSalesforceContact

export type DeleteSalesforceContactResponse = Response<DeleteSalesforceContactData>

export type UpdateSalesforceContactPayload = Partial<CreateSalesforceContactPayload> & {
  recordId: string
}

export type UpdateSalesforceContactData = App.ParagonSalesforceContact

export type UpdateSalesforceContactResponse = Response<UpdateSalesforceContactData>

export type GetSalesforceContactByIdPayload = {
  recordId: string
}

export type GetSalesforceContactByIdData = App.ParagonSalesforceContact

export type GetSalesforceContactByIdResponse = Response<GetSalesforceContactByIdData>

export type GetSalesforceLeadsParameters = {
  paginationParameters: {
    pageCursor: string
  }
}

export type GetSalesforceLeadsData = {
  records: {
    done: boolean
    totalSize: number
    records: App.ParagonSalesforceLead[]
    pageCursor?: string
  }
}

export type GetSalesforceLeadsResponse = Response<GetSalesforceLeadsData>

export type CreateSalesforceLeadPayload = {
  LastName: string
  Company: string
  FirstName: string
  Email?: string
  Phone?: string
  Website?: string
  Title?: string
  Status?: string
  Description?: string
  additionalFields?: Record<string, string | number>
}

export type CreateSalesforceLeadData = App.ParagonSalesforceLead

export type CreateSalesforceLeadResponse = Response<CreateSalesforceLeadData>

export type DeleteSalesforceLeadPayload = {
  recordId: string
}

export type DeleteSalesforceLeadData = App.ParagonSalesforceLead

export type DeleteSalesforceLeadResponse = Response<DeleteSalesforceLeadData>

export type UpdateSalesforceLeadPayload = Partial<CreateSalesforceLeadPayload> & {
  recordId: string
}

export type UpdateSalesforceLeadData = App.ParagonSalesforceLead

export type UpdateSalesforceLeadResponse = Response<UpdateSalesforceLeadData>

export type GetSalesforceLeadByIdPayload = {
  recordId: string
}

export type GetSalesforceLeadByIdData = App.ParagonSalesforceLead

export type GetSalesforceLeadByIdResponse = Response<GetSalesforceLeadByIdData>

export type UpdateAgentPayload = Partial<
  Omit<
    App.Agent,
    'agent_id' | 'created_at' | 'last_updated' | 'organization_id' | 'user_id' | 'knowledge_bases'
  >
> & {
  knowledge_bases: {
    id: string
    eleven_labs_id: string
    name: string
    type: 'url' | 'file' | 'text'
    usage_mode: string
  }[]
}

export type UpdateAgentResponse = Response<App.Agent>

export interface OrganizationWithMemberInfo extends App.Organization {
  role: App.OrganizationRole
  member_count: number
}
