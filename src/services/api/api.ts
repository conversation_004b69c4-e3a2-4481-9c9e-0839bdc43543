/* eslint-disable @typescript-eslint/no-explicit-any */
import Axios, { AxiosInstance } from 'axios'

import { useStore } from '@/stores/root.store'

import { paragonService } from '../paragon.service'
import { paragonActions } from './actions'
import { ApiConfig, DEFAULT_API_CONFIG } from './api.config'
import Endpoints from './api.endpoint'
import {
  CreateHubSpotCompanyData,
  CreateHubSpotCompanyPayload,
  CreateHubSpotCompanyResponse,
  CreateHubSpotContactData,
  CreateHubSpotContactPayload,
  CreateHubSpotContactResponse,
  CreateSalesforceAccountData,
  CreateSalesforceAccountPayload,
  CreateSalesforceAccountResponse,
  CreateSalesforceContactData,
  CreateSalesforceContactPayload,
  CreateSalesforceContactResponse,
  CreateSalesforceLeadData,
  CreateSalesforceLeadPayload,
  CreateSalesforceLeadResponse,
  DeleteHubSpotCompanyData,
  DeleteHubSpotCompanyPayload,
  DeleteHubSpotCompanyResponse,
  DeleteHubSpotContactData,
  DeleteHubSpotContactPayload,
  DeleteHubSpotContactResponse,
  DeleteSalesforceAccountData,
  DeleteSalesforceAccountPayload,
  DeleteSalesforceAccountResponse,
  DeleteSalesforceContactData,
  DeleteSalesforceContactPayload,
  DeleteSalesforceContactResponse,
  DeleteSalesforceLeadData,
  DeleteSalesforceLeadPayload,
  DeleteSalesforceLeadResponse,
  GetHubSpotCompaniesData,
  GetHubSpotCompaniesResponse,
  GetHubSpotCompanyByIdData,
  GetHubSpotCompanyByIdPayload,
  GetHubSpotCompanyByIdResponse,
  GetHubSpotContactByIdData,
  GetHubSpotContactByIdPayload,
  GetHubSpotContactByIdResponse,
  GetHubSpotContactsData,
  GetHubSpotContactsResponse,
  GetSalesforceAccountByIdData,
  GetSalesforceAccountByIdPayload,
  GetSalesforceAccountByIdResponse,
  GetSalesforceAccountsData,
  GetSalesforceAccountsParameters,
  GetSalesforceAccountsResponse,
  GetSalesforceContactByIdData,
  GetSalesforceContactByIdPayload,
  GetSalesforceContactByIdResponse,
  GetSalesforceContactsData,
  GetSalesforceContactsResponse,
  GetSalesforceLeadByIdData,
  GetSalesforceLeadByIdPayload,
  GetSalesforceLeadByIdResponse,
  GetSalesforceLeadsData,
  GetSalesforceLeadsParameters,
  GetSalesforceLeadsResponse,
  UpdateHubSpotCompanyData,
  UpdateHubSpotCompanyPayload,
  UpdateHubSpotCompanyResponse,
  UpdateHubSpotContactData,
  UpdateHubSpotContactPayload,
  UpdateHubSpotContactResponse,
  UpdateSalesforceAccountData,
  UpdateSalesforceAccountPayload,
  UpdateSalesforceAccountResponse,
  UpdateSalesforceContactData,
  UpdateSalesforceContactPayload,
  UpdateSalesforceContactResponse,
  UpdateSalesforceLeadData,
  UpdateSalesforceLeadPayload,
  UpdateSalesforceLeadResponse,
} from './types'

export class Api {
  /**
   * The underlying apisauce instance which performs the requests.
   */
  static _jwt: string
  static _instance: Api
  private _privateGateway: AxiosInstance
  private _publicGateway: AxiosInstance
  /**
   * Configurable options.
   */
  config: ApiConfig

  setup() {
    this._publicGateway.interceptors.request.use(
      async config => {
        const configValue = config

        return configValue
      },
      error => {
        // Do something with request error
        return Promise.reject(error)
      },
    )
    this._privateGateway.interceptors.request.use(
      async (config: any) => {
        const token = await paragonService.getToken(useStore.getState().organizationId ?? '')
        return {
          ...config,
          headers: {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          },
        }
      },
      error => Promise.reject(error),
    )
    // Response interceptor for API calls
    this._privateGateway.interceptors.response.use(response => {
      return response
    }, this.onResponseError)
  }
  private onResponseError = async (error: any): Promise<void> => {
    if (error.response.status === 403) {
      // useAuthStore.getState().logout()

      return Promise.reject(error)
    }
    if (error.response.status === 401 && !(error.config as any)._retry) {
      ;(error.config as any)._retry = true
      try {
        const token = await paragonService.getToken(useStore.getState().organizationId ?? '')
        if (token) {
          error.config.headers.Authorization = `Bearer ${token}`

          return this._privateGateway(error.config) as any
        }
      } catch {
        return Promise.reject(error)
      }

      return Promise.reject(error)
    }

    return Promise.reject(error)
  }

  /**
   * Creates the api.
   *
   * @param config The configuration to use.
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG) {
    this.config = config

    this._publicGateway = Axios.create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        Accept: 'application/json',
      },
    })

    this._privateGateway = Axios.create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        Accept: 'application/json',
      },
    })

    this._privateGateway.interceptors.request.use(
      async config => {
        const token = await paragonService.getToken(useStore.getState().organizationId ?? '')
        config.headers.Authorization = `Bearer ${token}`

        return config
      },
      error => Promise.reject(error),
    )

    this._privateGateway.interceptors.response.use(
      response => response,
      error => {
        if (error.response?.status === 401) {
          window.location.href = '/login'
        }
        return Promise.reject(error)
      },
    )

    this.setup()
  }

  /**
   * Get instance of apis.
   */
  public static get instance() {
    if (!Api._instance) {
      Api._instance = new Api()
    }

    return Api._instance
  }

  getPublicGateway() {
    return this._publicGateway
  }

  getPrivateGateway() {
    return this._privateGateway
  }

  getHubSpotContacts() {
    return this._privateGateway.post<GetHubSpotContactsData, GetHubSpotContactsResponse>(
      Endpoints.HUBSPOT_GET_RECORDS_CONTACTS,
      {
        action: paragonActions.HUBSPOT_GET_RECORDS_CONTACTS,
        parameters: { paginationParameters: { pageCursor: '0' } },
      },
    )
  }

  createHubSpotContact(payload: CreateHubSpotContactPayload) {
    return this._privateGateway.post<CreateHubSpotContactData, CreateHubSpotContactResponse>(
      Endpoints.HUBSPOT_CREATE_RECORD_CONTACTS,
      {
        action: paragonActions.HUBSPOT_CREATE_RECORD_CONTACTS,
        parameters: { ...payload },
      },
    )
  }

  deleteHubSpotContact(payload: DeleteHubSpotContactPayload) {
    return this._privateGateway.post<DeleteHubSpotContactData, DeleteHubSpotContactResponse>(
      Endpoints.HUBSPOT_DELETE_RECORD_CONTACTS,
      {
        action: paragonActions.HUBSPOT_DELETE_RECORD_CONTACTS,
        parameters: { ...payload },
      },
    )
  }

  updateHubSpotContact(payload: UpdateHubSpotContactPayload) {
    return this._privateGateway.post<UpdateHubSpotContactData, UpdateHubSpotContactResponse>(
      Endpoints.HUBSPOT_UPDATE_RECORD_CONTACTS,
      {
        action: paragonActions.HUBSPOT_UPDATE_RECORD_CONTACTS,
        parameters: { ...payload },
      },
    )
  }

  getHubSpotContactById(payload: GetHubSpotContactByIdPayload) {
    return this._privateGateway.post<GetHubSpotContactByIdData, GetHubSpotContactByIdResponse>(
      Endpoints.HUBSPOT_GET_RECORD_BY_ID_CONTACTS,
      {
        action: paragonActions.HUBSPOT_GET_RECORD_BY_ID_CONTACTS,
        parameters: { ...payload },
      },
    )
  }

  getHubSpotCompanies() {
    return this._privateGateway.post<GetHubSpotCompaniesData, GetHubSpotCompaniesResponse>(
      Endpoints.HUBSPOT_GET_RECORDS_COMPANIES,
      {
        action: paragonActions.HUBSPOT_GET_RECORDS_COMPANIES,
        parameters: { paginationParameters: { pageCursor: '0' } },
      },
    )
  }

  createHubSpotCompany(payload: CreateHubSpotCompanyPayload) {
    return this._privateGateway.post<CreateHubSpotCompanyData, CreateHubSpotCompanyResponse>(
      Endpoints.HUBSPOT_CREATE_RECORD_COMPANIES,
      {
        action: paragonActions.HUBSPOT_CREATE_RECORD_COMPANIES,
        parameters: { ...payload },
      },
    )
  }

  deleteHubSpotCompany(payload: DeleteHubSpotCompanyPayload) {
    return this._privateGateway.post<DeleteHubSpotCompanyData, DeleteHubSpotCompanyResponse>(
      Endpoints.HUBSPOT_DELETE_RECORD_COMPANIES,
      {
        action: paragonActions.HUBSPOT_DELETE_RECORD_COMPANIES,
        parameters: { ...payload },
      },
    )
  }

  updateHubSpotCompany(payload: UpdateHubSpotCompanyPayload) {
    return this._privateGateway.post<UpdateHubSpotCompanyData, UpdateHubSpotCompanyResponse>(
      Endpoints.HUBSPOT_UPDATE_RECORD_COMPANIES,
      {
        action: paragonActions.HUBSPOT_UPDATE_RECORD_COMPANIES,
        parameters: { ...payload },
      },
    )
  }

  getHubSpotCompanyById(payload: GetHubSpotCompanyByIdPayload) {
    return this._privateGateway.post<GetHubSpotCompanyByIdData, GetHubSpotCompanyByIdResponse>(
      Endpoints.HUBSPOT_GET_RECORD_BY_ID_COMPANIES,
      {
        action: paragonActions.HUBSPOT_GET_RECORD_BY_ID_COMPANIES,
        parameters: { ...payload },
      },
    )
  }

  getSalesforceAccounts(parameters: GetSalesforceAccountsParameters) {
    return this._privateGateway.post<GetSalesforceAccountsData, GetSalesforceAccountsResponse>(
      Endpoints.SALESFORCE_SEARCH_RECORDS_ACCOUNT,
      {
        action: paragonActions.SALESFORCE_SEARCH_RECORDS_ACCOUNT,
        parameters,
      },
    )
  }

  createSalesforceAccount(payload: CreateSalesforceAccountPayload) {
    return this._privateGateway.post<CreateSalesforceAccountData, CreateSalesforceAccountResponse>(
      Endpoints.SALESFORCE_CREATE_RECORD_ACCOUNT,
      {
        action: paragonActions.SALESFORCE_CREATE_RECORD_ACCOUNT,
        parameters: { ...payload },
      },
    )
  }

  deleteSalesforceAccount(payload: DeleteSalesforceAccountPayload) {
    return this._privateGateway.post<DeleteSalesforceAccountData, DeleteSalesforceAccountResponse>(
      Endpoints.SALESFORCE_DELETE_RECORD_ACCOUNT,
      {
        action: paragonActions.SALESFORCE_DELETE_RECORD_ACCOUNT,
        parameters: { ...payload },
      },
    )
  }

  updateSalesforceAccount(payload: UpdateSalesforceAccountPayload) {
    return this._privateGateway.post<UpdateSalesforceAccountData, UpdateSalesforceAccountResponse>(
      Endpoints.SALESFORCE_UPDATE_RECORD_ACCOUNT,
      {
        action: paragonActions.SALESFORCE_UPDATE_RECORD_ACCOUNT,
        parameters: { ...payload },
      },
    )
  }
  getSalesforceAccountById(payload: GetSalesforceAccountByIdPayload) {
    return this._privateGateway.post<
      GetSalesforceAccountByIdData,
      GetSalesforceAccountByIdResponse
    >(Endpoints.SALESFORCE_GET_RECORD_BY_ID_ACCOUNT, {
      action: paragonActions.SALESFORCE_GET_RECORD_BY_ID_ACCOUNT,
      parameters: { ...payload },
    })
  }
  getSalesforceContacts(parameters: GetSalesforceAccountsParameters) {
    return this._privateGateway.post<GetSalesforceContactsData, GetSalesforceContactsResponse>(
      Endpoints.SALESFORCE_SEARCH_RECORDS_CONTACT,
      {
        action: paragonActions.SALESFORCE_SEARCH_RECORDS_CONTACT,
        parameters,
      },
    )
  }
  createSalesforceContact(payload: CreateSalesforceContactPayload) {
    return this._privateGateway.post<CreateSalesforceContactData, CreateSalesforceContactResponse>(
      Endpoints.SALESFORCE_CREATE_RECORD_CONTACT,
      {
        action: paragonActions.SALESFORCE_CREATE_RECORD_CONTACT,
        parameters: { ...payload },
      },
    )
  }
  deleteSalesforceContact(payload: DeleteSalesforceContactPayload) {
    return this._privateGateway.post<DeleteSalesforceContactData, DeleteSalesforceContactResponse>(
      Endpoints.SALESFORCE_DELETE_RECORD_CONTACT,
      {
        action: paragonActions.SALESFORCE_DELETE_RECORD_CONTACT,
        parameters: { ...payload },
      },
    )
  }
  updateSalesforceContact(payload: UpdateSalesforceContactPayload) {
    return this._privateGateway.post<UpdateSalesforceContactData, UpdateSalesforceContactResponse>(
      Endpoints.SALESFORCE_UPDATE_RECORD_CONTACT,
      {
        action: paragonActions.SALESFORCE_UPDATE_RECORD_CONTACT,
        parameters: { ...payload },
      },
    )
  }
  getSalesforceContactById(payload: GetSalesforceContactByIdPayload) {
    return this._privateGateway.post<
      GetSalesforceContactByIdData,
      GetSalesforceContactByIdResponse
    >(Endpoints.SALESFORCE_GET_RECORD_BY_ID_CONTACT, {
      action: paragonActions.SALESFORCE_GET_RECORD_BY_ID_CONTACT,
      parameters: { ...payload },
    })
  }
  getSalesforceLeads(parameters: GetSalesforceLeadsParameters) {
    return this._privateGateway.post<GetSalesforceLeadsData, GetSalesforceLeadsResponse>(
      Endpoints.SALESFORCE_SEARCH_RECORDS_LEAD,
      {
        action: paragonActions.SALESFORCE_SEARCH_RECORDS_LEAD,
        parameters,
      },
    )
  }
  createSalesforceLead(payload: CreateSalesforceLeadPayload) {
    return this._privateGateway.post<CreateSalesforceLeadData, CreateSalesforceLeadResponse>(
      Endpoints.SALESFORCE_CREATE_RECORD_LEAD,
      {
        action: paragonActions.SALESFORCE_CREATE_RECORD_LEAD,
        parameters: { ...payload },
      },
    )
  }
  deleteSalesforceLead(payload: DeleteSalesforceLeadPayload) {
    return this._privateGateway.post<DeleteSalesforceLeadData, DeleteSalesforceLeadResponse>(
      Endpoints.SALESFORCE_DELETE_RECORD_LEAD,
      {
        action: paragonActions.SALESFORCE_DELETE_RECORD_LEAD,
        parameters: { ...payload },
      },
    )
  }
  updateSalesforceLead(payload: UpdateSalesforceLeadPayload) {
    return this._privateGateway.post<UpdateSalesforceLeadData, UpdateSalesforceLeadResponse>(
      Endpoints.SALESFORCE_UPDATE_RECORD_LEAD,
      {
        action: paragonActions.SALESFORCE_UPDATE_RECORD_LEAD,
        parameters: { ...payload },
      },
    )
  }
  getSalesforceLeadById(payload: GetSalesforceLeadByIdPayload) {
    return this._privateGateway.post<GetSalesforceLeadByIdData, GetSalesforceLeadByIdResponse>(
      Endpoints.SALESFORCE_GET_RECORD_BY_ID_LEAD,
      {
        action: paragonActions.SALESFORCE_GET_RECORD_BY_ID_LEAD,
        parameters: { ...payload },
      },
    )
  }
}
