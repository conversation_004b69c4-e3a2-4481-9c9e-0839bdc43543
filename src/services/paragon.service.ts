/* eslint-disable @typescript-eslint/no-explicit-any */

import { paragon } from '@useparagon/connect'

import { createClient } from '@/utils/supabase/client'

const supabase = createClient()

export class ParagonAuthManager {
  private organizationId: string
  private token: string | null = null
  private expiresAt: Date | null = null
  private projectId: string

  constructor(organizationId: string) {
    this.organizationId = organizationId
    this.projectId = process.env.NEXT_PUBLIC_PARAGON_PROJECT_ID || ''

    if (!this.projectId) {
      console.warn('Paragon Project ID not configured')
    }
  }

  /**
   * Get a valid token, refreshing if necessary
   */
  async getToken(): Promise<string> {
    // Check if we have a valid token already
    if (this.token && this.expiresAt && new Date() < this.expiresAt) {
      return this.token
    }

    // Otherwise, fetch a new token
    return this.refreshToken()
  }

  /**
   * Force refresh the token
   */
  async refreshToken(): Promise<string> {
    try {
      const { data, error } = await supabase.functions.invoke(
        `organizations/${this.organizationId}/crm/paragon-token`,
        {
          method: 'GET',
        },
      )

      if (error) throw error

      this.token = data.token
      this.expiresAt = new Date(data.expires_at)

      return data.token
    } catch (error) {
      console.error('Failed to refresh Paragon token:', error)
      throw new Error('Failed to get Paragon authentication token')
    }
  }

  /**
   * Authenticate with Paragon
   */
  async authenticate(): Promise<void> {
    if (!this.projectId) {
      throw new Error('Paragon Project ID not configured')
    }

    const token = await this.getToken()
    await paragon.authenticate(this.projectId, token)
  }

  /**
   * Connect to a CRM provider
   */
  async connectCRM(crmType: App.CRMType, options = {}): Promise<any> {
    await this.authenticate()
    return paragon.connect(crmType, options)
  }
}

// Singleton cache of ParagonAuthManager instances by organization ID
const paragonManagerCache: Record<string, ParagonAuthManager> = {}

export const getParagonManager = (organizationId: string): ParagonAuthManager => {
  if (!paragonManagerCache[organizationId]) {
    paragonManagerCache[organizationId] = new ParagonAuthManager(organizationId)
  }
  return paragonManagerCache[organizationId]
}

// Convenience functions
export const paragonService = {
  authenticate: async (organizationId: string): Promise<void> => {
    const manager = getParagonManager(organizationId)
    return manager.authenticate()
  },

  connectCRM: async (organizationId: string, crmType: App.CRMType, options = {}): Promise<any> => {
    const manager = getParagonManager(organizationId)
    return manager.connectCRM(crmType, options)
  },
  getToken: async (organizationId: string): Promise<string> => {
    const manager = getParagonManager(organizationId)
    return manager.getToken()
  },
}
