import { createClient } from '@/utils/supabase/client'

const supabase = createClient()

export const contactService = {
  async getOrganizationContacts(organizationId: string): Promise<App.CachedCRMContact[]> {
    const { data: connections } = await supabase
      .from('crm_connections')
      .select('id')
      .eq('organization_id', organizationId)

    if (!connections?.length) return []

    const { data, error } = await supabase
      .from('cached_crm_contacts')
      .select('*')
      .in(
        'crm_connection_id',
        connections.map(c => c.id),
      )

    if (error) throw error
    return data
  },

  async createContact(
    connectionId: string,
    contactData: {
      first_name: string
      last_name: string
      email: string
      phone?: string
      properties?: Record<string, unknown>
    },
  ): Promise<App.CachedCRMContact> {
    const { data, error } = await supabase.functions.invoke(`crm/${connectionId}/contacts`, {
      method: 'POST',
      body: JSON.stringify(contactData),
    })

    if (error) throw error
    return data
  },

  async updateContact(
    connectionId: string,
    contactId: string,
    contactData: {
      first_name: string
      last_name: string
      email: string
      phone?: string
      properties?: Record<string, unknown>
    },
  ): Promise<App.CachedCRMContact> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${connectionId}/contacts/${contactId}`,
      {
        method: 'PATCH',
        body: JSON.stringify(contactData),
      },
    )

    if (error) throw error
    return data
  },

  async deleteContact(connectionId: string, contactId: string): Promise<void> {
    const { error } = await supabase.functions.invoke(`crm/${connectionId}/contacts/${contactId}`, {
      method: 'DELETE',
    })

    if (error) throw error
  },
  async getContactAssociatedCompanies(
    connectionId: string,
    contactId: string,
  ): Promise<App.CachedCRMCompany[]> {
    const { data, error } = await supabase.functions.invoke(
      `crm/${connectionId}/contacts/${contactId}/companies`,
      {
        method: 'GET',
      },
    )

    if (error) throw error
    return data
  },
}
