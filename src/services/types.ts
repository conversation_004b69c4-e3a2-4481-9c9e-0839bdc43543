export type CreateOrganizationFormData = {
  name: string
  type: App.OrganizationType
  company_size: App.CompanySize | null
}
export type UpdateOrganizationFormData = Partial<App.Organization>

export type UpdateProfilePayload = {
  id: string
  data: Pick<App.Profile, 'name' | 'avatar'>
}

export type UpdatePasswordPayload = {
  currentPassword: string
  newPassword: string
  email: string
}

export type CustomFile =
  | File
  | {
      url: string
    }

export type CreateCompanyData = {
  connectionId: string
  companyData: Pick<
    App.CachedCRMCompany,
    | 'name'
    | 'domain'
    | 'industry'
    | 'type'
    | 'description'
    | 'linkedin_url'
    | 'annual_revenue'
    | 'employee_count'
    | 'phone'
    | 'street_address'
    | 'city'
    | 'state'
    | 'postal_code'
    | 'country'
    | 'timezone'
  >
}
export type UpdateCompanyData = CreateCompanyData & {
  companyId: string
}

export type ConnectCRMPayload = {
  crm_type: App.CRMType
  integration_id: string
  credential_id: string
  credentials: Record<string, unknown>
  provider_id: string
}

export type DisconnectCRMPayload = {
  crm_type: App.CRMType
  connection_id: string
}

export type AgentSignedUrlResponse = {
  signed_url: string
}

export type VoiceResponse = {
  voices: App.Voice[]
}

export type CreateKnowledgeBaseTextPayload = {
  organization_id: string
  name: string
  content: string
}

export type CreateKnowledgeBaseUrlPayload = {
  organization_id: string
  url: string
}

export type CreateKnowledgeBaseFilePayload = {
  organization_id: string
  file: File
}

export type KnowledgeBaseRagIndexStatusResponse = {
  status: 'created' | 'processing' | 'succeeded' | 'failed'
  progress_percentage: number
}
