import pick from 'lodash/pick'

import { createClient } from '@/utils/supabase/client'

import { CreateOrganizationFormData } from './types'

const supabase = createClient()

export interface OrganizationWithMemberInfo extends App.Organization {
  role: App.OrganizationRole
  member_count: number
}

export const organizationService = {
  async getUserOrganizations(userId: string): Promise<OrganizationWithMemberInfo[]> {
    const { data, error } = await supabase
      .from('organization_members')
      .select(
        `
        organization_id,
        role,
        organizations (
          id,
          name,
          logo_url,
          type,
          company_size,
          organization_members (count)
        )
      `,
      )
      .eq('user_id', userId)
      .eq('status', 'active')

    if (error) throw error

    type OrganizationResponse = {
      organization_id: string
      role: App.OrganizationRole
      organizations: {
        id: string
        name: string
        logo_url: string | null
        type: App.OrganizationType
        company_size: App.CompanySize | null
        organization_members: Array<{ count: number }>
      }
    }

    return (data as unknown as OrganizationResponse[]).map(item => ({
      ...pick(item.organizations, ['id', 'name', 'logo_url', 'type', 'company_size']),
      role: item.role,
      member_count: item.organizations.organization_members[0]?.count ?? 0,
    }))
  },
  async create(data: CreateOrganizationFormData) {
    const { data: organization, error } = await supabase
      .from('organizations')
      .insert(data)
      .select()
      .single()

    if (error) throw error

    // Add creator as owner
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) throw new Error('User not authenticated')

    const { error: memberError } = await supabase.from('organization_members').insert({
      organization_id: organization.id,
      user_id: user.user.id,
      role: 'owner',
      status: 'active',
    })

    if (memberError) throw memberError

    return organization
  },

  async update(id: string, data: Partial<CreateOrganizationFormData>) {
    const { data: organization, error } = await supabase
      .from('organizations')
      .update({
        name: data.name,
        type: data.type,
        company_size: data.company_size,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return organization
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('organizations')
      .update({ deleted_at: new Date().toISOString() })
      .eq('id', id)

    if (error) throw error
    return true
  },

  async get(id: string) {
    const { data: organization, error } = await supabase
      .from('organizations')
      .select(
        `
        *,
        organization_members (
          user_id,
          role,
          status
        )
      `,
      )
      .eq('id', id)
      .is('deleted_at', null)
      .single()

    if (error) throw error
    return organization
  },

  async getMembers(organizationId: string) {
    const { data, error } = await supabase
      .from('organization_members')
      .select(
        `
        *,
        user:user_id (
          id,
          email,
          name,
          avatar
        )
      `,
      )
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) throw error

    return data as App.OrganizationMember[]
  },

  async inviteMember(organizationId: string, { email, role }: { email: string; role: string }) {
    // Then trigger the invite email
    const { data: inviteData, error: inviteError } = await supabase.functions.invoke(
      'organization-invite',
      {
        body: JSON.stringify({ organizationId, email, role }),
        method: 'POST',
      },
    )

    if (inviteError) throw inviteError

    return inviteData
  },

  async removeMember(memberId: string) {
    const { error } = await supabase.from('organization_members').delete().eq('id', memberId)

    if (error) throw error
  },

  async updateMember(memberId: string, data: Partial<App.OrganizationMember>) {
    const { data: member, error } = await supabase
      .from('organization_members')
      .update(data)
      .eq('id', memberId)
      .select()
      .single()

    if (error) throw error

    return member
  },

  async cancelInvite(inviteId: string) {
    const { error } = await supabase.from('organization_invites').delete().eq('id', inviteId)

    if (error) throw error
  },

  async getInvites(organizationId: string) {
    const { data, error } = await supabase
      .from('organization_invites')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },
  async getInvite(inviteId: string) {
    const { data, error } = await supabase
      .from('organization_invites')
      .select('*, organization:organization_id (*)')
      .eq('id', inviteId)
      .single()

    if (error) throw error
    return data as App.OrganizationInvite
  },
  async acceptInvite(inviteId: string) {
    const { data: invite, error: inviteError } = await supabase
      .from('organization_invites')
      .update({ status: 'accepted' })
      .eq('id', inviteId)
      .select()
      .single()

    if (inviteError) throw inviteError

    const { error: memberError } = await supabase.from('organization_members').insert({
      organization_id: invite.organization_id,
      user_id: (await supabase.auth.getUser()).data.user?.id,
      role: invite.role,
    })

    if (memberError) throw memberError

    return invite
  },

  async getMemberRole(
    organizationId: string,
    userId: string,
  ): Promise<App.OrganizationRole | null> {
    const { data, error } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()

    if (error) throw error
    return data?.role ?? null
  },
}
