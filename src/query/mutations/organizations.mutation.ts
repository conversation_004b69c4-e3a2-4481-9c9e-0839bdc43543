import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

import { useRouter } from '@/i18n/navigation'
import { organizationService } from '@/services/organization.service'
import { CreateOrganizationFormData, UpdateOrganizationFormData } from '@/services/types'

import { QUERY_KEYS } from '../constants/query-keys'

export const useCreateOrganization = () => {
  const router = useRouter()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: CreateOrganizationFormData) => {
      return await organizationService.create(data)
    },
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATIONS] })
      router.push(`/organizations/${data.id}`)
      toast.success('Organization created successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to create organization')
    },
  })
}

export const useUpdateOrganization = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateOrganizationFormData }) => {
      return await organizationService.update(id, data)
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATIONS] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATIONS, id] })
      toast.success('Organization updated successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to update organization')
    },
  })
}

export const useDeleteOrganization = () => {
  const router = useRouter()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (id: string) => {
      return await organizationService.delete(id)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATIONS] })
      router.push('/organizations')
      toast.success('Organization deleted successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to delete organization')
    },
  })
}

export const useInviteOrganizationMember = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      organizationId,
      email,
      role,
    }: {
      organizationId: string
      email: string
      role: App.OrganizationRole
    }) => {
      return await organizationService.inviteMember(organizationId, { email, role })
    },
    onSuccess: (_, { organizationId }) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_INVITES, organizationId],
      })
      toast.success('Invitation sent successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to send invitation')
    },
  })
}

export const useRemoveOrganizationMember = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ memberId }: { memberId: string }) => {
      return await organizationService.removeMember(memberId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATION_MEMBERS] })
      toast.success('Member removed successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to remove member')
    },
  })
}

export const useUpdateOrganizationMember = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      memberId,
      data,
    }: {
      memberId: string
      data: Partial<App.OrganizationMember>
    }) => {
      return await organizationService.updateMember(memberId, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATION_MEMBERS] })
      toast.success('Member updated successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to update member')
    },
  })
}

export const useCancelOrganizationInvite = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ inviteId }: { inviteId: string }) => {
      return await organizationService.cancelInvite(inviteId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATION_INVITES] })
      toast.success('Invitation cancelled successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to cancel invitation')
    },
  })
}

export const useAcceptOrganizationInvite = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ inviteId }: { inviteId: string }) => {
      return await organizationService.acceptInvite(inviteId)
    },
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_MEMBERS, data.organization_id],
      })
      toast.success('Successfully joined organization')
    },
    onError: error => {
      toast.error(error.message || 'Failed to accept invitation')
    },
  })
}
