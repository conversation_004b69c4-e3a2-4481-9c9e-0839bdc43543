import { Provider } from '@supabase/supabase-js'
import { useMutation } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

import { useRouter } from '@/i18n/navigation'
import { auth } from '@/services/auth.service'
import { getAuthError } from '@/services/auth-error'
import { logger } from '@/utils/logger'

export const useSignIn = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirect_to')

  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      return await auth.signIn(email, password)
    },
    onSuccess: () => {
      if (redirectTo) {
        router.push(redirectTo)
      } else {
        router.push('/organizations')
      }
      router.refresh()
    },
    onError: error => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useSignUp = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirect_to')

  return useMutation({
    mutationFn: async ({
      email,
      password,
      name,
    }: {
      email: string
      password: string
      name: string
    }) => {
      return await auth.signUp(email, password, name)
    },
    onSuccess: async (data, { email }) => {
      // Check if email verification is required by checking email_verified status
      if (!data.user?.user_metadata?.email_verified) {
        router.push(
          `/verify?email=${encodeURIComponent(email)}${redirectTo ? `&redirect_to=${encodeURIComponent(redirectTo)}` : ''}`,
        )
      } else {
        // If no verification needed, redirect to specified path or default
        if (redirectTo) {
          router.push(redirectTo)
        } else {
          router.push('/login')
        }
        router.refresh()
      }
    },
    onError: (error: unknown) => {
      logger.error('🚀 ~ useSignUp ~ error:', error)
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useSignInWithOAuth = () => {
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirect_to')

  return useMutation({
    mutationFn: async ({ provider }: { provider: Provider }) => {
      return await auth.signInWithOAuth(provider, redirectTo || undefined)
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useSignOut = () => {
  return useMutation({
    mutationFn: async () => {
      return auth.signOut()
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useResetPasswordRequest = () => {
  return useMutation({
    mutationFn: async (email: string) => {
      return await auth.resetPasswordRequest(email)
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useResetPassword = () => {
  const router = useRouter()
  const t = useTranslations('auth')

  return useMutation({
    mutationFn: async (password: string) => {
      return await auth.resetPassword(password)
    },
    onSuccess: async () => {
      toast.success(t('resetPassword.success'))
      router.push('/organizations')
      router.refresh()
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useVerifyOtp = (redirectTo?: string) => {
  const router = useRouter()

  return useMutation({
    mutationFn: async ({ email, token }: { email: string; token: string }) => {
      return await auth.verifyOtp(email, token)
    },
    onSuccess: () => {
      toast.success('Email verified successfully!')
      router.push(redirectTo ?? '/organizations')
      router.refresh()
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}

export const useResendVerification = () => {
  return useMutation({
    mutationFn: async (email: string) => {
      return await auth.resendVerificationEmail(email)
    },
    onSuccess: () => {
      toast.success('Verification email has been resent')
    },
    onError: (error: unknown) => {
      const { message } = getAuthError(error)
      toast.error(message)
    },
  })
}
