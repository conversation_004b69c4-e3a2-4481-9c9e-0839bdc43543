import { useMutation, useQueryClient } from '@tanstack/react-query'

import { knowledgeBaseService } from '@/services/knowledge-base.service'
import {
  CreateKnowledgeBaseFilePayload,
  CreateKnowledgeBaseTextPayload,
  CreateKnowledgeBaseUrlPayload,
} from '@/services/types'

import { QUERY_KEYS } from '../constants/query-keys'

type ConnectAgentKnowledgeBasePayload = {
  agentId: string
  knowledgeBaseId: string
}

export const useDeleteKnowledgeBase = (organizationId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (knowledgeBaseId: string) => {
      // Get the knowledge base first to know its organization_id for cache invalidation
      await knowledgeBaseService.deleteKnowledgeBase(knowledgeBaseId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.KNOWLEDGE_BASES, organizationId] })
    },
  })
}

export const useConnectAgentToKnowledgeBase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ agentId, knowledgeBaseId }: ConnectAgentKnowledgeBasePayload) => {
      await knowledgeBaseService.connectAgentToKnowledgeBase(agentId, knowledgeBaseId)
      return { agentId, knowledgeBaseId }
    },
    onSuccess: ({ agentId }) => {
      queryClient.invalidateQueries({ queryKey: ['agent-knowledge-bases', agentId] })
    },
  })
}

export const useDisconnectAgentFromKnowledgeBase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ agentId, knowledgeBaseId }: ConnectAgentKnowledgeBasePayload) => {
      await knowledgeBaseService.disconnectAgentFromKnowledgeBase(agentId, knowledgeBaseId)
      return { agentId, knowledgeBaseId }
    },
    onSuccess: ({ agentId }) => {
      queryClient.invalidateQueries({ queryKey: ['agent-knowledge-bases', agentId] })
    },
  })
}

export const useCreateKnowledgeBaseText = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeBaseTextPayload) => {
      return knowledgeBaseService.createKnowledgeBaseText(payload)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASES, variables.organization_id],
      })
    },
  })
}
export const useCreateKnowledgeBaseUrl = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeBaseUrlPayload) => {
      return knowledgeBaseService.createKnowledgeBaseUrl(payload)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASES, variables.organization_id],
      })
    },
  })
}

export const useCreateKnowledgeBaseFile = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeBaseFilePayload) => {
      return knowledgeBaseService.createKnowledgeBaseFile(payload)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.KNOWLEDGE_BASES, variables.organization_id],
      })
    },
  })
}

export const useKnowledgeBaseRagIndexStatus = () => {
  return useMutation({
    mutationFn: async (knowledgeBaseId: string) => {
      return knowledgeBaseService.knowledgeBaseRagIndexStatus(knowledgeBaseId)
    },
  })
}
