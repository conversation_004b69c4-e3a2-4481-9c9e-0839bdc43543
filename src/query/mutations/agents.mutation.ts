import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

import { agentService } from '@/services/agent.service'
import { UpdateAgentPayload } from '@/services/api/types'

import { QUERY_KEYS } from '../constants/query-keys'

// Mutation hook for creating a new agent
export function useCreateAgent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: {
      organization_id: string
      // Use Partial<Omit<...>> matching the service method
      agentData: Partial<Omit<App.Agent, 'agent_id' | 'created_at' | 'last_updated'>>
    }) => {
      const { organization_id, agentData } = data
      return agentService.createAgent(organization_id, agentData)
    },
    onSuccess: (newAgent, variables) => {
      // Invalidate the list of agents for the organization
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_AGENTS, variables.organization_id],
      })
      toast.success(`Agent "${newAgent.agent_name}" created successfully.`)
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create agent.')
    },
  })
}

// Mutation hook for deleting an agent
export function useDeleteAgentMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      organizationId,
      agentId,
    }: {
      organizationId: string
      agentId: string
    }) => {
      return agentService.deleteAgent(organizationId, agentId)
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries after successful deletion
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_AGENTS, variables.organizationId],
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_TEMPLATES, variables.organizationId],
      })
      // Also invalidate the specific agent's details query, although the page might redirect
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AGENT_DETAILS, variables.organizationId, variables.agentId],
      })
      toast.success(`Agent deleted successfully.`) // Generic message as agent name might not be available
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete agent.')
    },
  })
}

// Mutation hook for updating an existing agent
export function useUpdateAgent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: {
      organization_id: string
      agent_id: string
      agentData: UpdateAgentPayload
    }) => {
      const { organization_id, agent_id, agentData } = data
      return agentService.updateAgent(organization_id, agent_id, agentData)
    },
    onSuccess: (updatedAgent, variables) => {
      // Invalidate the list of agents
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_AGENTS, variables.organization_id],
      })
      // Invalidate the specific agent details cache
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AGENT_DETAILS, variables.organization_id, variables.agent_id],
      })
      toast.success(`Agent "${updatedAgent.agent_name}" updated successfully.`)
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update agent.')
    },
  })
}

// Mutation hook for updating the template status of an agent
export function useUpdateAgentTemplateStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      organizationId,
      agentId,
      isTemplate,
    }: {
      organizationId: string
      agentId: string
      isTemplate: boolean
    }) => {
      return agentService.updateAgentTemplateStatus(organizationId, agentId, isTemplate)
    },
    onSuccess: (updatedAgent, variables) => {
      // Invalidate lists and details
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_AGENTS, variables.organizationId],
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_TEMPLATES, variables.organizationId],
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.AGENT_DETAILS, variables.organizationId, variables.agentId],
      })
      toast.success(
        `Agent "${updatedAgent.agent_name}" marked as ${variables.isTemplate ? 'template' : 'not a template'}.`,
      )
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update agent template status.')
    },
  })
}

export function useGetAgentSignedUrl() {
  return useMutation({
    mutationFn: async (agentId: string) => {
      return agentService.getAgentSignedUrl(agentId)
    },
  })
}
