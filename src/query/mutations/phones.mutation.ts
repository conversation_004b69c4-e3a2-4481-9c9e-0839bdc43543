import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

import { phoneService } from '@/services/phone.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useAddPhone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: {
      organization_id: string
      phone_number: string
      label: string
      provider: App.PhoneProvider
      sid?: string
      token?: string
      termination_uri?: string
      credentials?: {
        username?: string
        password?: string
      }
    }) => {
      const { organization_id, ...phoneData } = data
      return phoneService.addPhone(organization_id, phoneData)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_PHONES, variables.organization_id],
      })
    },
  })
}

export function useDeletePhone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      organizationId,
      phoneId,
    }: {
      organizationId: string
      phoneId: string
    }) => {
      return phoneService.deletePhone(organizationId, phoneId)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_PHONES, variables.organizationId],
      })
    },
    onError: error => {
      toast.error(error.message || 'Failed to delete phone')
    },
  })
}
