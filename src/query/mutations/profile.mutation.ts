import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

import { auth } from '@/services/auth.service'
import { UpdatePasswordPayload, UpdateProfilePayload } from '@/services/types'
import { useGetProfile, useSetProfile } from '@/stores/root.store'

export const useUpdateProfile = () => {
  const setProfile = useSetProfile()
  const profile = useGetProfile()

  return useMutation({
    mutationFn: async (payload: UpdateProfilePayload) => {
      return await auth.updateProfile(payload)
    },
    onSuccess: (_, payload) => {
      if (!profile) return
      setProfile({ ...profile, ...payload.data })
      toast.success('Profile updated successfully')
    },
  })
}

export const useUpdatePassword = () => {
  return useMutation({
    mutationFn: async (payload: UpdatePasswordPayload) => {
      return await auth.updatePassword(payload)
    },
    onSuccess: () => {
      toast.success('Password updated successfully')
    },
    onError: error => {
      toast.error(error.message || 'Failed to update password')
    },
  })
}
