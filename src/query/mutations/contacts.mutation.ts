import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

import { contactService } from '@/services/contact.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useCreateContact(organizationId: string) {
  const t = useTranslations('Organizations.contacts')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      connectionId,
      contactData,
    }: {
      connectionId: string
      contactData: {
        first_name: string
        last_name: string
        email: string
        phone?: string
      }
    }) => {
      return contactService.createContact(connectionId, contactData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CONTACTS, organizationId],
      })
      toast.success(t('success.created'))
    },
    onError: error => {
      toast.error(error.message || t('errors.createFailed'))
    },
  })
}

export function useUpdateContact(organizationId: string) {
  const t = useTranslations('Organizations.contacts')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      connectionId,
      contactId,
      contactData,
    }: {
      connectionId: string
      contactId: string
      contactData: {
        first_name: string
        last_name: string
        email: string
        phone?: string
      }
    }) => {
      return contactService.updateContact(connectionId, contactId, contactData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CONTACTS, organizationId],
      })
      toast.success(t('success.updated'))
    },
    onError: error => {
      toast.error(error.message || t('errors.updateFailed'))
    },
  })
}

export function useDeleteContact(organizationId: string) {
  const t = useTranslations('Organizations.contacts')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      connectionId,
      contactId,
    }: {
      connectionId: string
      contactId: string
    }) => {
      return contactService.deleteContact(connectionId, contactId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CONTACTS, organizationId],
      })
      toast.success(t('success.deleted'))
    },
    onError: error => {
      toast.error(error.message || t('errors.deleteFailed'))
    },
  })
}
