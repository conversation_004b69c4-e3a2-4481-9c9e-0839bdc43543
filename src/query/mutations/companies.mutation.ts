import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

import { companyService } from '@/services/company.service'
import { CreateCompanyData, UpdateCompanyData } from '@/services/types'

import { QUERY_KEYS } from '../constants/query-keys'

interface DeleteCompanyData {
  connectionId: string
  companyId: string
}

export function useCreateCompany(organizationId: string) {
  const t = useTranslations('Organizations.companies')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateCompanyData) => {
      return companyService.createCompany(payload)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_COMPANIES, organizationId],
      })
      toast.success(t('success.created'))
    },
    onError: error => {
      toast.error(error.message || t('errors.createFailed'))
    },
  })
}

export function useUpdateCompany(organizationId: string) {
  const t = useTranslations('Organizations.companies')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: UpdateCompanyData) => {
      return companyService.updateCompany(payload)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_COMPANIES, organizationId],
      })
      toast.success(t('success.updated'))
    },
    onError: error => {
      toast.error(error.message || t('errors.updateFailed'))
    },
  })
}

export function useDeleteCompany(organizationId: string) {
  const t = useTranslations('Organizations.companies')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ connectionId, companyId }: DeleteCompanyData) => {
      return companyService.deleteCompany(connectionId, companyId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_COMPANIES, organizationId],
      })
      toast.success(t('success.deleted'))
    },
    onError: error => {
      toast.error(error.message || t('errors.deleteFailed'))
    },
  })
}
