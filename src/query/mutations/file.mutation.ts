import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

import { auth } from '@/services/auth.service'

export const useUploadAvatar = (userId: string) => {
  return useMutation({
    mutationFn: async (file: File) => {
      return await auth.uploadAvatar(file, userId)
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to upload avatar')
    },
  })
}
