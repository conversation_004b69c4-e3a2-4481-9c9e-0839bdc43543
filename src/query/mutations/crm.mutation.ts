import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

import { Api } from '@/services/api'
import {
  CreateSalesforceAccountPayload,
  CreateSalesforceContactPayload,
  CreateSalesforceLeadPayload,
  DeleteSalesforceAccountPayload,
  DeleteSalesforceContactPayload,
  DeleteSalesforceLeadPayload,
  GetSalesforceAccountByIdPayload,
  GetSalesforceAccountsData,
  GetSalesforceAccountsParameters,
  GetSalesforceContactByIdPayload,
  GetSalesforceContactsData,
  GetSalesforceLeadByIdPayload,
  GetSalesforceLeadsData,
  GetSalesforceLeadsParameters,
  UpdateSalesforceAccountPayload,
  UpdateSalesforceContactPayload,
  UpdateSalesforceLeadPayload,
} from '@/services/api/types'
import { crmService } from '@/services/crm.service'
import { ConnectCRMPayload } from '@/services/types'
import { createClient } from '@/utils/supabase/client'

import { QUERY_KEYS } from '../constants/query-keys'

const supabase = createClient()

export const useGetHubSpotAuthUrl = () => {
  return useMutation({
    mutationFn: async () => {
      return await crmService.getHubSpotAuthUrl()
    },
    onSuccess: data => {
      window.location.href = data.url
    },
    onError: error => {
      toast.error(error.message || 'Failed to get HubSpot authorization URL')
    },
  })
}

export const useConnectHubSpot = (organizationId: string) => {
  const t = useTranslations('Settings.crm')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (code: string) => {
      return await crmService.connectHubSpot(organizationId, code)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
      })
      toast.success(t('hubspot.connectSuccess'))
    },
    onError: error => {
      toast.error(error.message || t('hubspot.connectError'))
    },
  })
}

export const useGetParagonToken = (organizationId: string) => {
  return useMutation({
    mutationFn: async () => {
      return await crmService.getParagonToken(organizationId)
    },
    onError: error => {
      toast.error(error.message || 'Failed to get Paragon token')
    },
  })
}

export const useConnectCRM = (organizationId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: ConnectCRMPayload) => {
      return await crmService.connectCRM(organizationId, payload)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
      })
    },
  })
}

// export const useDeleteCRMConnection = (organizationId: string) => {
//   const t = useTranslations('Settings.crm')
//   const queryClient = useQueryClient()

//   return useMutation({
//     mutationFn: async (payload: DisconnectCRMPayload) => {
//       return await crmService.disconnectCRM(organizationId, payload)
//     },
//     onSuccess: (_, payload) => {
//       queryClient.invalidateQueries({
//         queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
//       })
//       toast.success(t('success.disconnected', { type: t(`types.${payload.crm_type}`) }))
//     },
//     onError: error => {
//       toast.error(error.message || t('errors.disconnectionFailed'))
//     },
//   })
// }

export function useSyncCRMData(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ connectionId }: { connectionId: string }) => {
      const { data, error } = await supabase.functions.invoke(`/crm/${connectionId}/sync`, {
        method: 'POST',
      })

      if (error) throw error

      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
      })
    },
  })
}

export const useCreateSalesforceAccount = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateSalesforceAccountPayload) => {
      return await Api.instance.createSalesforceAccount(payload).then(res => res.data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SALESFORCE_ACCOUNTS, organizationId, parameters],
      })
    },
  })
}

export const useUpdateSalesforceAccount = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: UpdateSalesforceAccountPayload) => {
      return await Api.instance.updateSalesforceAccount(payload).then(res => res.data)
    },
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_ACCOUNTS, organizationId, parameters],
        (oldData: GetSalesforceAccountsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.map(account => {
                if (account.Id === variables.recordId) {
                  const { Name, Website, Phone, Description, additionalFields } = variables
                  return {
                    ...account,
                    Name,
                    Website,
                    Description,
                    Phone,
                    ...additionalFields,
                  }
                }
                return account
              }),
            },
          }
        },
      )
    },
  })
}

export const useDeleteSalesforceAccount = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: DeleteSalesforceAccountPayload) => {
      return await Api.instance.deleteSalesforceAccount(payload).then(res => res.data)
    },
    onSuccess: (_, { recordId }) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_ACCOUNTS, organizationId, parameters],
        (oldData: GetSalesforceAccountsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.filter(account => account.Id !== recordId),
            },
          }
        },
      )
    },
  })
}

export const useGetSalesforceAccountById = () => {
  return useMutation({
    mutationFn: async (payload: GetSalesforceAccountByIdPayload) => {
      return await Api.instance.getSalesforceAccountById(payload).then(res => res.data)
    },
  })
}

export const useCreateSalesforceContact = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateSalesforceContactPayload) => {
      return await Api.instance.createSalesforceContact(payload).then(res => res.data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SALESFORCE_CONTACTS, organizationId, parameters],
      })
    },
  })
}

export const useUpdateSalesforceContact = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: UpdateSalesforceContactPayload) => {
      return await Api.instance.updateSalesforceContact(payload).then(res => res.data)
    },
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_CONTACTS, organizationId, parameters],
        (oldData: GetSalesforceContactsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.map(contact => {
                if (contact.Id === variables.recordId) {
                  const { Description, Email, FirstName, LastName, Title, additionalFields } =
                    variables

                  return {
                    ...contact,
                    Description,
                    Email,
                    FirstName,
                    LastName,
                    Title,
                    Name: `${FirstName} ${LastName}`,
                    ...additionalFields,
                  }
                }
                return contact
              }),
            },
          }
        },
      )
    },
  })
}

export const useDeleteSalesforceContact = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: DeleteSalesforceContactPayload) => {
      return await Api.instance.deleteSalesforceContact(payload).then(res => res.data)
    },
    onSuccess: (_, { recordId }) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_CONTACTS, organizationId, parameters],
        (oldData: GetSalesforceContactsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.filter(contact => contact.Id !== recordId),
            },
          }
        },
      )
    },
  })
}

export const useGetSalesforceContactById = () => {
  return useMutation({
    mutationFn: async (payload: GetSalesforceContactByIdPayload) => {
      return await Api.instance.getSalesforceContactById(payload).then(res => res.data)
    },
  })
}

export const useCreateSalesforceLead = (
  organizationId: string,
  parameters: GetSalesforceLeadsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: CreateSalesforceLeadPayload) => {
      return await Api.instance.createSalesforceLead(payload).then(res => res.data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SALESFORCE_LEADS, organizationId, parameters],
      })
    },
  })
}

export const useUpdateSalesforceLead = (
  organizationId: string,
  parameters: GetSalesforceLeadsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: UpdateSalesforceLeadPayload) => {
      return await Api.instance.updateSalesforceLead(payload).then(res => res.data)
    },
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_LEADS, organizationId, parameters],
        (oldData: GetSalesforceLeadsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.map(lead => {
                if (lead.Id === variables.recordId) {
                  const {
                    LastName,
                    Company,
                    FirstName,
                    Email,
                    Phone,
                    Website,
                    Title,
                    Status,
                    Description,
                    additionalFields,
                  } = variables

                  return {
                    ...lead,
                    LastName,
                    Company,
                    FirstName,
                    Email,
                    Phone,
                    Website,
                    Title,
                    Status,
                    Description,
                    ...additionalFields,
                  }
                }
                return lead
              }),
            },
          }
        },
      )
    },
  })
}

export const useDeleteSalesforceLead = (
  organizationId: string,
  parameters: GetSalesforceLeadsParameters,
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: DeleteSalesforceLeadPayload) => {
      return await Api.instance.deleteSalesforceLead(payload).then(res => res.data)
    },
    onSuccess: (_, { recordId }) => {
      queryClient.setQueryData(
        [QUERY_KEYS.SALESFORCE_LEADS, organizationId, parameters],
        (oldData: GetSalesforceLeadsData) => {
          return {
            ...oldData,
            records: {
              ...oldData.records,
              records: oldData.records.records.filter(lead => lead.Id !== recordId),
            },
          }
        },
      )
    },
  })
}

export const useGetSalesforceLeadById = () => {
  return useMutation({
    mutationFn: async (payload: GetSalesforceLeadByIdPayload) => {
      return await Api.instance.getSalesforceLeadById(payload).then(res => res.data)
    },
  })
}

export const useDeleteCRMConnection = (organizationId: string) => {
  const t = useTranslations('Settings.crm')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (connectionId: string) => {
      return await crmService.deleteCRMConnection(connectionId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
      })
      toast.success(t('hubspot.disconnectSuccess'))
    },
    onError: error => {
      toast.error(error.message || t('hubspot.disconnectError'))
    },
  })
}

// Add deal-related mutation hooks
export const useCreateDeal = (organizationId: string) => {
  const t = useTranslations('Organizations.deals')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      connectionId,
      dealData,
    }: {
      connectionId: string
      dealData: {
        name: string
        amount?: number
        pipeline?: string
        stage?: string
        close_date?: string
        priority?: string
        type?: string
        properties?: Record<string, unknown>
      }
    }) => {
      return crmService.createDeal(connectionId, dealData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_DEALS, organizationId],
      })
      toast.success(t('success.created'))
    },
    onError: error => {
      toast.error(error.message || t('errors.createFailed'))
    },
  })
}

export const useUpdateDeal = (organizationId: string) => {
  const t = useTranslations('Organizations.deals')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      connectionId,
      dealId,
      dealData,
    }: {
      connectionId: string
      dealId: string
      dealData: {
        name: string
        amount?: number
        pipeline?: string
        stage?: string
        close_date?: string
        priority?: string
        type?: string
        properties?: Record<string, unknown>
      }
    }) => {
      return crmService.updateDeal(connectionId, dealId, dealData)
    },
    onSuccess: (_, { dealId }) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_DEALS, organizationId],
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DEAL_ASSOCIATED_CONTACTS, , dealId],
      })
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.DEAL_ASSOCIATED_COMPANIES, , dealId],
      })
      toast.success(t('success.updated'))
    },
    onError: error => {
      toast.error(error.message || t('errors.updateFailed'))
    },
  })
}

export const useDeleteDeal = (organizationId: string) => {
  const t = useTranslations('Organizations.deals')
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ connectionId, dealId }: { connectionId: string; dealId: string }) => {
      return crmService.deleteDeal(connectionId, dealId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION_CRM_DEALS, organizationId],
      })
      toast.success(t('success.deleted'))
    },
    onError: error => {
      toast.error(error.message || t('errors.deleteFailed'))
    },
  })
}
