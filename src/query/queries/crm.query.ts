import { useQuery, UseQueryOptions } from '@tanstack/react-query'

import { Api } from '@/services/api'
import {
  GetSalesforceAccountsData,
  GetSalesforceAccountsParameters,
  GetSalesforceContactsData,
  GetSalesforceLeadsData,
  GetSalesforceLeadsParameters,
} from '@/services/api/types'
import { crmService } from '@/services/crm.service'

import { QUERY_KEYS } from '../constants/query-keys'

export const useGetOrganizationCRMConnections = (organizationId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_CRM_CONNECTIONS, organizationId],
    queryFn: () => crmService.getOrganizationCRMConnections(organizationId),
    enabled: !!organizationId,
  })
}

export const useGetIndustries = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.INDUSTRIES],
    queryFn: () => crmService.getIndustries(),
  })
}

export const useGetSalesforceAccounts = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
  options?: Omit<UseQueryOptions<GetSalesforceAccountsData>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SALESFORCE_ACCOUNTS, organizationId, parameters],
    queryFn: () => Api.instance.getSalesforceAccounts(parameters).then(res => res.data),
    ...options,
  })
}

export const useGetSalesforceContacts = (
  organizationId: string,
  parameters: GetSalesforceAccountsParameters,
  options?: Omit<UseQueryOptions<GetSalesforceContactsData>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SALESFORCE_CONTACTS, organizationId, parameters],
    queryFn: () => Api.instance.getSalesforceContacts(parameters).then(res => res.data),
    ...options,
  })
}

export const useGetSalesforceLeads = (
  organizationId: string,
  parameters: GetSalesforceLeadsParameters,
  options?: Omit<UseQueryOptions<GetSalesforceLeadsData>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SALESFORCE_LEADS, organizationId, parameters],
    queryFn: () => Api.instance.getSalesforceLeads(parameters).then(res => res.data),
    ...options,
  })
}

// Add deal-related query hooks
export const useGetOrganizationCRMDeals = (
  organizationId: string,
  options?: Omit<UseQueryOptions<App.CachedCRMDeal[]>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_CRM_DEALS, organizationId],
    queryFn: () => crmService.getOrganizationCRMDeals(organizationId),
    enabled: !!organizationId,
    ...options,
  })
}

export const useGetDealAssociatedContacts = (
  connectionId: string,
  dealId: string,
  options?: Omit<UseQueryOptions<App.CachedCRMContact[]>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DEAL_ASSOCIATED_CONTACTS, connectionId, dealId],
    queryFn: () => crmService.getDealAssociatedContacts(connectionId, dealId),
    enabled: !!connectionId && !!dealId,
    ...options,
  })
}

export const useGetDealAssociatedCompanies = (
  connectionId: string,
  dealId: string,
  options?: Omit<UseQueryOptions<App.CachedCRMCompany[]>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DEAL_ASSOCIATED_COMPANIES, connectionId, dealId],
    queryFn: () => crmService.getDealAssociatedCompanies(connectionId, dealId),
    enabled: !!connectionId && !!dealId,
    ...options,
  })
}
