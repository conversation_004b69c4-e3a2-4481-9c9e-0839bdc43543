import { useQuery, UseQueryOptions } from '@tanstack/react-query'

import { knowledgeBaseService } from '@/services/knowledge-base.service'

import { QUERY_KEYS } from '../constants/query-keys'

export const useGetKnowledgeBases = (
  organizationId: string,
  options?: Omit<UseQueryOptions<App.KnowledgeBase[]>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.KNOWLEDGE_BASES, organizationId],
    queryFn: async (): Promise<App.KnowledgeBase[]> => {
      return knowledgeBaseService.getOrganizationKnowledgeBases(organizationId)
    },
    ...options,
  })
}
