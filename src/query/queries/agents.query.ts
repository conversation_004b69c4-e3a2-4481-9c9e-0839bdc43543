import { useQuery } from '@tanstack/react-query'

import { agentService } from '@/services/agent.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useGetOrganizationAgents(organizationId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_AGENTS, organizationId],
    queryFn: () => agentService.getOrganizationAgents(organizationId),
    enabled: !!organizationId,
  })
}

export function useGetOrganizationTemplates(organizationId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_TEMPLATES, organizationId],
    queryFn: () => agentService.getOrganizationTemplates(organizationId),
    enabled: !!organizationId,
  })
}

export function useGetAgent(organizationId: string, agentId: string | null) {
  return useQuery({
    queryKey: [QUERY_KEYS.AGENT_DETAILS, organizationId, agentId],
    queryFn: () => {
      if (!agentId) {
        return Promise.resolve(null) // Return null if agentId is not provided
      }
      return agentService.getAgent(organizationId, agentId)
    },
    enabled: !!organizationId && !!agentId, // Only enable if both IDs are present
  })
}

export function useGetVoices() {
  return useQuery({
    queryKey: [QUERY_KEYS.VOICES],
    queryFn: () => agentService.getVoices(),
  })
}
