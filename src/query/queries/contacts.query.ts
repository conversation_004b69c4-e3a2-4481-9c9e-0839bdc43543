import { useQuery, UseQueryOptions } from '@tanstack/react-query'

import { contactService } from '@/services/contact.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useGetOrganizationContacts(
  organizationId: string,
  options?: Omit<UseQueryOptions<App.CachedCRMContact[]>, 'queryKey' | 'queryFn'>,
) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_CONTACTS, organizationId],
    queryFn: () => contactService.getOrganizationContacts(organizationId),
    ...options,
  })
}

export function useGetContactAssociatedCompanies(connectionId: string, contactId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.CONTACT_ASSOCIATED_COMPANIES, connectionId, contactId],
    queryFn: () => contactService.getContactAssociatedCompanies(connectionId, contactId),
  })
}
