import { useQuery } from '@tanstack/react-query'

import { phoneService } from '@/services/phone.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useGetOrganizationPhones(organizationId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_PHONES, organizationId],
    queryFn: () => phoneService.getOrganizationPhones(organizationId),
    enabled: !!organizationId,
  })
}
