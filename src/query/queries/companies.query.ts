import { useQuery, UseQueryOptions } from '@tanstack/react-query'

import { Api } from '@/services/api'
import { companyService } from '@/services/company.service'

import { QUERY_KEYS } from '../constants/query-keys'

export function useOrganizationHubSpotCompanies(
  organizationId: string,
  options?: Omit<UseQueryOptions<App.ParagonHubSpotCompany[]>, 'queryKey' | 'queryFn'>,
) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_COMPANIES, organizationId],
    queryFn: () => Api.instance.getHubSpotCompanies().then(res => res.data.records),
    ...options,
  })
}
export function useOrganizationCompanies(organizationId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_COMPANIES, organizationId],
    queryFn: () => companyService.getOrganizationCompanies(organizationId),
  })
}

export function useGetCompanyAssociatedContacts(connectionId: string, companyId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_ASSOCIATED_CONTACTS, connectionId, companyId],
    queryFn: () => companyService.getCompanyAssociatedContacts(connectionId, companyId),
  })
}
