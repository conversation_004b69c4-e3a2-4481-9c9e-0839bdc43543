import { useQuery } from '@tanstack/react-query'

import {
  organizationService,
  type OrganizationWithMemberInfo,
} from '@/services/organization.service'
import { useGetUser } from '@/stores/root.store'

import { QUERY_KEYS } from '../constants/query-keys'

export const useGetUserOrganizations = () => {
  const user = useGetUser()
  const userId = user?.id

  return useQuery<OrganizationWithMemberInfo[]>({
    queryKey: [QUERY_KEYS.ORGANIZATIONS],
    queryFn: () => organizationService.getUserOrganizations(userId!),
    enabled: !!userId,
  })
}

export function useGetOrganization(id: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATIONS, id],
    queryFn: () => organizationService.get(id),
    enabled: !!id,
  })
}

export const useGetOrganizationMembers = (organizationId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_MEMBERS, organizationId],
    queryFn: () => organizationService.getMembers(organizationId),
  })
}

export const useGetOrganizationInvites = (organizationId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_INVITES, organizationId],
    queryFn: () => organizationService.getInvites(organizationId),
  })
}

export const useGetOrganizationInvite = (inviteId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ORGANIZATION_INVITE, inviteId],
    queryFn: () => organizationService.getInvite(inviteId),
  })
}

export const useGetMemberRole = (organizationId: string, userId: string | undefined) => {
  return useQuery<App.OrganizationRole | null>({
    queryKey: [QUERY_KEYS.ORGANIZATION_MEMBER_ROLE, organizationId, userId],
    queryFn: () => organizationService.getMemberRole(organizationId, userId!),
    enabled: !!organizationId && !!userId,
  })
}
