import { useOrganizations } from '@/providers/organizations-provider'

type Permission =
  | 'canManageTeam'
  | 'canManageSettings'
  | 'canDeleteOrganization'
  | 'canInviteMembers'
  | 'canManagePhones'
  | 'canManageAgents'
  | 'canManageOrganization'
  | 'canManageContacts'
  | 'canManageCompanies'
  | 'canManageSalesforceAccounts'
  | 'canManageHubSpotContacts' // Add hubspot contacts permission
  | 'canManageSalesforceContacts' // Add salesforce contacts permission
  | 'canManageSalesforceLeads' // Add salesforce leads permission
  | 'canManageKnowledgeBase'
  | 'canManageDeals'

export function useOrganizationPermissions() {
  const { currentUserRole } = useOrganizations()

  const checkPermission = (permission: Permission): boolean => {
    // If no role is set, deny all permissions
    if (!currentUserRole) return false

    switch (permission) {
      case 'canManageTeam':
        // Only owners and admins can manage team
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canManageSettings':
        // Only owners and admins can manage settings
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canDeleteOrganization':
        // Only owners can delete organization
        return currentUserRole === 'owner'
      case 'canInviteMembers':
        // Only owners and admins can invite members
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canManagePhones':
        // Only owners and admins can manage phones
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canManageAgents':
        // Only owners and admins can manage agents (assuming same logic)
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canManageOrganization':
        // Only owners and admins can manage organization
        return ['owner', 'admin'].includes(currentUserRole)
      case 'canManageContacts':
        // Only owners and admins can manage contacts
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageCompanies': // Only owners and admins can manage companies
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageSalesforceAccounts': // Only owners and admins can manage salesforce accounts
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageHubSpotContacts': // Only owners and admins can manage hubspot contacts
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageSalesforceContacts': // Only owners and admins can manage salesforce contacts
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageSalesforceLeads': // Only owners and admins can manage salesforce leads
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageKnowledgeBase': // Only owners and admins can manage knowledge base
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      case 'canManageDeals': // Only owners and admins can manage deals
        return ['owner', 'admin', 'member'].includes(currentUserRole)
      default:
        return false
    }
  }

  return {
    canManageTeam: checkPermission('canManageTeam'),
    canManageSettings: checkPermission('canManageSettings'),
    canDeleteOrganization: checkPermission('canDeleteOrganization'),
    canInviteMembers: checkPermission('canInviteMembers'),
    canManagePhones: checkPermission('canManagePhones'),
    canManageAgents: checkPermission('canManageAgents'), // Add agent permission to return object
    canManageOrganization: checkPermission('canManageOrganization'),
    canManageContacts: checkPermission('canManageContacts'),
    canManageCompanies: checkPermission('canManageCompanies'), // Add company permission to return object
    canManageSalesforceAccounts: checkPermission('canManageSalesforceAccounts'), // Add salesforce accounts permission to return object
    canManageHubSpotContacts: checkPermission('canManageHubSpotContacts'), // Add hubspot contacts permission to return object
    canManageSalesforceContacts: checkPermission('canManageSalesforceContacts'),
    canManageSalesforceLeads: checkPermission('canManageSalesforceLeads'),
    canManageKnowledgeBase: checkPermission('canManageKnowledgeBase'),
    canManageDeals: checkPermission('canManageDeals'),
    currentUserRole,
  }
}
