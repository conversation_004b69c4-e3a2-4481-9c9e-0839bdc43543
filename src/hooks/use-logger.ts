'use client'

import { useCallback } from 'react'

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogOptions {
  namespace?: string
  data?: Record<string, unknown>
}

export const useLogger = (defaultNamespace = 'client') => {
  const log = useCallback(
    (level: LogLevel, message: string, options: LogOptions = {}) => {
      const { namespace = defaultNamespace, data = {} } = options
      const timestamp = new Date().toISOString()
      // const logData = {
      //   timestamp,
      //   level,
      //   namespace,
      //   message,
      //   ...data,
      // }

      // In development, we can use the console with colors
      if (process.env.NODE_ENV === 'development') {
        const colors = {
          debug: '#7f8c8d',
          info: '#2ecc71',
          warn: '#f1c40f',
          error: '#e74c3c',
        }

        console.log(
          `%c${timestamp} [${level.toUpperCase()}] ${namespace}: ${message}`,
          `color: ${colors[level]}`,
          data,
        )
        return
      }

      // In production, we might want to send logs to a service
      // You could integrate with services like Sentry, LogRocket, etc.
      if (process.env.NODE_ENV === 'production') {
        // Example: send to your logging service
        // await fetch('/api/logs', {
        //   method: 'POST',
        //   body: JSON.stringify(logData),
        // })
      }
    },
    [defaultNamespace],
  )

  return {
    debug: (message: string, options?: LogOptions) => log('debug', message, options),
    info: (message: string, options?: LogOptions) => log('info', message, options),
    warn: (message: string, options?: LogOptions) => log('warn', message, options),
    error: (message: string, options?: LogOptions) => log('error', message, options),
  }
}
