declare namespace App {
  type UserStatus = 'active' | 'inactive' | 'deleted'
  type OrganizationType = 'personal' | 'education' | 'startup' | 'agency' | 'company'
  type CompanySize = '1-10' | '10-49' | '50-99' | '100-299' | '300+'
  type OrganizationRole = 'owner' | 'admin' | 'member' | 'guest'
  type OrganizationStatus = 'active' | 'inactive' | 'pending' | 'declined'
  type OrganizationInviteStatus = 'pending' | 'accepted' | 'declined' | 'expired'
  type PhoneProvider = 'twilio' | 'sip_trunk'
  type CRMType = 'hubspot' | 'salesforce' | 'paragon'
  type CRMEntityType = 'contact' | 'deal' | 'company' | 'call_log'
  type CRMChangeType = 'create' | 'update' | 'delete'
  type ObservationStatus = 'pending' | 'processing' | 'completed' | 'failed'

  interface Profile {
    id: string
    name: string | null
    email: string | null
    avatar?: string | null
    provider: string | null
    phone: string | null
    status: UserStatus
    created_at: string
    updated_at: string | null
    deleted_at: string | null
  }

  interface Organization {
    id: string
    name: string
    logo_url?: string | null
    type: OrganizationType
    company_size?: CompanySize | null
    paragon_credentials?: Record<string, unknown> | null
    created_at?: string
    updated_at?: string | null
    deleted_at?: string | null
  }

  interface OrganizationMember {
    id: string
    created_at: string
    updated_at: string | null
    deleted_at: string | null
    organization_id: string
    user_id: string
    role: OrganizationRole
    status: OrganizationStatus
    user: Profile
  }

  interface OrganizationInvite {
    id: string
    organization_id: string
    email: string
    role: OrganizationRole
    status: OrganizationInviteStatus
    organization: Organization
    invited_by: string // uuid
    expires_at: string // timestamp with timezone
    created_at: string // timestamp with timezone
    updated_at: string | null // timestamp with timezone
    deleted_at: string | null // timestamp with timezone
  }

  interface Phone {
    id: string
    eleven_labs_phone_id: string
    organization_id: string
    user_id: string | null
    phone_number: string
    label: string
    sid: string | null
    token: string | null
    provider: PhoneProvider
    termination_uri: string | null
    credentials: {
      username: string | null
      password: string | null
    }
    created_at: string
    updated_at: string | null
  }

  interface Agent {
    agent_id: string
    agent_name: string
    first_message_prompt?: string
    system_prompt?: string
    evaluation_criteria?: Record<string, unknown>
    data_collection_points?: Record<string, unknown>
    turn_timeout?: number
    max_duration_seconds?: number
    organization_id: string
    user_id?: string
    created_at: string
    last_updated: string
    tools?: string[]
    is_template: boolean
    language: string
    llm: string
    temperature: number
    max_tokens: number
    voice_id?: string
    agent_output_audio_format?: string
    model_id: string
    optimize_streaming_latency: number
    similarity_boost: number
    speed: number
    stability: number
    turn_timeout?: number
    max_duration_seconds?: number
    rag_enabled?: boolean
    knowledge_bases?: KnowledgeBase[]
  }

  interface Conversation {
    conversation_id: string
    agent_id: string
    status: string | null
    transcript: Record<string, unknown> | null
    analysis: Record<string, unknown> | null
    metadata: Record<string, unknown> | null
    conversation_initiation_client_data: Record<string, unknown> | null
    data: Record<string, unknown> | null
    created_at: string
    last_synced: string
  }

  interface CompanyIndustry {
    id: string
    name: string
    slug: string
    created_at: string
    updated_at: string
  }

  interface CRMConnection {
    id: string
    organization_id: string
    user_id: string | null
    crm_type: CRMType
    credentials: Record<string, unknown>
    integration_id: string
    credential_id: string
    crm_instance_identifier: string | null
    schema_definition: Record<string, unknown> | null
    is_synced_contacts: boolean
    is_synced_deals: boolean
    is_synced_companies: boolean
    created_at: string
    updated_at: string
  }

  interface CachedCRMContact {
    id: string
    crm_connection_id: string
    crm_entity_id: string
    first_name: string | null
    last_name: string | null
    email: string | null
    phone: string | null
    job_title: string | null
    properties: Record<string, unknown> | null
    last_synced_at: string | null
    created_at: string
    updated_at: string
  }

  interface CachedCRMCompany {
    id: string
    crm_connection_id: string
    crm_entity_id: string
    name: string
    domain: string
    industry?: string | null
    type?: string | null
    description?: string | null
    linkedin_url?: string | null
    annual_revenue?: number | null
    employee_count?: number | null
    phone?: string | null
    street_address?: string | null
    city?: string | null
    state?: string | null
    postal_code?: string | null
    country?: string | null
    timezone?: string | null
    properties?: Record<string, unknown> | null
    last_synced_at: string | null
    created_at: string
    updated_at: string
  }

  interface CachedCRMDeal {
    id: string
    crm_connection_id: string
    crm_entity_id: string
    name: string
    amount?: number
    pipeline?: string
    stage?: string
    close_date?: string
    priority?: string
    type?: string
    properties?: Record<string, unknown>
    last_synced_at: string
    created_at: string
    updated_at: string
  }

  interface Observation {
    id: number
    crm_connection_id: string
    timestamp: string
    source: string
    entity_type: CRMEntityType
    entity_id: string
    change_type: CRMChangeType
    state_snapshot: Record<string, unknown>
  }

  interface ObservationProcessingStatus {
    id: number
    observation_id: number
    worker_id: string | null
    status: ObservationStatus
    started_at: string | null
    finished_at: string | null
    error_message: string | null
    retry_count: number
    created_at: string
    updated_at: string
  }

  interface ParagonHubSpotContactProperties {
    associatedcompanyid: string
    city: string
    company: string
    createdate: string
    email: string
    firstname: string
    hs_lead_status: string
    hs_all_contact_vids: string
    hs_analytics_average_page_views: string
    hs_analytics_first_timestamp: string
    hs_analytics_num_event_completions: string
    hs_analytics_num_page_views: string
    hs_analytics_num_visits: string
    hs_analytics_revenue: string
    hs_analytics_source: string
    hs_analytics_source_data_1: string
    hs_analytics_source_data_2: string
    hs_associated_target_accounts: string
    hs_calculated_phone_number: string
    hs_calculated_phone_number_country_code: string
    hs_currently_enrolled_in_prospecting_agent: string
    hs_email_domain: string
    hs_full_name_or_email: string
    hs_object_id: string
    lastmodifieddate: string
    hs_is_contact: string
    hs_is_unworked: string
    hs_latest_source: string
    hs_latest_source_data_1: string
    hs_latest_source_data_2: string
    hs_latest_source_timestamp: string
    hs_lifecyclestage_lead_date: string
    hs_marketable_reason_id: string
    hs_marketable_reason_type: string
    hs_marketable_status: string
    hs_marketable_until_renewal: string
    hs_membership_has_accessed_private_content: string
    hs_object_source: string
    hs_object_source_id: string
    hs_object_source_label: string
    hs_pipeline: string
    hs_prospecting_agent_actively_enrolled_count: string
    hs_registered_member: string
    hs_searchable_calculated_phone_number: string
    hs_sequences_actively_enrolled_count: string
    hs_social_facebook_clicks: string
    hs_social_google_plus_clicks: string
    hs_social_linkedin_clicks: string
    hs_social_num_broadcast_clicks: string
    hs_social_twitter_clicks: string
    hs_updated_by_user_id: string
    hs_v2_date_entered_lead: string
    jobtitle: string
    lastname: string
    lifecyclestage: string
    num_conversion_events: string
    num_notes: string
    num_unique_conversion_events: string
    phone: string
    website: string
  }
  interface ParagonHubSpotContact {
    id: string
    properties: ParagonHubSpotContactProperties
    createdAt: string
    updatedAt: string
    archived: boolean
  }

  type ParagonHubSpotCompanyProperties = Record<string, string | number | boolean | null>

  interface ParagonHubSpotCompany {
    id: string
    properties: ParagonHubSpotCompanyProperties
    createdAt: string
    updatedAt: string
    archived: boolean
  }

  interface ParagonSalesforceAccount {
    attributes: {
      type: string
      url: string
    }
    Id: string
    IsDeleted: boolean
    MasterRecordId: string | null
    Name: string
    Type: string
    ParentId: string | null
    BillingStreet: string | null
    BillingCity: string | null
    BillingState: string | null
    BillingPostalCode: string | null
    BillingCountry: string | null
    BillingLatitude: string | null
    BillingLongitude: string | null
    BillingGeocodeAccuracy: string | null
    BillingAddress: string | null
    ShippingStreet: string | null
    ShippingCity: string | null
    ShippingState: string | null
    ShippingPostalCode: string | null
    ShippingCountry: string | null
    ShippingLatitude: string | null
    ShippingLongitude: string | null
    ShippingGeocodeAccuracy: string | null
    ShippingAddress: string | null
    Phone: string | null
    Fax: string | null
    Website: string | null
    PhotoUrl: string | null
    Industry: string | null
    AnnualRevenue: string | null
    NumberOfEmployees: string | null
    Description: string | null
    OwnerId: string
    CreatedDate: string
    CreatedById: string
    LastModifiedDate: string
    LastModifiedById: string
    SystemModstamp: string
    LastActivityDate: string | null
    LastViewedDate: string | null
    LastReferencedDate: string | null
    IsPartner: boolean
    IsCustomerPortal: boolean
    Jigsaw: string | null
    JigsawCompanyId: string | null
    AccountSource: string | null
    SicDesc: string | null
    IsBuyer: boolean
  }

  interface ParagonSalesforceContact {
    attributes: {
      type: string
      url: string
    }
    Id: string
    IsDeleted: boolean
    MasterRecordId: string | null
    AccountId: string
    LastName: string
    FirstName: string
    Salutation: string
    Name: string
    OtherStreet: string | null
    OtherCity: string | null
    OtherState: string | null
    OtherPostalCode: string | null
    OtherCountry: string | null
    OtherLatitude: string | null
    OtherLongitude: string | null
    OtherGeocodeAccuracy: string | null
    OtherAddress: string | null
    MailingStreet: string
    MailingCity: string
    MailingState: string
    MailingPostalCode: string
    MailingCountry: string
    MailingLatitude: string | null
    MailingLongitude: string | null
    MailingGeocodeAccuracy: string | null
    MailingAddress: {
      city: string
      country: string
      geocodeAccuracy: string | null
      latitude: string | null
      longitude: string | null
      postalCode: string
      state: string
      street: string
    }
    Phone: string | null
    Fax: string | null
    MobilePhone: string | null
    HomePhone: string | null
    OtherPhone: string | null
    AssistantPhone: string | null
    ReportsToId: string | null
    Email: string
    Title: string
    Department: string | null
    AssistantName: string | null
    LeadSource: string | null
    Birthdate: string | null
    Description: string | null
    OwnerId: string
    HasOptedOutOfEmail: boolean
    CreatedDate: string
    CreatedById: string
    LastModifiedDate: string
    LastModifiedById: string
    SystemModstamp: string
    LastActivityDate: string | null
    LastCURequestDate: string | null
    LastCUUpdateDate: string | null
    LastViewedDate: string | null
    LastReferencedDate: string | null
    EmailBouncedReason: string | null
    EmailBouncedDate: string | null
    IsEmailBounced: boolean
    PhotoUrl: string | null
    Jigsaw: string | null
    JigsawContactId: string | null
    IndividualId: string | null
    ContactSource: string | null
  }

  interface ParagonSalesforceLead {
    attributes: {
      type: string
      url: string
    }
    Id: string
    IsDeleted: boolean
    MasterRecordId: string | null
    LastName: string
    FirstName: string
    Salutation: string
    Name: string
    Title: string
    Company: string
    Street: string
    City: string
    State: string
    PostalCode: string
    Country: string
    Latitude: string | null
    Longitude: string | null
    GeocodeAccuracy: string | null
    Address: {
      city: string
      country: string
      geocodeAccuracy: string | null
      latitude: string | null
      longitude: string | null
      postalCode: string
      state: string
      street: string
    }
    Phone: string | null
    Email: string
    Website: string | null
    PhotoUrl: string | null
    Description: string | null
    LeadSource: string | null
    Status: string | null
    Industry: string | null
    Rating: string | null
    AnnualRevenue: number | null
    NumberOfEmployees: number | null
    OwnerId: string
    HasOptedOutOfEmail: boolean
    IsConverted: boolean
    ConvertedDate: string | null
    ConvertedAccountId: string | null
    ConvertedContactId: string | null
    ConvertedOpportunityId: string | null
    IsUnreadByOwner: boolean
    CreatedDate: string
    CreatedById: string
    LastModifiedDate: string
    LastModifiedById: string
    SystemModstamp: string
    LastActivityDate: string | null
    LastViewedDate: string | null
    LastReferencedDate: string | null
    Jigsaw: string | null
    JigsawContactId: string | null
    EmailBouncedReason: string | null
    EmailBouncedDate: string | null
    IndividualId: string | null
  }
  interface Voice {
    voice_id: string
    name: string
    samples: null
    category: string
    labels: {
      accent: string
      description: string
      age: string
      gender: string
      use_case: string
    }
    description: string | null
    preview_url: string
    available_for_tiers: string[]
    high_quality_base_model_ids: string[]
    verified_languages: string[]
  }
  interface KnowledgeBase {
    id: string
    eleven_labs_id: string
    organization_id: string
    user_id: string
    user: Partial<Profile>
    agents: Pick<Agent, 'agent_id' | 'agent_name'>[]
    name: string
    type: 'file' | 'url' | 'text'
    content: string | null
    prompt_injectable: boolean
    metadata: Record<string, string | number>
    url?: string
    created_at: string
    updated_at: string | null
    deleted_at: string | null
  }
}

interface Window {
  ReactNativeWebView?: {
    postMessage: (message: string) => void
  }
}
