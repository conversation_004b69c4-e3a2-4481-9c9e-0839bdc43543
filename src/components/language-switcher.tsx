'use client'

import { Languages } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { usePathname, useRouter } from '@/i18n/navigation'
import { locales } from '@/i18n/routing'

export function LanguageSwitcher() {
  const router = useRouter()
  const t = useTranslations('Header')
  const locale = useLocale()
  const pathname = usePathname()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const onChange = (newLocale: string) => () => {
    router.push(pathname, { locale: newLocale })
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon">
        <Languages className="h-4 w-4" />
        <span className="sr-only">{t('switchLanguage')}</span>
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Languages className="h-4 w-4" />
          <span className="sr-only">{t('switchLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuRadioGroup value={locale}>
          {locales.map(loc => (
            <DropdownMenuRadioItem
              key={loc}
              value={loc}
              className="cursor-pointer"
              onClick={onChange(loc)}>
              {loc.toUpperCase()}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
