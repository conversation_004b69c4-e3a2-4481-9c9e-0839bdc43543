/* eslint-disable @typescript-eslint/no-explicit-any */
import { Upload, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { toast } from 'sonner'

import { Avatar, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { CustomFile } from '@/services/types'

interface UploadAvatarProps {
  value?: CustomFile | null
  onChange: (value: CustomFile | null) => void
}

const MAX_SIZE = 5 * 1024 * 1024 // 5MB in bytes

export default function UploadAvatar({ value, onChange }: UploadAvatarProps) {
  const t = useTranslations('common')

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0]

      if (file.size > MAX_SIZE) {
        toast.error(t('file_size_error', { size: MAX_SIZE / (1024 * 1024) }))
        return
      }
      onChange(file)
    },
    [onChange, t],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'image/*': [] },
    multiple: false,
  })

  const removeLogo = () => {
    onChange(null)
  }

  return (
    <Card className="w-full max-w-sm border-none !shadow-none">
      <CardContent className="flex items-center gap-4 !p-0">
        {value ? (
          <div className="relative">
            <Avatar className="h-32 w-32">
              <AvatarImage
                src={value instanceof File ? URL.createObjectURL(value as any) : (value.url ?? '')}
                alt="Uploaded logo"
              />
            </Avatar>
            <Button
              variant="outline"
              size="icon"
              className="absolute -right-2 -top-2 rounded-full"
              onClick={removeLogo}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div
            {...getRootProps()}
            className={`flex h-32 w-32 cursor-pointer items-center justify-center rounded-full border-2 border-dashed ${isDragActive ? 'border-primary' : 'border-gray-300'}`}>
            <input {...getInputProps()} />
            <Upload className="h-8 w-8 text-gray-400" />
          </div>
        )}
        <div
          {...getRootProps()}
          className="flex cursor-pointer items-center justify-center gap-2 rounded px-4 py-2 font-semibold">
          <input {...getInputProps()} />
          <Upload className="mr-2 h-4 w-4" /> {value ? t('change') : t('upload')}
        </div>
      </CardContent>
    </Card>
  )
}
