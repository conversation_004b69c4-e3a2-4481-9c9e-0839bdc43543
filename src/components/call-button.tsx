'use client'

import { cva } from 'class-variance-authority'
import { Mic, PhoneOff, Waves } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Spinner } from '@/components/ui/spinner'
import { cn } from '@/lib/utils'

const callStatusVariants = cva(
  'absolute inset-0 flex items-center justify-center rounded-full transition-all duration-500',
  {
    variants: {
      status: {
        idle: 'bg-primary scale-0 opacity-0',
        connecting: 'bg-amber-500 scale-100 opacity-70',
        connected: 'bg-green-500 scale-100 opacity-70',
        disconnecting: 'bg-red-500 scale-100 opacity-70',
      },
    },
    defaultVariants: {
      status: 'idle',
    },
  },
)

const pulseRingVariants = cva('absolute inset-0 rounded-full transition-all duration-300', {
  variants: {
    status: {
      idle: 'animate-none opacity-0',
      connecting: 'animate-pulse border-2 border-amber-500/50',
      connected: 'animate-pulse border-2 border-green-500/50',
      disconnecting: 'animate-none opacity-0',
    },
  },
  defaultVariants: {
    status: 'idle',
  },
})

interface CallButtonProps {
  status: 'idle' | 'connecting' | 'connected' | 'disconnecting'
  isSpeaking?: boolean
  onStartCall: () => void
  onEndCall: () => void
}

export function CallButton({ status, isSpeaking, onStartCall, onEndCall }: CallButtonProps) {
  const t = useTranslations('Call')
  const [animateStatus, setAnimateStatus] = useState(status)

  // Add a slight delay to animations for smoother transitions
  useEffect(() => {
    if (status === 'disconnecting') {
      const timer = setTimeout(() => {
        setAnimateStatus('idle')
      }, 1000)
      return () => clearTimeout(timer)
    }
    setAnimateStatus(status)
  }, [status])

  const handleClick = () => {
    if (status === 'connected') {
      onEndCall()
    } else if (status === 'idle') {
      onStartCall()
    }
  }

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="relative">
        {/* Outer pulse ring */}
        <div className={pulseRingVariants({ status: animateStatus })} />

        {/* Status background */}
        <div className={callStatusVariants({ status: animateStatus })} />

        {/* Main button */}
        <Button
          onClick={handleClick}
          disabled={status === 'connecting' || status === 'disconnecting'}
          variant={status === 'connected' ? 'secondary' : 'default'}
          size="lg"
          className={cn(
            'relative z-10 h-14 w-14 rounded-full p-0 transition-all duration-300',
            status === 'connected' &&
              'bg-white text-green-500 hover:bg-white/90 hover:text-green-600',
            status === 'idle' && 'shadow-lg hover:shadow-xl',
          )}>
          {status === 'connecting' ? (
            <Spinner size="small" className="text-white" />
          ) : status === 'connected' ? (
            <PhoneOff className="h-6 w-6" />
          ) : (
            <Mic className="h-6 w-6" />
          )}
        </Button>

        {/* Speaking indicator */}
        {status === 'connected' && isSpeaking && (
          <div className="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-100 text-green-600">
            <Waves className="h-4 w-4 animate-pulse" />
          </div>
        )}
      </div>

      {/* Status text */}
      <div className="flex items-center gap-2 rounded-full bg-background/80 px-3 py-1 text-sm backdrop-blur-sm">
        <span
          className={cn(
            'h-2 w-2 rounded-full',
            status === 'idle'
              ? 'bg-slate-400'
              : status === 'connecting'
                ? 'bg-amber-500'
                : status === 'connected'
                  ? 'bg-green-500'
                  : 'bg-red-500',
          )}
        />
        <p className="font-medium">
          {status === 'idle'
            ? t('ready')
            : status === 'connecting'
              ? t('connecting')
              : status === 'connected'
                ? isSpeaking
                  ? t('speaking')
                  : t('listening')
                : t('ending')}
        </p>
      </div>
    </div>
  )
}
