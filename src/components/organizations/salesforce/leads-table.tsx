'use client'
import { <PERSON>, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import { toast } from 'sonner'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useDeleteSalesforceLead } from '@/query/mutations/crm.mutation'
import { GetSalesforceLeadsParameters } from '@/services/api/types'

export function LeadsTableSkeleton() {
  const t = useTranslations('Organizations.salesforce.sales')

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('table.name')}</TableHead>
          <TableHead>{t('table.company')}</TableHead>
          <TableHead>{t('table.email')}</TableHead>
          <TableHead>{t('table.phone')}</TableHead>
          <TableHead>{t('table.status')}</TableHead>
          <TableHead className="w-[100px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...Array(5)].map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-4 w-[200px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[150px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[180px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[120px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[100px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="ml-auto h-8 w-8 rounded-md" />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export function LeadsTable({
  leads,
  isLoading,
  onEdit,
  organizationId,
  parameters,
}: {
  leads: App.ParagonSalesforceLead[]
  isLoading: boolean
  onEdit: (lead: App.ParagonSalesforceLead) => void
  organizationId: string
  parameters: GetSalesforceLeadsParameters
}) {
  const t = useTranslations('Organizations.salesforce.sales')

  const { canManageSalesforceLeads } = useOrganizationPermissions()
  const [selectedLoadingLead, setSelectedLoadingLead] = useState<Record<string, boolean>>({})
  const deleteLead = useDeleteSalesforceLead(organizationId, parameters)
  const [selectedLeadToDelete, setSelectedLeadToDelete] =
    useState<App.ParagonSalesforceLead | null>(null)

  const handleEditLead = (lead: App.ParagonSalesforceLead) => () => {
    onEdit(lead)
  }

  const handleDeleteLead = (lead: App.ParagonSalesforceLead) => () => {
    setSelectedLeadToDelete(lead)
  }

  const handleDeleteLeadConfirm = async () => {
    if (!selectedLeadToDelete) return
    try {
      setSelectedLoadingLead({ [selectedLeadToDelete.Id]: true })
      await deleteLead.mutateAsync({
        recordId: selectedLeadToDelete.Id,
      })
      toast.success(t('delete.success'))
    } catch {
      toast.error(t('delete.error'))
    } finally {
      setSelectedLoadingLead({ [selectedLeadToDelete.Id]: false })
      setSelectedLeadToDelete(null)
    }
  }

  const renderActions = (lead: App.ParagonSalesforceLead) => {
    if (!canManageSalesforceLeads) return null
    if (selectedLoadingLead[lead.Id]) return <Loader2 className="h-4 w-4 animate-spin" />
    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('table.actions.open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={handleEditLead(lead)}>
            <Edit className="mr-2 h-4 w-4" />
            {t('table.actions.edit')}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={handleDeleteLead(lead)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t('table.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  if (isLoading) {
    return <LeadsTableSkeleton />
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.name')}</TableHead>
            <TableHead>{t('table.company')}</TableHead>
            <TableHead>{t('table.email')}</TableHead>
            <TableHead>{t('table.phone')}</TableHead>
            <TableHead>{t('table.status')}</TableHead>
            <TableHead className="w-[100px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {leads.map(lead => (
            <TableRow key={lead.Id}>
              <TableCell className="font-medium">
                {`${lead.FirstName || ''} ${lead.LastName || ''}`}
              </TableCell>
              <TableCell>{lead.Company}</TableCell>
              <TableCell>{lead.Email}</TableCell>
              <TableCell>{lead.Phone}</TableCell>
              <TableCell>{lead.Status}</TableCell>
              <TableCell>{renderActions(lead)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <AlertDialog open={!!selectedLeadToDelete} onOpenChange={() => setSelectedLeadToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('delete.title')}</AlertDialogTitle>
            <AlertDialogDescription>{t('delete.description')}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('delete.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteLeadConfirm}
              disabled={deleteLead.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {t('delete.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
