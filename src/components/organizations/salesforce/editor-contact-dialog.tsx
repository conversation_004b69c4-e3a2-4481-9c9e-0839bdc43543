'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import { toast } from 'sonner'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  useCreateSalesforceContact,
  useUpdateSalesforceContact,
} from '@/query/mutations/crm.mutation'
import { useGetSalesforceAccounts } from '@/query/queries/crm.query'
import { GetSalesforceContactsParameters } from '@/services/api/types'
import { logger } from '@/utils/logger'

interface EditorContactDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
  contact: App.ParagonSalesforceContact | null
  parameters: GetSalesforceContactsParameters
}

const contactSchema = z.object({
  FirstName: z.string().min(1, 'First name is required'),
  LastName: z.string().min(1, 'Last name is required'),
  Email: z.string().email('Invalid email address').optional().or(z.literal('')),
  Phone: z
    .string()
    .optional()
    .refine(val => !val || isValidPhoneNumber(val), {
      message: 'Invalid phone number',
    }),
  Title: z.string().optional().or(z.literal('')),
  AccountId: z.string().min(1, 'Account is required'),
  Description: z.string().optional().or(z.literal('')),
  MailingStreet: z.string().optional().or(z.literal('')),
  MailingCity: z.string().optional().or(z.literal('')),
  MailingState: z.string().optional().or(z.literal('')),
  MailingPostalCode: z.string().optional().or(z.literal('')),
  MailingCountry: z.string().optional().or(z.literal('')),
})

type ContactFormValues = z.infer<typeof contactSchema>

interface AccountSelectProps {
  accounts: App.ParagonSalesforceAccount[]
  value: string
  onChange: (value: string) => void
  placeholder: string
}

function AccountSelect({ accounts, value, onChange, placeholder }: AccountSelectProps) {
  return (
    <Select onValueChange={onChange} value={value || ''}>
      <FormControl>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
      </FormControl>
      <SelectContent>
        {accounts.map(account => (
          <SelectItem key={account.Id} value={account.Id}>
            {account.Name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

const defaultValues: ContactFormValues = {
  FirstName: '',
  LastName: '',
  Email: '',
  Phone: '',
  Title: '',
  AccountId: '',
  Description: '',
  MailingStreet: '',
  MailingCity: '',
  MailingState: '',
  MailingPostalCode: '',
  MailingCountry: '',
}

export function EditorContactDialog({
  open,
  onClose,
  organizationId,
  contact,
  parameters,
}: EditorContactDialogProps) {
  const t = useTranslations('Organizations.salesforce.contacts')
  const isEditing = !!contact

  const createContact = useCreateSalesforceContact(organizationId, parameters)
  const updateContact = useUpdateSalesforceContact(organizationId, parameters)

  const { data: accountsData } = useGetSalesforceAccounts(
    organizationId,
    {
      paginationParameters: {
        pageCursor: '0',
      },
    },
    { enabled: open },
  )

  const accounts = accountsData?.records.records ?? []

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues,
  })

  const onSubmit = async (values: ContactFormValues) => {
    try {
      const { FirstName, LastName, Email, Title, AccountId, Description, ...rest } = values
      const payload = {
        FirstName,
        LastName,
        Email,
        Title,
        accountId: AccountId,
        Description,
        additionalFields: {
          ...rest,
        },
      }
      if (isEditing && contact) {
        await updateContact.mutateAsync({
          recordId: contact.Id,
          ...payload,
        })
        toast.success(t('success.updated'))
      } else {
        await createContact.mutateAsync(payload)
        toast.success(t('success.created'))
      }
      onClose()
      form.reset()
    } catch (error) {
      logger.error(error)
      toast.error(t('error.save'))
    }
  }

  const handleClose = () => {
    form.reset(defaultValues)
    onClose()
  }

  useEffect(() => {
    if (!open || !contact) return
    form.reset({
      FirstName: contact?.FirstName || '',
      LastName: contact?.LastName || '',
      Email: contact?.Email || '',
      Phone: contact?.Phone || '',
      Title: contact?.Title || '',
      AccountId: contact?.AccountId || '',
      Description: contact?.Description || '',
      MailingStreet: contact?.MailingStreet || '',
      MailingCity: contact?.MailingCity || '',
      MailingState: contact?.MailingState || '',
      MailingPostalCode: contact?.MailingPostalCode || '',
      MailingCountry: contact?.MailingCountry || '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, contact])

  return (
    <Dialog open={open} onOpenChange={open => !open && handleClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? t('editContact') : t('addContact')}</DialogTitle>
          <DialogDescription>
            {isEditing ? t('editContactDescription') : t('addContactDescription')}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="flex max-h-[70vh] flex-col gap-4 overflow-y-auto px-1">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">{t('form.basicInfo')}</h3>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="FirstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.firstName')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="LastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.lastName')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="Email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.email')}</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="Phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.phone')}</FormLabel>
                      <FormControl>
                        <PhoneInput
                          defaultCountry="US"
                          placeholder={t('form.phonePlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="Title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.title')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="AccountId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.account')}</FormLabel>
                      <FormControl>
                        <AccountSelect
                          accounts={accounts}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder={t('form.selectAccount')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Mailing Address */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {t('form.mailingAddress')}
                </h3>

                <FormField
                  control={form.control}
                  name="MailingStreet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.mailingStreet')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="MailingCity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.mailingCity')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="MailingState"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.mailingState')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="MailingPostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.mailingPostalCode')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="MailingCountry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.mailingCountry')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Description */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {t('form.additionalInfo')}
                </h3>

                <FormField
                  control={form.control}
                  name="Description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.description')}</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={createContact.isPending || updateContact.isPending}>
                {form.formState.isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? t('save') : t('add')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
