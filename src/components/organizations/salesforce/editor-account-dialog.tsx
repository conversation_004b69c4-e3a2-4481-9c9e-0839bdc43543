'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import { toast } from 'sonner'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ACCOUNT_TYPES } from '@/constants/crm'
import {
  useCreateSalesforceAccount,
  useUpdateSalesforceAccount,
} from '@/query/mutations/crm.mutation'
import { GetSalesforceAccountsParameters } from '@/services/api/types'

const accountSchema = z.object({
  Name: z.string().min(1, { message: 'Account name is required' }),
  Website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  Type: z.string().optional(),
  Phone: z
    .string()
    .optional()
    .refine(val => !val || isValidPhoneNumber(val), {
      message: 'Invalid phone number',
    }),
  BillingStreet: z.string().optional(),
  BillingCity: z.string().optional(),
  BillingState: z.string().optional(),
  BillingPostalCode: z.string().optional(),
  BillingCountry: z.string().optional(),
  ShippingStreet: z.string().optional(),
  ShippingCity: z.string().optional(),
  ShippingState: z.string().optional(),
  ShippingPostalCode: z.string().optional(),
  ShippingCountry: z.string().optional(),
})

type AccountFormValues = z.infer<typeof accountSchema>

interface EditorAccountDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
  account?: App.ParagonSalesforceAccount | null
  parameters: GetSalesforceAccountsParameters
}

export function EditorAccountDialog({
  open,
  onClose,
  organizationId,
  account,
  parameters,
}: EditorAccountDialogProps) {
  const t = useTranslations('Organizations.salesforce.accounts')
  const isEditing = !!account
  const createAccount = useCreateSalesforceAccount(organizationId, parameters)
  const updateAccount = useUpdateSalesforceAccount(organizationId, parameters)

  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      Name: '',
      Website: '',
      Type: '',
      Phone: '',
      BillingStreet: '',
      BillingCity: '',
      BillingState: '',
      BillingPostalCode: '',
      BillingCountry: '',
      ShippingStreet: '',
      ShippingCity: '',
      ShippingState: '',
      ShippingPostalCode: '',
      ShippingCountry: '',
    },
  })

  // Update form values when editing an existing account
  useEffect(() => {
    if (account) {
      form.reset({
        Name: account.Name || '',
        Website: account.Website || '',
        Type: account.Type || '',
        Phone: account.Phone || '',
        BillingStreet: account.BillingStreet || '',
        BillingCity: account.BillingCity || '',
        BillingState: account.BillingState || '',
        BillingPostalCode: account.BillingPostalCode || '',
        BillingCountry: account.BillingCountry || '',
        ShippingStreet: account.ShippingStreet || '',
        ShippingCity: account.ShippingCity || '',
        ShippingState: account.ShippingState || '',
        ShippingPostalCode: account.ShippingPostalCode || '',
        ShippingCountry: account.ShippingCountry || '',
      })
    } else {
      form.reset({
        Name: '',
        Website: '',
        Type: '',
        Phone: '',
        BillingStreet: '',
        BillingCity: '',
        BillingState: '',
        BillingPostalCode: '',
        BillingCountry: '',
        ShippingStreet: '',
        ShippingCity: '',
        ShippingState: '',
        ShippingPostalCode: '',
        ShippingCountry: '',
      })
    }
  }, [account, form])

  const onSubmit = async (values: AccountFormValues) => {
    try {
      const { Name, Website, Type, Phone, ...rest } = values
      const payload = {
        Name,
        Website,
        Type,
        Phone,
        additionalFields: {
          ...rest,
        },
      }
      if (isEditing && account) {
        await updateAccount.mutateAsync({
          recordId: account.Id,
          ...payload,
        })
        toast.success(t('edit.success'))
      } else {
        await createAccount.mutateAsync(payload)
        toast.success(t('add.success'))
      }
      onClose()
      form.reset()
    } catch (error) {
      console.error('Error saving account:', error)
      toast.error(isEditing ? t('edit.error') : t('add.error'))
    }
  }

  const isPending = createAccount.isPending || updateAccount.isPending

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="px-0 sm:max-w-[600px]">
        <DialogHeader className="px-4">
          <DialogTitle>{isEditing ? t('edit.title') : t('add.title')}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <div className="flex max-h-[70vh] flex-col gap-4 overflow-y-auto px-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">Basic Information</h3>

                <FormField
                  control={form.control}
                  name="Name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.name')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="Website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.website')}</FormLabel>
                      <FormControl>
                        <Input type="url" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="Type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.type')}</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {ACCOUNT_TYPES.map(type => (
                              <SelectItem key={type.id} value={type.id}>
                                {type.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="Phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.phone')}</FormLabel>
                        <FormControl>
                          <PhoneInput
                            defaultCountry="US"
                            placeholder="Enter phone number"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Billing Address */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">Billing Address</h3>

                <FormField
                  control={form.control}
                  name="BillingStreet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.billingStreet')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="BillingCity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.billingCity')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="BillingState"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.billingState')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="BillingPostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.billingPostalCode')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="BillingCountry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.billingCountry')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Shipping Address */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">Shipping Address</h3>

                <FormField
                  control={form.control}
                  name="ShippingStreet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.shippingStreet')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="ShippingCity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.shippingCity')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ShippingState"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.shippingState')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="ShippingPostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.shippingPostalCode')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ShippingCountry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.shippingCountry')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <DialogFooter className="px-4 py-4">
              <Button variant="outline" type="button" onClick={onClose}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? t('save') : t('create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
