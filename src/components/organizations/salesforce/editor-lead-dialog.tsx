'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import { toast } from 'sonner'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { INDUSTRY_TYPES, LEAD_SOURCE, SALESFORCE_LEAD_STATUS } from '@/constants/crm'
import { useCreateSalesforceLead, useUpdateSalesforceLead } from '@/query/mutations/crm.mutation'
import { GetSalesforceLeadsParameters } from '@/services/api/types'

const leadSchema = z.object({
  FirstName: z.string().min(1, { message: 'First name is required' }),
  LastName: z.string().min(1, { message: 'Last name is required' }),
  Company: z.string().min(1, { message: 'Company is required' }),
  Title: z.string().optional().or(z.literal('')),
  Website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  Description: z.string().optional().or(z.literal('')),
  Status: z.string().min(1, { message: 'Status is required' }),
  Phone: z
    .string()
    .optional()
    .refine(val => !val || isValidPhoneNumber(val), {
      message: 'Invalid phone number',
    }),
  Email: z.string().email({ message: 'Invalid email address' }).optional().or(z.literal('')),
  Street: z.string().optional().or(z.literal('')),
  City: z.string().optional().or(z.literal('')),
  State: z.string().optional().or(z.literal('')),
  PostalCode: z.string().optional().or(z.literal('')),
  Country: z.string().optional().or(z.literal('')),
  NumberOfEmployees: z.coerce.number().optional().or(z.literal(0)),
  AnnualRevenue: z.coerce.number().optional().or(z.literal(0)),
  LeadSource: z.string().optional().or(z.literal('')),
  Industry: z.string().optional().or(z.literal('')),
})

type LeadFormValues = z.infer<typeof leadSchema>

interface EditorLeadDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
  lead?: App.ParagonSalesforceLead | null
  parameters: GetSalesforceLeadsParameters
}

const defaultValues: LeadFormValues = {
  FirstName: '',
  LastName: '',
  Company: '',
  Title: '',
  Website: '',
  Description: '',
  Status: 'New',
  Phone: '',
  Email: '',
  Street: '',
  City: '',
  State: '',
  PostalCode: '',
  Country: '',
  NumberOfEmployees: undefined,
  AnnualRevenue: undefined,
  LeadSource: '',
  Industry: '',
}

export function EditorLeadDialog({
  open,
  onClose,
  organizationId,
  lead,
  parameters,
}: EditorLeadDialogProps) {
  const t = useTranslations('Organizations.salesforce.sales')
  const isEditing = !!lead
  const createLead = useCreateSalesforceLead(organizationId, parameters)
  const updateLead = useUpdateSalesforceLead(organizationId, parameters)

  const form = useForm<LeadFormValues>({
    resolver: zodResolver(leadSchema),
    defaultValues,
  })

  useEffect(() => {
    if (!open) {
      form.reset(defaultValues)
      return
    }
    if (!lead) return
    form.reset({
      FirstName: lead.FirstName || '',
      LastName: lead.LastName || '',
      Company: lead.Company || '',
      Title: lead.Title || '',
      Website: lead.Website || '',
      Description: lead.Description || '',
      Status: lead.Status || 'New',
      Phone: lead.Phone || '',
      Email: lead.Email || '',
      Street: lead.Street || '',
      City: lead.City || '',
      State: lead.State || '',
      PostalCode: lead.PostalCode || '',
      Country: lead.Country || '',
      NumberOfEmployees: lead.NumberOfEmployees || undefined,
      AnnualRevenue: lead.AnnualRevenue || undefined,
      LeadSource: lead.LeadSource || '',
      Industry: lead.Industry || '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lead, open])

  const onSubmit = async (values: LeadFormValues) => {
    try {
      const {
        LastName,
        Company,
        FirstName,
        Email,
        Phone,
        Website,
        Title,
        Status,
        Description,
        ...rest
      } = values
      const payload = {
        LastName,
        Company,
        FirstName,
        Email,
        Phone,
        Website,
        Title,
        Status,
        Description,
        additionalFields: rest,
      }
      if (isEditing && lead) {
        await updateLead.mutateAsync({
          recordId: lead.Id,
          ...payload,
        })
        toast.success(t('edit.success'))
      } else {
        await createLead.mutateAsync(payload)
        toast.success(t('add.success'))
      }
      onClose()
      form.reset()
    } catch (error) {
      console.error('Error saving lead:', error)
      toast.error(isEditing ? t('edit.error') : t('add.error'))
    }
  }

  const isPending = createLead.isPending || updateLead.isPending

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="px-0 sm:max-w-[600px]">
        <DialogHeader className="px-4">
          <DialogTitle>{isEditing ? t('edit.title') : t('add.title')}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <div className="flex max-h-[70vh] flex-col gap-4 overflow-y-auto px-4">
              {/* About */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">{t('form.about')}</h3>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="FirstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.firstName')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="LastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.lastName')}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="Company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.company')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="Title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.title')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="Website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.website')}</FormLabel>
                        <FormControl>
                          <Input type="url" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="Description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.description')}</FormLabel>
                      <FormControl>
                        <Textarea {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="Status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.leadStatus')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('form.selectLeadStatus')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {SALESFORCE_LEAD_STATUS.map(status => (
                            <SelectItem key={status.id} value={status.id}>
                              {status.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Get in Touch */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {t('form.getInTouch')}
                </h3>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="Phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.phone')}</FormLabel>
                        <FormControl>
                          <PhoneInput
                            defaultCountry="US"
                            placeholder={t('form.phonePlaceholder')}
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="Email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.email')}</FormLabel>
                        <FormControl>
                          <Input type="email" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="Street"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.street')}</FormLabel>
                      <FormControl>
                        <Textarea {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="City"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.city')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="State"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.state')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="PostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.postalCode')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="Country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.country')}</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Segment */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">{t('form.segment')}</h3>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="NumberOfEmployees"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.employees')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="AnnualRevenue"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.annualRevenue')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="LeadSource"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.leadSource')}</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectLeadSource')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {LEAD_SOURCE.map(source => (
                              <SelectItem key={source.id} value={source.id}>
                                {source.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="Industry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('form.industry')}</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('form.selectIndustry')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {INDUSTRY_TYPES.map(industry => (
                              <SelectItem key={industry.id} value={industry.id}>
                                {industry.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <DialogFooter className="px-4 py-4">
              <Button variant="outline" type="button" onClick={onClose}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? t('save') : t('create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
