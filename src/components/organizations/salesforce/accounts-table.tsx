'use client'
import { Edit, Loader2, More<PERSON><PERSON><PERSON><PERSON> } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import { toast } from 'sonner'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useDeleteSalesforceAccount } from '@/query/mutations/crm.mutation'
import { GetSalesforceAccountsParameters } from '@/services/api/types'

export function AccountsTableSkeleton() {
  const t = useTranslations('Organizations.salesforce.accounts')

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('table.name')}</TableHead>
          <TableHead>{t('table.phone')}</TableHead>
          <TableHead>{t('table.type')}</TableHead>
          <TableHead>{t('table.website')}</TableHead>
          <TableHead className="w-[100px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...Array(5)].map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-4 w-[200px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[150px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[100px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[180px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="ml-auto h-8 w-8 rounded-md" />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export function AccountsTable({
  accounts,
  isLoading,
  onEdit,
  organizationId,
  parameters,
}: {
  accounts: App.ParagonSalesforceAccount[]
  isLoading: boolean
  onEdit: (account: App.ParagonSalesforceAccount) => void
  organizationId: string
  parameters: GetSalesforceAccountsParameters
}) {
  const t = useTranslations('Organizations.salesforce.accounts')

  const { canManageSalesforceAccounts } = useOrganizationPermissions()
  const [selectedLoadingAccount, setSelectedLoadingAccount] = useState<Record<string, boolean>>({})
  const deleteAccount = useDeleteSalesforceAccount(organizationId, parameters)
  const [selectedAccountToDelete, setSelectedAccountToDelete] =
    useState<App.ParagonSalesforceAccount | null>(null)

  const handleEditAccount = (account: App.ParagonSalesforceAccount) => () => {
    onEdit(account)
  }

  // const handleDeleteAccount = (account: App.ParagonSalesforceAccount) => () => {
  //   setSelectedAccountToDelete(account)
  // }

  const handleDeleteAccountConfirm = async () => {
    if (!selectedAccountToDelete) return
    try {
      setSelectedLoadingAccount({ [selectedAccountToDelete.Id]: true })
      await deleteAccount.mutateAsync({
        recordId: selectedAccountToDelete.Id,
      })
      toast.success(t('delete.success'))
    } catch {
      toast.error(t('delete.error'))
    } finally {
      setSelectedLoadingAccount({ [selectedAccountToDelete.Id]: false })
      setSelectedAccountToDelete(null)
    }
  }

  const renderActions = (account: App.ParagonSalesforceAccount) => {
    if (!canManageSalesforceAccounts) return null
    if (selectedLoadingAccount[account.Id]) return <Loader2 className="h-4 w-4 animate-spin" />
    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('table.actions.open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={handleEditAccount(account)}>
            <Edit className="mr-2 h-4 w-4" />
            {t('table.actions.edit')}
          </DropdownMenuItem>
          {/* <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={handleDeleteAccount(account)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t('table.actions.delete')}
          </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }
  if (isLoading) {
    return <AccountsTableSkeleton />
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.name')}</TableHead>
            <TableHead>{t('table.phone')}</TableHead>
            <TableHead>{t('table.type')}</TableHead>
            <TableHead>{t('table.website')}</TableHead>
            <TableHead className="w-[100px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {accounts.map(account => (
            <TableRow key={account.Id}>
              <TableCell className="font-medium">{account.Name}</TableCell>
              <TableCell>{account.Phone}</TableCell>
              <TableCell>{account.Type}</TableCell>
              <TableCell>{account.Website}</TableCell>
              <TableCell>{renderActions(account)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <AlertDialog
        open={!!selectedAccountToDelete}
        onOpenChange={() => setSelectedAccountToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('delete.title')}</AlertDialogTitle>
            <AlertDialogDescription>{t('delete.description')}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('delete.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAccountConfirm}
              disabled={deleteAccount.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {t('delete.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
