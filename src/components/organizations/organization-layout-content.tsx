/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import { useTranslations } from 'next-intl'
import { Fragment, JSXElementConstructor, ReactElement, ReactNode, ReactPortal } from 'react'

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import { Link, usePathname } from '@/i18n/navigation'
import { useOrganizations } from '@/providers/organizations-provider'

interface Breadcrumb {
  href: string
  isLast: boolean
  label:
    | string
    | number
    | bigint
    | boolean
    | ReactElement<unknown, string | JSXElementConstructor<any>>
    | Iterable<ReactNode>
    | ReactPortal
    | Promise<
        | string
        | number
        | bigint
        | boolean
        | ReactPortal
        | ReactElement<unknown, string | JSXElementConstructor<any>>
        | Iterable<ReactNode>
        | null
        | undefined
      >
    | null
    | undefined
}

export function OrganizationLayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const t = useTranslations('Organizations')
  const { currentOrganization } = useOrganizations()

  const getBreadcrumbs = (): Breadcrumb[] => {
    const paths = pathname.split('/').filter(Boolean)
    return paths.map((path: string) => {
      const href = '/' + paths.slice(0, paths.indexOf(path) + 1).join('/')
      const isLast = path === paths[paths.length - 1]

      switch (path) {
        case 'organizations':
          return {
            href,
            label: t('breadcrumb.organizations'),
            isLast,
          }
        case currentOrganization?.id:
          return {
            href,
            label: currentOrganization?.name || t('breadcrumb.loading'),
            isLast,
          }
        case 'agents':
          return {
            href,
            label: t('breadcrumb.agents'),
            isLast,
          }
        case 'settings':
          return {
            href,
            label: t('breadcrumb.settings'),
            isLast,
          }
        case 'history': {
          return {
            href,
            label: t('breadcrumb.history'),
            isLast,
          }
        }
        default:
          return {
            href,
            label: path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' '),
            isLast,
          }
      }
    })
  }

  return (
    <SidebarInset>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              {getBreadcrumbs().map(crumb => (
                <Fragment key={crumb.href}>
                  <BreadcrumbItem className="hidden md:block">
                    {crumb.isLast ? (
                      <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink asChild>
                        <Link href={crumb.href}>{crumb.label}</Link>
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                  {!crumb.isLast && <BreadcrumbSeparator className="hidden md:block" />}
                </Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      {children}
    </SidebarInset>
  )
}
