import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useDeletePhone } from '@/query/mutations/phones.mutation'
import { formatDate } from '@/utils/time'

interface PhonesTableProps {
  phones: App.Phone[]
  isLoading: boolean
}

export function PhonesTable({ phones, isLoading }: PhonesTableProps) {
  const t = useTranslations('Organizations.phones')
  const deletePhone = useDeletePhone()
  const { canManagePhones } = useOrganizationPermissions()
  const [phoneToDelete, setPhoneToDelete] = useState<App.Phone | null>(null)
  const [selectedLoadingPhone, setSelectedLoadingPhone] = useState<Record<string, boolean>>({})

  if (isLoading) {
    return <PhoneSkeleton />
  }

  const handleDeletePhone = async () => {
    if (!phoneToDelete) return
    try {
      setSelectedLoadingPhone({ [phoneToDelete.id]: true })
      await deletePhone.mutateAsync({
        organizationId: phoneToDelete.organization_id,
        phoneId: phoneToDelete.id,
      })
    } catch {
    } finally {
      setSelectedLoadingPhone({ [phoneToDelete.id]: false })
    }
  }
  const renderActions = (phone: App.Phone) => {
    if (!canManagePhones) return null
    if (selectedLoadingPhone[phone.id]) return <Loader2 className="h-4 w-4 animate-spin" />
    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('table.actions.open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={() => setPhoneToDelete(phone)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t('table.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.phoneNumber')}</TableHead>
            <TableHead>{t('table.label')}</TableHead>
            <TableHead>{t('table.provider')}</TableHead>
            <TableHead>{t('table.createdAt')}</TableHead>
            <TableHead className="flex w-[100px] items-center justify-end"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {phones.map(phone => (
            <TableRow key={phone.id}>
              <TableCell className="font-medium">{phone.phone_number}</TableCell>
              <TableCell>{phone.label}</TableCell>
              <TableCell className="capitalize">{phone.provider}</TableCell>
              <TableCell>{formatDate(phone.created_at)}</TableCell>
              <TableCell className="flex items-center justify-end">
                {renderActions(phone)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={!!phoneToDelete} onOpenChange={() => setPhoneToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('deleteDialog.description', { phone: phoneToDelete?.phone_number ?? '' })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('deleteDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePhone}
              disabled={deletePhone.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deletePhone.isPending ? t('deleteDialog.deleting') : t('deleteDialog.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

const PhoneSkeleton = () => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>
            <Skeleton className="h-4 w-[120px]" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-[100px]" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-[80px]" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-[100px]" />
          </TableHead>
          <TableHead className="w-[100px]" />
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...Array(5)].map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-4 w-[140px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[100px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[80px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[120px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-8 w-8 rounded-md" />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
