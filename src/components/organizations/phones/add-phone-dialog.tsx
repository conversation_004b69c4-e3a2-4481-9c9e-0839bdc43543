import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAddPhone } from '@/query/mutations/phones.mutation'

const formSchema = z.object({
  phone_number: z
    .string()
    .min(1, 'Phone number is required')
    .refine(isValidPhoneNumber, { message: 'Invalid phone number' }),
  label: z.string().min(1, 'Label is required'),
  provider: z.enum(['twilio', 'sip_trunk']),
  sid: z.string().optional(),
  token: z.string().optional(),
  termination_uri: z.string().optional(),
  credentials: z
    .object({
      username: z.string().optional(),
      password: z.string().optional(),
    })
    .optional(),
})

interface AddPhoneDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function AddPhoneDialog({ open, onClose, organizationId }: AddPhoneDialogProps) {
  const t = useTranslations('Organizations.phones')
  const addPhone = useAddPhone()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone_number: '',
      label: '',
      provider: 'twilio',
      sid: '',
      token: '',
      termination_uri: '',
      credentials: {
        username: '',
        password: '',
      },
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await addPhone.mutateAsync({
      ...values,
      organization_id: organizationId,
    })
    onClose()
    form.reset()
  }

  const provider = form.watch('provider')

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="px-0">
        <DialogHeader className="px-4">
          <DialogTitle>{t('addDialog.title')}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex w-full flex-col gap-4">
            <div className="flex max-h-[80vh] flex-col gap-4 overflow-y-auto px-4">
              <FormField
                control={form.control}
                name="phone_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('addDialog.phoneNumber')}</FormLabel>
                    <FormControl>
                      <PhoneInput
                        defaultCountry="US"
                        placeholder={t('addDialog.phoneNumberPlaceholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="label"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('addDialog.label')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('addDialog.labelPlaceholder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="provider"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('addDialog.provider')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('addDialog.selectProvider')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="twilio">Twilio</SelectItem>
                        <SelectItem value="sip_trunk">SIP Trunk</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {provider === 'twilio' && (
                <>
                  <FormField
                    control={form.control}
                    name="sid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('addDialog.twilioSid')}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('addDialog.twilioSidPlaceholder')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="token"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('addDialog.twilioToken')}</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder={t('addDialog.twilioTokenPlaceholder')}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {provider === 'sip_trunk' && (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle>{t('addDialog.outboundConfiguration')}</CardTitle>
                      <CardDescription>
                        {t('addDialog.outboundConfigurationDescription')}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="termination_uri"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('addDialog.terminationUri')}</FormLabel>
                            <FormControl>
                              <Input placeholder="sip:your-sip-trunk-uri" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-2">
                        <h3 className="text-lg font-medium">{t('addDialog.authentication')}</h3>
                        <p className="text-sm text-muted-foreground">
                          {t('addDialog.authenticationDescription')}
                        </p>
                      </div>

                      <FormField
                        control={form.control}
                        name="credentials.username"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('addDialog.sipUsername')}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('addDialog.sipUsernamePlaceholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="credentials.password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('addDialog.sipPassword')}</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder={t('addDialog.sipPasswordPlaceholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </>
              )}
            </div>

            <div className="flex justify-end space-x-2 px-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={addPhone.isPending}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={addPhone.isPending}>
                {addPhone.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('add')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
