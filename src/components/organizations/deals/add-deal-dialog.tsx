'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { DatePicker } from '@/components/ui/date-picker'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DEAL_PRIORITIES, DEAL_STAGES } from '@/constants/crm'
import { useCreateDeal } from '@/query/mutations/crm.mutation'

interface AddDealDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
  connectionId: string
}

const formSchema = z.object({
  name: z.string().min(1, { message: 'Deal name is required' }),
  amount: z.coerce
    .number()
    .optional()
    .transform(val => (val ? val : undefined)),
  pipeline: z.string().min(1, { message: 'Pipeline is required' }),
  stage: z.string().min(1, { message: 'Stage is required' }),
  close_date: z.date().optional(),
  priority: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export function AddDealDialog({ open, onClose, organizationId, connectionId }: AddDealDialogProps) {
  const t = useTranslations('Organizations.deals')
  const createDeal = useCreateDeal(organizationId)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      amount: undefined,
      pipeline: '',
      stage: '',
      close_date: undefined,
      priority: '',
    },
  })

  const onSubmit = async (values: FormValues) => {
    await createDeal.mutateAsync({
      connectionId,
      dealData: {
        name: values.name,
        amount: values.amount,
        pipeline: values.pipeline || undefined,
        stage: values.stage || undefined,
        close_date: values.close_date ? dayjs(values.close_date).format('YYYY-MM-DD') : undefined,
        priority: values.priority || undefined,
      },
    })
    onClose()
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('addDialog.title')}</DialogTitle>
          <DialogDescription>{t('addDialog.description')}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.name')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pipeline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.pipeline')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('form.selectPipeline')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="default">Sales Pipeline</SelectItem>
                        {/* <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem> */}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="stage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.stage')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('form.selectStage')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {DEAL_STAGES.map(stage => (
                          <SelectItem key={stage.id} value={stage.id}>
                            {stage.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.amount')}</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" step="0.01" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="close_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.closeDate')}</FormLabel>
                  <FormControl>
                    <DatePicker value={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.priority')}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('form.selectPriority')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {DEAL_PRIORITIES.map(priority => (
                        <SelectItem key={priority.id} value={priority.id}>
                          {priority.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button variant="outline" onClick={onClose} type="button">
                {t('form.cancel')}
              </Button>
              <Button type="submit" disabled={createDeal.isPending}>
                {createDeal.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('form.create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
