'use client'

import { <PERSON>, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useDeleteDeal } from '@/query/mutations/crm.mutation'
import { formatCurrency } from '@/utils/format'

interface DealsTableProps {
  organizationId: string
  deals: App.CachedCRMDeal[]
  isLoading: boolean
  onEdit: (deal: App.CachedCRMDeal) => void
}

export function DealsTable({ organizationId, deals, onEdit }: DealsTableProps) {
  const t = useTranslations('Organizations.deals')
  const { canManageDeals } = useOrganizationPermissions()
  const [dealToDelete, setDealToDelete] = useState<App.CachedCRMDeal | null>(null)
  const [selectedLoadingDeal, setSelectedLoadingDeal] = useState<Record<string, boolean>>({})
  const deleteDeal = useDeleteDeal(organizationId)

  const handleDeleteDeal = async () => {
    if (!dealToDelete) return

    setSelectedLoadingDeal(prev => ({ ...prev, [dealToDelete.id]: true }))
    try {
      await deleteDeal.mutateAsync({
        connectionId: dealToDelete.crm_connection_id,
        dealId: dealToDelete.crm_entity_id,
      })
    } finally {
      setSelectedLoadingDeal(prev => ({ ...prev, [dealToDelete.id]: false }))
      setDealToDelete(null)
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString()
  }

  const renderActions = (deal: App.CachedCRMDeal) => {
    if (!canManageDeals) return null
    if (selectedLoadingDeal[deal.id]) return <Loader2 className="h-4 w-4 animate-spin" />
    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('table.actions.open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={() => onEdit(deal)}>
            <Edit className="mr-2 h-4 w-4" />
            {t('table.actions.edit')}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={() => setDealToDelete(deal)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t('table.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('table.name')}</TableHead>
              <TableHead>{t('table.amount')}</TableHead>
              <TableHead>{t('table.stage')}</TableHead>
              <TableHead>{t('table.closeDate')}</TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {deals.map(deal => (
              <TableRow key={deal.id}>
                <TableCell className="font-medium">{deal.name}</TableCell>
                <TableCell>{deal.amount ? formatCurrency(deal.amount) : '-'}</TableCell>
                <TableCell>
                  <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium">
                    {deal.stage || '-'}
                  </span>
                </TableCell>
                <TableCell>{formatDate(deal.close_date)}</TableCell>
                <TableCell>{renderActions(deal)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={!!dealToDelete} onOpenChange={() => setDealToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('deleteDialog.description', { name: dealToDelete?.name ?? '' })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('deleteDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDeal}
              disabled={deleteDeal.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteDeal.isPending ? t('deleteDialog.deleting') : t('deleteDialog.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
