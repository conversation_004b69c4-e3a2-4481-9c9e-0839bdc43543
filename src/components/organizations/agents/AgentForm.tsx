'use client'

import { Language, useConversation } from '@11labs/react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2, PlusCircle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { CallButton } from '@/components/call-button'
import { SelectKnowledgeBaseDialog } from '@/components/organizations/knowledge-base/select-knowledge-base-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import {
  AGENT_LANGUAGES_SUPPORTED,
  AGENT_LLM_OPTIONS_SUPPORTED,
  TSS_OUTPUT_FORMATS,
} from '@/constants/agents'
import { useGetAgentSignedUrl } from '@/query/mutations/agents.mutation'
import { useKnowledgeBaseRagIndexStatus } from '@/query/mutations/knowledge-base.mutation'
import { useGetVoices } from '@/query/queries/agents.query'
import { UpdateAgentPayload } from '@/services/api/types'
import { logger } from '@/utils/logger'

// Define the Zod schema for form validation based on App.Agent
const agentFormSchema = z.object({
  agent_name: z.string().min(2, { message: 'Agent name must be at least 2 characters.' }),
  first_message_prompt: z.string().optional(),
  system_prompt: z.string().optional(),
  language: z.string().default('en'),
  max_tokens: z.coerce.number().int().default(-1),
  llm: z.string().default('gemini-2.0-flash-001'),
  temperature: z.number().min(0).max(1).default(0),
  turn_timeout: z.coerce.number().int().positive().optional(),
  max_duration_seconds: z.coerce.number().int().positive().optional(),
  voice_id: z.string().optional().default('9BWtsMINqrJLrRacOk9x'),
  model_id: z.string().default('eleven_turbo_v2'),
  agent_output_audio_format: z.string().default('pcm_16000'),
  optimize_streaming_latency: z.number().min(0).max(4).default(3),
  stability: z.number().min(0).max(1).default(0.5),
  speed: z.number().min(0.7).max(1.2).default(1),
  similarity_boost: z.number().min(0).max(1).default(0.8),
  rag_enabled: z.boolean().default(false),
  knowledge_bases: z
    .array(
      z.object({
        id: z.string(),
        eleven_labs_id: z.string(),
        name: z.string(),
        type: z.enum(['file', 'url', 'text']),
      }),
    )
    .default([]),
})

export type AgentFormValues = z.infer<typeof agentFormSchema>

interface AgentFormProps {
  initialData?: App.Agent | null
  onSubmit: (values: UpdateAgentPayload) => Promise<void>
  isSubmitting: boolean
  submitButtonText?: string
}

const defaultValues: AgentFormValues = {
  agent_name: '',
  first_message_prompt: '',
  system_prompt: '',
  language: 'en',
  max_tokens: -1,
  llm: 'gemini-2.0-flash-001',
  temperature: 0,
  turn_timeout: undefined,
  max_duration_seconds: undefined,
  voice_id: '9BWtsMINqrJLrRacOk9x',
  model_id: 'eleven_turbo_v2',
  agent_output_audio_format: 'pcm_16000',
  optimize_streaming_latency: 3,
  stability: 0.5,
  speed: 1,
  similarity_boost: 0.8,
  rag_enabled: false,
  knowledge_bases: [],
}

export function AgentForm({
  initialData,
  onSubmit,
  isSubmitting,
  submitButtonText = 'Save Agent',
}: AgentFormProps) {
  const t = useTranslations('Organizations.agents.form')
  const [activeTab, setActiveTab] = useState('agent')
  const { data: voices = [] } = useGetVoices()

  // Add state for knowledge base dialog
  const [isKnowledgeBaseDialogOpen, setIsKnowledgeBaseDialogOpen] = useState(false)
  const organizationId = initialData?.organization_id || ''

  // Add state to track RAG indexing status
  const [indexingStatuses, setIndexingStatuses] = useState<Record<string, string>>({})
  const checkRagIndexStatus = useKnowledgeBaseRagIndexStatus()

  const getAgentSignedUrl = useGetAgentSignedUrl()
  const form = useForm({
    resolver: zodResolver(agentFormSchema),
    defaultValues,
  })

  const [callStatus, setCallStatus] = useState<
    'idle' | 'connecting' | 'connected' | 'disconnecting'
  >('idle')

  const conversation = useConversation({
    onConnect: () => {
      setCallStatus('connected')
      toast.info('Connected to agent')
    },
    onDisconnect: () => {
      setCallStatus('idle')
      toast.info('Disconnected from agent')
    },
    onMessage: message => toast.info(`Message: ${message.message}`),
    onError: error => {
      setCallStatus('idle')
      toast.error(`Error: ${error}`)
    },
  })
  const startConversation = useCallback(async () => {
    if (!initialData?.agent_id) return
    setCallStatus('connecting')
    const editablePrompt = form.getValues('system_prompt')
    const editableFirstMessage = form.getValues('first_message_prompt')
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true })

      const signedUrlResult = await getAgentSignedUrl.mutateAsync(initialData.agent_id)

      await conversation.startSession({
        signedUrl: signedUrlResult.signed_url,
        overrides: {
          agent: {
            prompt: {
              prompt: editablePrompt,
            },
            firstMessage: editableFirstMessage,
            language: form.getValues('language') as Language,
          },
          tts: {
            voiceId: form.getValues('voice_id'),
          },
        },
      })
    } catch (error) {
      logger.error(error)
      setCallStatus('idle')
      toast.error('Failed to start conversation')
    }
  }, [initialData?.agent_id, form, getAgentSignedUrl, conversation])

  const stopConversation = useCallback(async () => {
    setCallStatus('disconnecting')
    await conversation.endSession()
  }, [conversation])

  // Reset form if initialData changes (e.g., navigating between agents)
  useEffect(() => {
    if (initialData) {
      form.reset({
        agent_name: initialData.agent_name || '',
        first_message_prompt: initialData.first_message_prompt || '',
        system_prompt: initialData.system_prompt || '',
        language: initialData.language || 'en',
        llm: initialData.llm || 'gemini-2.0-flash-001',
        temperature: initialData.temperature || 0,
        turn_timeout: initialData.turn_timeout || undefined,
        max_duration_seconds: initialData.max_duration_seconds || undefined,
        voice_id: initialData.voice_id || '9BWtsMINqrJLrRacOk9x',
        model_id: initialData.model_id || 'eleven_turbo_v2',
        agent_output_audio_format: initialData.agent_output_audio_format || 'pcm_16000',
        optimize_streaming_latency: initialData.optimize_streaming_latency || 3,
        stability: initialData.stability || 0.5,
        speed: initialData.speed || 1,
        similarity_boost: initialData.similarity_boost || 0.8,
        max_tokens: initialData.max_tokens ?? -1,
        rag_enabled: initialData.rag_enabled || false,
        knowledge_bases: initialData.knowledge_bases || [],
      })
      setIndexingStatuses(
        (initialData.knowledge_bases ?? []).reduce(
          (acc, kb) => {
            acc[kb.id] = 'succeeded'
            return acc
          },
          {} as Record<string, string>,
        ),
      )
    } else {
      setIndexingStatuses({})
      form.reset(defaultValues)
    }
  }, [initialData, form])

  const handleSubmit = form.handleSubmit(async values => {
    const { knowledge_bases = [], ...rest } = values
    await onSubmit({
      ...rest,
      knowledge_bases: knowledge_bases.map(kb => ({
        id: kb.id,
        name: kb.name,
        type: kb.type,
        usage_mode: 'auto',
        eleven_labs_id: kb.eleven_labs_id,
      })),
    })
  })

  // Function to check RAG index status for a knowledge base
  const checkKnowledgeBaseIndexStatus = useCallback(
    async (knowledgeBaseId: string) => {
      try {
        const result = await checkRagIndexStatus.mutateAsync(knowledgeBaseId)
        setIndexingStatuses(prev => ({ ...prev, [knowledgeBaseId]: result.status }))

        // If still processing, poll again after a delay
        if (result.status === 'processing' || result.status === 'created') {
          setTimeout(() => checkKnowledgeBaseIndexStatus(knowledgeBaseId), 3000)
        }
      } catch (error) {
        console.error('Failed to check RAG index status:', error)
        setIndexingStatuses(prev => ({ ...prev, [knowledgeBaseId]: 'failed' }))
      }
    },
    [checkRagIndexStatus],
  )

  // Enhanced handler for knowledge base selection
  const handleKnowledgeBaseSelect = (selectedKnowledgeBases: App.KnowledgeBase[]) => {
    const currentKnowledgeBases = form.getValues('knowledge_bases') || []
    const currentIds = new Set(currentKnowledgeBases.map(kb => kb.id))

    // Add only new knowledge bases
    const newKnowledgeBases = [
      ...currentKnowledgeBases,
      ...selectedKnowledgeBases
        .filter(kb => !currentIds.has(kb.id))
        .map(kb => ({
          id: kb.id,
          eleven_labs_id: kb.eleven_labs_id,
          name: kb.name,
          type: kb.type,
        })),
    ]

    form.setValue('knowledge_bases', newKnowledgeBases)

    // Check RAG index status for newly added knowledge bases
    selectedKnowledgeBases
      .filter(kb => !currentIds.has(kb.id))
      .forEach(kb => {
        setIndexingStatuses(prev => ({ ...prev, [kb.id]: 'created' }))
        checkKnowledgeBaseIndexStatus(kb.id)
      })
  }

  // Get the status badge for a knowledge base
  const getStatusBadge = (knowledgeBaseId: string) => {
    const status = indexingStatuses[knowledgeBaseId]

    if (!status) return null

    switch (status) {
      case 'created':
      case 'processing':
        return (
          <Badge variant="outline" className="ml-2 animate-pulse bg-amber-100 text-amber-800">
            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
            {t('knowledge.indexing')}
          </Badge>
        )
      case 'succeeded':
        return (
          <Badge variant="outline" className="ml-2 bg-green-100 text-green-800">
            {t('knowledge.indexed')}
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="outline" className="ml-2 bg-red-100 text-red-800">
            {t('knowledge.indexFailed')}
          </Badge>
        )
      default:
        return null
    }
  }

  // Add handler to remove knowledge base
  const handleRemoveKnowledgeBase = (id: string) => {
    const currentKnowledgeBases = form.getValues('knowledge_bases') || []
    form.setValue(
      'knowledge_bases',
      currentKnowledgeBases.filter(kb => kb.id !== id),
    )
  }

  const knowledgeBases = form.watch('knowledge_bases')

  return (
    <>
      {initialData && (
        <div className="my-6 flex justify-center">
          <CallButton
            status={callStatus}
            isSpeaking={conversation.isSpeaking}
            onStartCall={startConversation}
            onEndCall={stopConversation}
          />
        </div>
      )}

      <Form {...form}>
        <form onSubmit={handleSubmit} className="space-y-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList>
              <TabsTrigger value="agent">Agent</TabsTrigger>
              <TabsTrigger value="voice">Voice</TabsTrigger>
              <TabsTrigger value="knowledge">Knowledge</TabsTrigger>
            </TabsList>

            <TabsContent value="agent" className="space-y-6 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('language.title')}</CardTitle>
                  <CardDescription>{t('language.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="language"
                    render={({ field }) => (
                      <FormItem>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select language" />
                          </SelectTrigger>
                          <SelectContent>
                            {AGENT_LANGUAGES_SUPPORTED.map(language => (
                              <SelectItem key={language.value} value={language.value}>
                                {language.text}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <FormField
                control={form.control}
                name="agent_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('agentName.label')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('agentName.placeholder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="first_message_prompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('firstMessagePrompt.label')}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('firstMessagePrompt.placeholder')}
                        {...field}
                        rows={3}
                      />
                    </FormControl>
                    <FormDescription>{t('firstMessagePrompt.description')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="system_prompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('systemPrompt.label')}</FormLabel>
                    <FormControl>
                      <Textarea placeholder={t('systemPrompt.placeholder')} {...field} rows={6} />
                    </FormControl>
                    <FormDescription>{t('systemPrompt.description')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Card>
                <CardHeader>
                  <div className="space-y-2">
                    <p className="text-md font-medium text-foreground">{t('llm.title')}</p>
                    <p className="text-sm font-normal text-muted-foreground">
                      {t('llm.description')}
                    </p>
                    <p className="mt-1 text-sm font-normal text-muted-foreground">
                      {t('llm.redirectNotice')}
                    </p>
                    <p className="mt-1 text-sm font-normal text-muted-foreground">
                      {t('llm.costNotice')}
                    </p>
                  </div>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="llm"
                    render={({ field }) => (
                      <FormItem>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select LLM" />
                          </SelectTrigger>
                          <SelectContent>
                            {AGENT_LLM_OPTIONS_SUPPORTED.map(llm => (
                              <SelectItem key={llm.value} value={llm.value}>
                                {llm.text}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('temperature.title')}</CardTitle>
                  <CardDescription>{t('temperature.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="temperature"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <FormLabel>{field.value?.toFixed(2)}</FormLabel>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">Precise</span>
                              <span className="text-sm text-muted-foreground">Creative</span>
                            </div>
                          </div>
                          <FormControl>
                            <Slider
                              min={0}
                              max={1}
                              step={0.05}
                              value={[field.value ?? 0]}
                              onValueChange={values => field.onChange(values[0])}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('maxTokens.title')}</CardTitle>
                  <CardDescription>{t('maxTokens.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="max_tokens"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder={t('maxTokens.placeholder')}
                            {...field}
                            type="number"
                            value={field.value}
                            onChange={e => {
                              if (isNaN(parseInt(e.target.value))) {
                                field.onChange(e.target.value)
                                return
                              }
                              field.onChange(e.target.valueAsNumber)
                            }}
                          />
                        </FormControl>
                        <FormDescription>{t('maxTokens.hint')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="turn_timeout"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('turnTimeout.label')}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder={t('turnTimeout.placeholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>{t('turnTimeout.description')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="max_duration_seconds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('maxDuration.label')}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder={t('maxDuration.placeholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>{t('maxDuration.description')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </TabsContent>

            <TabsContent value="voice" className="space-y-6 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('voice.title')}</CardTitle>
                  <CardDescription>{t('voice.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="voice_id"
                    render={({ field }) => (
                      <FormItem>
                        <Select onValueChange={field.onChange} value={field.value || ''}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={t('voice.placeholder')} />
                          </SelectTrigger>
                          <SelectContent>
                            {voices.map(voice => (
                              <SelectItem key={voice.voice_id} value={voice.voice_id}>
                                {voice.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('modelType.title')}</CardTitle>
                  <CardDescription>{t('modelType.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="model_id"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            {field.value === 'eleven_flash_v2'
                              ? t('modelType.useFlash')
                              : t('modelType.useTurbo')}
                          </FormLabel>
                          <FormDescription>
                            {field.value === 'eleven_flash_v2'
                              ? t('modelType.flashDescription')
                              : t('modelType.turboDescription')}
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value === 'eleven_flash_v2'}
                            onCheckedChange={checked =>
                              field.onChange(checked ? 'eleven_flash_v2' : 'eleven_turbo_v2')
                            }
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('audioFormat.title')}</CardTitle>
                  <CardDescription>{t('audioFormat.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="agent_output_audio_format"
                    render={({ field }) => (
                      <FormItem>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={t('audioFormat.placeholder')} />
                          </SelectTrigger>
                          <SelectContent>
                            {TSS_OUTPUT_FORMATS.map(format => (
                              <SelectItem key={format.value} value={format.value}>
                                {format.text}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('latency.title')}</CardTitle>
                  <CardDescription>{t('latency.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="optimize_streaming_latency"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <FormLabel>{field.value}</FormLabel>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {t('latency.quality')}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {t('latency.speed')}
                              </span>
                            </div>
                          </div>
                          <FormControl>
                            <Slider
                              min={0}
                              max={4}
                              step={1}
                              value={[field.value ?? 0]}
                              onValueChange={values => field.onChange(values[0])}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('stability.title')}</CardTitle>
                  <CardDescription>{t('stability.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="stability"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <FormLabel>{field.value?.toFixed(2)}</FormLabel>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {t('stability.expressive')}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {t('stability.consistent')}
                              </span>
                            </div>
                          </div>
                          <FormControl>
                            <Slider
                              min={0}
                              max={1}
                              step={0.05}
                              value={[field.value ?? 0]}
                              onValueChange={values => field.onChange(values[0])}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('speed.title')}</CardTitle>
                  <CardDescription>{t('speed.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="speed"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <FormLabel>{field.value?.toFixed(2)}</FormLabel>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {t('speed.slower')}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {t('speed.faster')}
                              </span>
                            </div>
                          </div>
                          <FormControl>
                            <Slider
                              min={0.7}
                              max={1.2}
                              step={0.01}
                              value={[field.value ?? 0]}
                              onValueChange={values => field.onChange(values[0])}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('similarity.title')}</CardTitle>
                  <CardDescription>{t('similarity.description')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="similarity_boost"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <FormLabel>{field.value?.toFixed(2)}</FormLabel>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {t('similarity.less')}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {t('similarity.more')}
                              </span>
                            </div>
                          </div>
                          <FormControl>
                            <Slider
                              min={0}
                              max={1}
                              step={0.05}
                              value={[field.value ?? 0]}
                              onValueChange={values => field.onChange(values[0])}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="knowledge" className="space-y-6 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>{t('knowledge.title')}</CardTitle>
                  <CardDescription>{t('knowledge.description')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-4">
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsKnowledgeBaseDialogOpen(true)}>
                      <PlusCircle className="mr-2 h-4 w-4" />
                      {t('knowledge.addDocuments')}
                    </Button>

                    {!!knowledgeBases?.length ? (
                      <ScrollArea className="h-[200px] rounded-md border p-4">
                        <div className="space-y-2">
                          {knowledgeBases.map(kb => (
                            <div
                              key={kb.id}
                              className="flex items-center justify-between rounded-md border p-2">
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="capitalize">
                                  {kb.type}
                                </Badge>
                                <div className="flex items-center">
                                  <span className="text-sm font-medium">{kb.name}</span>
                                  {getStatusBadge(kb.id)}
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveKnowledgeBase(kb.id)}>
                                {t('knowledge.remove')}
                              </Button>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    ) : (
                      <div className="rounded-md border border-dashed p-8 text-center">
                        <p className="text-sm text-muted-foreground">
                          {t('knowledge.noDocuments')}
                        </p>
                      </div>
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="rag_enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">{t('knowledge.useRag')}</FormLabel>
                          <FormDescription>{t('knowledge.ragDescription')}</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {submitButtonText}
          </Button>
        </form>
      </Form>

      <SelectKnowledgeBaseDialog
        open={isKnowledgeBaseDialogOpen}
        onClose={() => setIsKnowledgeBaseDialogOpen(false)}
        onSelect={handleKnowledgeBaseSelect}
        organizationId={organizationId}
        currentSelectedIds={form.watch('knowledge_bases')?.map(kb => kb.id) || []}
      />
    </>
  )
}
