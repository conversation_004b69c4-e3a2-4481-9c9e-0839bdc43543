'use client'

import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Trash2 } from 'lucide-react' // Added Trash2 back
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator, // Add back separator
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import {
  useDeleteAgentMutation, // Import the delete mutation hook
  useUpdateAgentTemplateStatus,
} from '@/query/mutations/agents.mutation'
import { formatDate } from '@/utils/time'

interface AgentsTableProps {
  agents: App.Agent[]
  isLoading: boolean
  organizationId: string
}

export function AgentsTable({ agents, isLoading, organizationId }: AgentsTableProps) {
  const t = useTranslations('Organizations.agents.table')
  const tActions = useTranslations('Organizations.agents.table.actions')
  const updateTemplateStatus = useUpdateAgentTemplateStatus()
  const deleteAgentMutation = useDeleteAgentMutation() // Use the delete mutation hook
  const { canManageAgents } = useOrganizationPermissions()
  const [agentToToggleTemplate, setAgentToToggleTemplate] = useState<App.Agent | null>(null)
  const [agentToDelete, setAgentToDelete] = useState<App.Agent | null>(null) // Uncomment delete state
  const [selectedLoadingAgent, setSelectedLoadingAgent] = useState<Record<string, boolean>>({})

  if (isLoading) {
    return <AgentSkeleton />
  }

  const handleToggleTemplate = async () => {
    if (!agentToToggleTemplate) return
    const targetStatus = !agentToToggleTemplate.is_template
    try {
      setSelectedLoadingAgent({ [agentToToggleTemplate.agent_id]: true })
      await updateTemplateStatus.mutateAsync({
        organizationId: agentToToggleTemplate.organization_id,
        agentId: agentToToggleTemplate.agent_id,
        isTemplate: targetStatus,
      })
    } catch {
      // Error handled by mutation hook's onError
    } finally {
      setSelectedLoadingAgent({ [agentToToggleTemplate.agent_id]: false })
      setAgentToToggleTemplate(null) // Close dialog on completion
    }
  }

  // Define the delete handler
  const handleDeleteAgent = async () => {
    if (!agentToDelete) return
    try {
      setSelectedLoadingAgent({ [agentToDelete.agent_id]: true })
      await deleteAgentMutation.mutateAsync({
        organizationId: agentToDelete.organization_id,
        agentId: agentToDelete.agent_id,
      })
      // onSuccess toast is handled by the mutation hook
    } catch {
      // onError toast is handled by the mutation hook
    } finally {
      setSelectedLoadingAgent({ [agentToDelete.agent_id]: false })
      setAgentToDelete(null) // Close dialog on completion or error
    }
  }

  const renderActions = (agent: App.Agent) => {
    if (!canManageAgents) return null
    if (selectedLoadingAgent[agent.agent_id]) return <Loader2 className="h-4 w-4 animate-spin" />

    // Construct the edit link manually
    const editHref = `/organizations/${organizationId}/agents/${agent.agent_id}`

    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{tActions('open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <Link href={editHref}>
              <Pencil className="mr-2 h-4 w-4" />
              {tActions('edit')}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => setAgentToToggleTemplate(agent)}>
            <Star className="mr-2 h-4 w-4" />
            {agent.is_template ? tActions('unmarkTemplate') : tActions('markTemplate')}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={() => setAgentToDelete(agent)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {tActions('delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('templateStatus')}</TableHead>
            <TableHead>{t('createdAt')}</TableHead>
            <TableHead className="w-[100px] text-right"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {agents.map(agent => (
            <TableRow key={agent.agent_id}>
              <TableCell className="font-medium">{agent.agent_name}</TableCell>
              <TableCell>{agent.is_template ? t('yes') : t('no')}</TableCell>
              <TableCell>{formatDate(agent.created_at)}</TableCell>
              <TableCell className="text-right">{renderActions(agent)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Template Toggle Confirmation Dialog */}
      <AlertDialog
        open={!!agentToToggleTemplate}
        onOpenChange={() => setAgentToToggleTemplate(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('templateDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {agentToToggleTemplate?.is_template
                ? t('templateDialog.unmarkDescription', {
                    name: agentToToggleTemplate?.agent_name ?? '',
                  })
                : t('templateDialog.markDescription', {
                    name: agentToToggleTemplate?.agent_name ?? '',
                  })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('templateDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleToggleTemplate}
              disabled={updateTemplateStatus.isPending}>
              {updateTemplateStatus.isPending
                ? t('templateDialog.updating')
                : t('templateDialog.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!agentToDelete} onOpenChange={() => setAgentToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('deleteDialog.description', { name: agentToDelete?.agent_name ?? '' })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('deleteDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAgent}
              disabled={deleteAgentMutation.isPending} // Use correct mutation hook state
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteAgentMutation.isPending
                ? t('deleteDialog.deleting')
                : t('deleteDialog.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

const AgentSkeleton = () => {
  const t = useTranslations('Organizations.agents.table')
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('name')}</TableHead>
          <TableHead>{t('templateStatus')}</TableHead>
          <TableHead>{t('createdAt')}</TableHead>
          <TableHead className="w-[100px] text-right"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...Array(5)].map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-4 w-[200px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[50px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[120px]" />
            </TableCell>
            <TableCell className="text-right">
              <Skeleton className="ml-auto h-8 w-8 rounded-md" />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
