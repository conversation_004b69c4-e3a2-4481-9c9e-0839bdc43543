import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Loader2, Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>ooter,
  She<PERSON>Header,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet'
import { PLAYGROUND_AGENTS } from '@/constants/agents'
import { cn } from '@/lib/utils'
import { useCreateAgent } from '@/query/mutations/agents.mutation'

// Define the form schema with zod
const agentCreateSchema = z.object({
  agent_name: z.string().min(1, { message: 'Agent name is required' }),
  template: z
    .number()
    .min(0, { message: 'Template is required' })
    .max(PLAYGROUND_AGENTS.length - 1, {
      message: 'Invalid template',
    }),
})

type AgentCreateFormValues = z.infer<typeof agentCreateSchema>
const defaultValues: AgentCreateFormValues = {
  agent_name: '',
  template: 0,
}
export const AgentCreateSheet = () => {
  const [isOpen, setIsOpen] = useState(false)
  const t = useTranslations('Organizations.agents')
  const params = useParams()
  const router = useRouter()
  const createAgent = useCreateAgent()
  const orgId = params.id as string

  const form = useForm<AgentCreateFormValues>({
    resolver: zodResolver(agentCreateSchema),
    defaultValues,
  })

  const handleCreateAgent = async (values: AgentCreateFormValues) => {
    try {
      const templateConfig = PLAYGROUND_AGENTS[values.template!].config
      const agent = await createAgent.mutateAsync({
        organization_id: orgId,
        agentData: {
          ...templateConfig,
          agent_name: values.agent_name,
        },
      })

      setIsOpen(false)
      form.reset()

      // Redirect to the agent edit page
      router.push(`/organizations/${orgId}/agents/${agent.agent_id}`)
      toast.success('Agent created successfully')
    } catch (error) {
      console.error('Failed to create agent:', error)
    }
  }

  const onOpenChange = (value: boolean) => {
    setIsOpen(value)
    form.reset(defaultValues)
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button variant="outline">
          <Plus className="mr-2 h-4 w-4" />
          {t('createAgent')}
        </Button>
      </SheetTrigger>
      <SheetContent className="flex w-full flex-col gap-6 px-0 sm:!max-w-xl">
        <SheetHeader className="px-6">
          <SheetTitle>{t('create_an_ai_agent')}</SheetTitle>
        </SheetHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleCreateAgent)}
            className="flex flex-1 flex-col gap-6">
            <div className="flex flex-1 flex-col gap-6 overflow-auto px-6">
              <FormField
                control={form.control}
                name="agent_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('ai_agent_name')}</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer support agent" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="template"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Template</FormLabel>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      {PLAYGROUND_AGENTS.map((agent, index) => {
                        const Icon = agent.icon
                        return (
                          <div
                            key={`agent-${index}`}
                            onClick={() => field.onChange(index)}
                            onKeyDown={e => {
                              if (e.key === 'Enter' || e.key === ' ') {
                                field.onChange(index)
                              }
                            }}
                            role="button"
                            tabIndex={0}
                            className={cn(
                              'cursor-pointer rounded-lg border p-4 transition-colors hover:bg-accent',
                              {
                                'border-primary bg-accent/50': field.value === index,
                              },
                            )}>
                            <div className="mb-2 flex items-center">
                              <Icon className="mr-2 h-5 w-5" />
                              <span className="font-medium">{agent.title}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{agent.description}</p>
                            <div className="mt-3 flex items-center">
                              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
                                <span className="text-xs">{agent.voice_name.charAt(0)}</span>
                              </div>
                              <span className="ml-2 text-sm">{agent.voice_name}</span>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <SheetFooter className="px-6">
              <Button disabled={form.formState.isSubmitting} type="submit" className="w-full">
                {form.formState.isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {t('create_agent')}
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  )
}
