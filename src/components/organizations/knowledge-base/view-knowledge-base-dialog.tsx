'use client'

import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { formatBytes } from '@/utils/format'

interface ViewKnowledgeBaseDialogProps {
  open: boolean
  onClose: () => void
  knowledgeBase: App.KnowledgeBase | null
}

export function ViewKnowledgeBaseDialog({
  open,
  onClose,
  knowledgeBase,
}: ViewKnowledgeBaseDialogProps) {
  const t = useTranslations('Organizations.knowledgeBase')

  return (
    <Dialog open={open} onOpenChange={isOpen => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{knowledgeBase?.name}</DialogTitle>
          <DialogDescription>
            {t('viewDescription', { type: knowledgeBase?.type ?? '' })}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[400px] overflow-y-auto">
          {knowledgeBase?.type === 'text' && (
            <div className="whitespace-pre-wrap rounded-md border p-4">{knowledgeBase.content}</div>
          )}
          {knowledgeBase?.type === 'url' && (
            <div className="space-y-2">
              <p className="font-medium">{t('sourceUrl')}:</p>
              <a
                href={knowledgeBase.url || '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="block truncate text-blue-500 hover:underline">
                {knowledgeBase.url}
              </a>
              {knowledgeBase.content && (
                <div className="mt-4">
                  <p className="font-medium">{t('extractedContent')}:</p>
                  <div
                    className="mt-2 max-h-[300px] overflow-y-auto rounded-md border p-4"
                    dangerouslySetInnerHTML={{ __html: knowledgeBase.content }}
                  />
                </div>
              )}
            </div>
          )}
          {knowledgeBase?.type === 'file' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <p className="font-medium">{t('fileDetails')}:</p>
                <div className="rounded-md border p-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm text-muted-foreground">{t('fileName')}:</div>
                    <div className="text-sm">{knowledgeBase.name || t('notAvailable')}</div>

                    <div className="text-sm text-muted-foreground">{t('fileSize')}:</div>
                    <div className="text-sm">
                      {knowledgeBase.metadata?.size_bytes
                        ? formatBytes(knowledgeBase.metadata.size_bytes as number)
                        : t('notAvailable')}
                    </div>
                  </div>
                </div>
              </div>

              {knowledgeBase.content && (
                <div>
                  <p className="font-medium">{t('extractedContent')}:</p>
                  <div
                    className="mt-2 max-h-[300px] overflow-y-auto whitespace-pre-wrap rounded-md border p-4"
                    dangerouslySetInnerHTML={{ __html: knowledgeBase.content }}
                  />
                </div>
              )}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button onClick={onClose}>{t('close')}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
