'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2, Upload, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useCreateKnowledgeBaseFile } from '@/query/mutations/knowledge-base.mutation'
import { formatBytes } from '@/utils/format'
import { logger } from '@/utils/logger'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'application/epub+zip',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/html',
]

const formSchema = z.object({
  file: z.instanceof(File, { message: 'File is required' }),
})

type CreateFileFormValues = z.infer<typeof formSchema>

interface CreateFileModalProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function CreateFileModal({ open, onClose, organizationId }: CreateFileModalProps) {
  const t = useTranslations('Organizations.knowledgeBase')
  const createKnowledgeBaseFile = useCreateKnowledgeBaseFile()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      file: undefined,
    },
  })

  const handleClose = () => {
    onClose()
    form.reset()
    setSelectedFile(null)
  }

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0]
      if (file) {
        if (file.size > MAX_FILE_SIZE) {
          toast.error(t('createFile.fileTooLarge', { size: formatBytes(MAX_FILE_SIZE) }))
          return
        }

        if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
          toast.error(t('createFile.invalidFileType'))
          return
        }

        setSelectedFile(file)
        form.setValue('file', file)
      }
    },
    [form, t],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/epub+zip': ['.epub'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/html': ['.html', '.htm'],
    },
    maxFiles: 1,
    multiple: false,
  })

  const removeFile = () => {
    setSelectedFile(null)
  }

  const onSubmit = async (values: CreateFileFormValues) => {
    try {
      await createKnowledgeBaseFile.mutateAsync({
        organization_id: organizationId,
        file: values.file,
      })
      toast.success(t('createFile.success'))
      handleClose()
    } catch (error) {
      logger.error('Failed to create knowledge base from file:', error)
      toast.error(t('createFile.error'))
    }
  }

  return (
    <Dialog open={open} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('createFile.title')}</DialogTitle>
          <DialogDescription>{t('createFile.description')}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="file"
              render={() => (
                <FormItem>
                  <FormLabel>{t('createFile.fileLabel')}</FormLabel>
                  <FormControl>
                    {!selectedFile ? (
                      <div
                        {...getRootProps()}
                        className={`flex cursor-pointer flex-col items-center justify-center rounded-md border border-dashed p-8 transition-colors ${
                          isDragActive
                            ? 'border-primary bg-primary/5'
                            : 'border-muted-foreground/25'
                        }`}>
                        <input {...getInputProps()} />
                        <Upload className="mb-2 h-8 w-8 text-muted-foreground" />
                        <p className="text-sm font-medium">{t('createFile.dragDrop')}</p>
                        <p className="mt-1 text-xs text-muted-foreground">
                          {t('createFile.fileTypes')}
                        </p>
                        <p className="mt-1 text-xs text-muted-foreground">
                          {t('createFile.maxSize', { size: formatBytes(MAX_FILE_SIZE) })}
                        </p>
                      </div>
                    ) : (
                      <div className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="flex flex-col">
                              <span className="text-sm font-medium">{selectedFile.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {formatBytes(selectedFile.size)}
                              </span>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={removeFile}
                            className="h-8 w-8 p-0">
                            <X className="h-4 w-4" />
                            <span className="sr-only">{t('createFile.remove')}</span>
                          </Button>
                        </div>
                      </div>
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={createKnowledgeBaseFile.isPending}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={createKnowledgeBaseFile.isPending}>
                {createKnowledgeBaseFile.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
