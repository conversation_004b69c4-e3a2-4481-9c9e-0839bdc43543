/* eslint-disable jsx-a11y/click-events-have-key-events */
'use client'

import { Check, Search } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useGetKnowledgeBases } from '@/query/queries/knowledge-base.query'

interface SelectKnowledgeBaseDialogProps {
  open: boolean
  onClose: () => void
  onSelect: (selectedKnowledgeBases: App.KnowledgeBase[]) => void
  organizationId: string
  currentSelectedIds: string[]
}

export function SelectKnowledgeBaseDialog({
  open,
  onClose,
  onSelect,
  organizationId,
  currentSelectedIds,
}: SelectKnowledgeBaseDialogProps) {
  const t = useTranslations('Organizations.knowledgeBase')
  const { data: knowledgeBases = [], isLoading } = useGetKnowledgeBases(organizationId, {
    enabled: open && !!organizationId,
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedIds, setSelectedIds] = useState<string[]>([])

  // Reset selection when dialog opens
  const handleDialogOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      setSelectedIds([])
      setSearchQuery('')
      onClose()
    }
  }

  // Filter knowledge bases based on search query
  const filteredKnowledgeBases = knowledgeBases.filter(kb => {
    if (!searchQuery) return true
    return kb.name.toLowerCase().includes(searchQuery.toLowerCase())
  })

  // Toggle selection of a knowledge base
  const toggleSelection = (kb: App.KnowledgeBase) => {
    setSelectedIds(prev =>
      prev.includes(kb.id) ? prev.filter(id => id !== kb.id) : [...prev, kb.id],
    )
  }

  // Handle confirmation
  const handleConfirm = () => {
    const selectedKnowledgeBases = knowledgeBases.filter(kb => selectedIds.includes(kb.id))
    onSelect(selectedKnowledgeBases)
    onClose()
    setSelectedIds([])
  }

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('selectKnowledgeBase.title')}</DialogTitle>
          <DialogDescription>{t('selectKnowledgeBase.description')}</DialogDescription>
        </DialogHeader>

        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('selectKnowledgeBase.searchPlaceholder')}
            className="pl-8"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
        </div>

        <ScrollArea className="h-[300px] rounded-md border">
          {isLoading ? (
            <div className="flex h-full items-center justify-center">
              <p className="text-sm text-muted-foreground">{t('loading')}</p>
            </div>
          ) : filteredKnowledgeBases.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <p className="text-sm text-muted-foreground">{t('selectKnowledgeBase.noResults')}</p>
            </div>
          ) : (
            <div className="p-4">
              {filteredKnowledgeBases.map(kb => {
                const isSelected = selectedIds.includes(kb.id)
                const isAlreadyAdded = currentSelectedIds.includes(kb.id)

                return (
                  <div
                    key={kb.id}
                    onClick={() => !isAlreadyAdded && toggleSelection(kb)}
                    role="button"
                    tabIndex={0}
                    className={cn(
                      'mb-2 flex cursor-pointer items-center justify-between rounded-md border p-3 transition-colors',
                      {
                        'border-primary bg-primary/5': isSelected,
                        'opacity-50': isAlreadyAdded,
                      },
                    )}>
                    <div className="flex flex-1 items-center space-x-3">
                      <Badge variant="outline" className="capitalize">
                        {kb.type}
                      </Badge>
                      <div className="flex-1">
                        <p className="font-medium">{kb.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(kb.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    {isAlreadyAdded ? (
                      <Badge variant="outline">{t('selectKnowledgeBase.alreadyAdded')}</Badge>
                    ) : isSelected ? (
                      <Check className="h-5 w-5 text-primary" />
                    ) : null}
                  </div>
                )
              })}
            </div>
          )}
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t('cancel')}
          </Button>
          <Button onClick={handleConfirm} disabled={selectedIds.length === 0}>
            {t('selectKnowledgeBase.addSelected', { count: selectedIds.length })}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
