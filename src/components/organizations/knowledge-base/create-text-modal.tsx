'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useCreateKnowledgeBaseText } from '@/query/mutations/knowledge-base.mutation'
import { logger } from '@/utils/logger'

const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
})

type CreateTextFormValues = z.infer<typeof formSchema>

interface CreateTextModalProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function CreateTextModal({ open, onClose, organizationId }: CreateTextModalProps) {
  const t = useTranslations('Organizations.knowledgeBase')
  const createKnowledgeBaseText = useCreateKnowledgeBaseText()

  const form = useForm<CreateTextFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      content: '',
    },
  })

  const handleClose = () => {
    onClose()
    form.reset()
  }

  const onSubmit = async (values: CreateTextFormValues) => {
    try {
      await createKnowledgeBaseText.mutateAsync({
        organization_id: organizationId,
        name: values.name,
        content: values.content,
      })
      toast.success(t('createText.success'))
      handleClose()
    } catch (error) {
      logger.error('Failed to create knowledge base:', error)
      toast.error(t('createText.error'))
    }
  }

  return (
    <Dialog open={open} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('createText.title')}</DialogTitle>
          <DialogDescription>{t('createText.description')}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('createText.nameLabel')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('createText.namePlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('createText.contentLabel')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('createText.contentPlaceholder')}
                      className="min-h-[200px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={createKnowledgeBaseText.isPending}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={createKnowledgeBaseText.isPending}>
                {createKnowledgeBaseText.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
