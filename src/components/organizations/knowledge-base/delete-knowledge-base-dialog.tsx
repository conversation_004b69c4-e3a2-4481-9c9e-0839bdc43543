'use client'

import { ExternalLink } from 'lucide-react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useDeleteKnowledgeBase } from '@/query/mutations/knowledge-base.mutation'

interface DeleteKnowledgeBaseDialogProps {
  open: boolean
  onClose: () => void
  knowledgeBase: App.KnowledgeBase | null
  organizationId: string
}

export function DeleteKnowledgeBaseDialog({
  open,
  onClose,
  knowledgeBase,
  organizationId,
}: DeleteKnowledgeBaseDialogProps) {
  const t = useTranslations('Organizations.knowledgeBase')
  const deleteKnowledgeBase = useDeleteKnowledgeBase(organizationId)
  const linkedAgents = knowledgeBase?.agents || []
  const hasLinkedAgents = !!linkedAgents.length
  const confirmDelete = async () => {
    if (!knowledgeBase) return

    try {
      await deleteKnowledgeBase.mutateAsync(knowledgeBase.id)
      toast.success(t('deleteSuccess'))
      onClose()
    } catch (error) {
      toast.error(t('deleteError'))
      console.error('Failed to delete knowledge base:', error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('deleteTitle')}</DialogTitle>
          <DialogDescription>{t('deleteDescription')}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <p>{t('deleteConfirm', { name: knowledgeBase?.name ?? '' })}</p>

          {hasLinkedAgents && (
            <div className="rounded-md border border-destructive/20 bg-destructive/5 p-4">
              <h4 className="mb-2 font-medium text-destructive">
                {t('linkedAgentsWarning', { count: linkedAgents.length })}
              </h4>
              <ul className="space-y-2">
                {linkedAgents.map(agent => (
                  <li key={agent.agent_id} className="flex items-center justify-between text-sm">
                    <span>{agent.agent_name}</span>
                    <Link
                      href={`/organizations/${organizationId}/agents/${agent.agent_id}`}
                      className="flex items-center text-xs text-blue-500 hover:underline"
                      target="_blank">
                      {t('viewAgent')}
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t('cancel')}
          </Button>
          {!hasLinkedAgents && (
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteKnowledgeBase.isPending}>
              {deleteKnowledgeBase.isPending ? t('deleting') : t('delete')}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
