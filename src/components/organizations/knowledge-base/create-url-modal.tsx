'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useCreateKnowledgeBaseUrl } from '@/query/mutations/knowledge-base.mutation'
import { logger } from '@/utils/logger'

const formSchema = z.object({
  url: z.string().url('Please enter a valid URL'),
})

type CreateUrlFormValues = z.infer<typeof formSchema>

interface CreateUrlModalProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function CreateUrlModal({ open, onClose, organizationId }: CreateUrlModalProps) {
  const t = useTranslations('Organizations.knowledgeBase')
  const createKnowledgeBaseUrl = useCreateKnowledgeBaseUrl()

  const form = useForm<CreateUrlFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      url: '',
    },
  })
  const handleClose = () => {
    onClose()
    form.reset()
  }

  const onSubmit = async (values: CreateUrlFormValues) => {
    try {
      await createKnowledgeBaseUrl.mutateAsync({
        organization_id: organizationId,
        url: values.url,
      })
      toast.success(t('createUrl.success'))
      handleClose()
    } catch (error) {
      logger.error('Failed to create knowledge base from URL:', error)
      toast.error(t('createUrl.error'))
    }
  }

  return (
    <Dialog open={open} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('createUrl.title')}</DialogTitle>
          <DialogDescription>{t('createUrl.description')}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('createUrl.urlLabel')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('createUrl.urlPlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={createKnowledgeBaseUrl.isPending}>
                {t('cancel')}
              </Button>
              <Button type="submit" disabled={createKnowledgeBaseUrl.isPending}>
                {createKnowledgeBaseUrl.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('create')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
