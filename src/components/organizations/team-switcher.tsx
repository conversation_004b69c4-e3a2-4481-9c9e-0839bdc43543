'use client'

import { ChevronsUpDown, Plus } from 'lucide-react'
import { useTranslations } from 'next-intl'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { Skeleton } from '@/components/ui/skeleton'
import { useRouter } from '@/i18n/navigation'
import { cn } from '@/lib/utils'
import { useOrganizations } from '@/providers/organizations-provider'

export function TeamSwitcher() {
  const t = useTranslations('Organizations')
  const router = useRouter()
  const { isMobile } = useSidebar()
  const { organizations, currentOrganization, isLoading } = useOrganizations()

  if (isLoading) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <div className="flex items-center gap-3 px-2 py-4">
            <Skeleton className="h-8 w-8 rounded-lg" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-3 w-[60px]" />
            </div>
          </div>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  if (!currentOrganization) {
    return null
  }

  const handleOrganizationSwitch = (org: App.Organization) => {
    router.push(`/organizations/${org.id}`)
  }

  const handleCreateOrganization = () => {
    router.push('/organizations/create')
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
              <div
                className={cn(
                  'flex aspect-square size-8 items-center justify-center rounded-lg',
                  'bg-sidebar-primary text-sidebar-primary-foreground',
                )}>
                {/* You can replace this with an actual logo/avatar component */}
                <span className="text-lg font-semibold">
                  {currentOrganization.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{currentOrganization.name}</span>
                <span className="truncate text-xs">{t(`types.${currentOrganization.type}`)}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}>
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              {t('switchOrganization')}
            </DropdownMenuLabel>
            {organizations.map((org, index) => (
              <DropdownMenuItem
                key={org.id}
                onClick={() => handleOrganizationSwitch(org)}
                className="gap-2 p-2">
                <div
                  className={cn(
                    'flex size-6 items-center justify-center rounded-lg border',
                    currentOrganization.id === org.id && 'border-primary bg-primary/10',
                  )}>
                  <span className="text-sm font-medium">{org.name.charAt(0).toUpperCase()}</span>
                </div>
                <span className="flex-1 truncate">{org.name}</span>
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleCreateOrganization} className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                <Plus className="size-4" />
              </div>
              <span className="font-medium text-muted-foreground">{t('createNew')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
