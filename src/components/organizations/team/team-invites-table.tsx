'use client'

import { Clock, MoreHorizontal, X } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useCancelOrganizationInvite } from '@/query/mutations/organizations.mutation'
import { formatDate } from '@/utils/time'

interface TeamInvitesTableProps {
  invites: App.OrganizationInvite[]
  isLoading: boolean
}

export function TeamInvitesTable({ invites, isLoading }: TeamInvitesTableProps) {
  const t = useTranslations('Settings.team')
  const { mutate: cancelInvite } = useCancelOrganizationInvite()

  if (isLoading) {
    return <TeamInvitesTableSkeleton />
  }

  if (invites.length === 0) {
    return null
  }

  const handleCancelInvite = (inviteId: string) => {
    cancelInvite({ inviteId })
  }

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium">{t('pendingInvites')}</h4>
        <p className="text-sm text-muted-foreground">{t('pendingInvitesDescription')}</p>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.email')}</TableHead>
            <TableHead>{t('table.role')}</TableHead>
            <TableHead>{t('table.status')}</TableHead>
            <TableHead>{t('table.expires')}</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invites.map(invite => (
            <TableRow key={invite.id}>
              <TableCell>{invite.email}</TableCell>
              <TableCell>
                <Badge variant="secondary">{t(`roles.${invite.role}`)}</Badge>
              </TableCell>
              <TableCell>
                <Badge variant="outline">
                  <Clock className="mr-1 h-3 w-3" />
                  {t(`status.pending`)}
                </Badge>
              </TableCell>
              <TableCell>{formatDate(invite.expires_at)}</TableCell>
              <TableCell>
                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">{t('table.actions.open')}</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onSelect={() => handleCancelInvite(invite.id)}>
                      <X className="mr-2 h-4 w-4" />
                      {t('table.actions.cancelInvite')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

function TeamInvitesTableSkeleton() {
  return (
    <div className="space-y-3">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  )
}
