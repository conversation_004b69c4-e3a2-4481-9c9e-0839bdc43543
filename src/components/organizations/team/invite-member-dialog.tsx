'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useInviteOrganizationMember } from '@/query/mutations/organizations.mutation'

const inviteMemberSchema = z.object({
  email: z.string().email(),
  role: z.enum(['member', 'admin']),
})

type InviteMemberFormData = z.infer<typeof inviteMemberSchema>

interface InviteMemberDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function InviteMemberDialog({ open, onClose, organizationId }: InviteMemberDialogProps) {
  const t = useTranslations('Settings.team')
  const { mutate: inviteMember, isPending } = useInviteOrganizationMember()
  const { currentUserRole } = useOrganizationPermissions()

  const form = useForm<InviteMemberFormData>({
    resolver: zodResolver(inviteMemberSchema),
    defaultValues: {
      email: '',
      role: 'member',
    },
  })

  const onSubmit = (data: InviteMemberFormData) => {
    // Prevent admins from creating other admins
    if (currentUserRole === 'admin' && data.role === 'admin') {
      data.role = 'member'
    }

    inviteMember(
      { organizationId, ...data },
      {
        onSuccess: () => {
          onClose()
          form.reset()
        },
      },
    )
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('invite.title')}</DialogTitle>
          <DialogDescription>{t('invite.description')}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('invite.fields.email')}</FormLabel>
                  <FormControl>
                    <Input {...field} type="email" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('invite.fields.role')}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="member">{t('roles.member')}</SelectItem>
                      {/* Only owners can create admins */}
                      {currentUserRole === 'owner' && (
                        <SelectItem value="admin">{t('roles.admin')}</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button variant="outline" onClick={onClose}>
                {t('invite.cancel')}
              </Button>
              <Button type="submit">
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('invite.submit')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
