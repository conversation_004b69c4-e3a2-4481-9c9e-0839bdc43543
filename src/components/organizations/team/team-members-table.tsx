'use client'

import { MoreHorizontal, Shield, UserX } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import {
  useRemoveOrganizationMember,
  useUpdateOrganizationMember,
} from '@/query/mutations/organizations.mutation'
import { useGetUser } from '@/stores/root.store'
import { formatDate } from '@/utils/time'

interface TeamMembersTableProps {
  members: App.OrganizationMember[]
  isLoading: boolean
}

export function TeamMembersTable({ members, isLoading }: TeamMembersTableProps) {
  const t = useTranslations('Settings.team')
  const { mutate: removeMember } = useRemoveOrganizationMember()
  const { mutate: updateMember } = useUpdateOrganizationMember()
  const { canManageTeam, currentUserRole } = useOrganizationPermissions()
  const currentUser = useGetUser()

  if (isLoading) {
    return <TeamMembersTableSkeleton />
  }

  const handleRemoveMember = (memberId: string, memberRole: App.OrganizationRole) => {
    // Prevent removing owners
    if (memberRole === 'owner') return
    // Prevent members from removing anyone
    if (!canManageTeam) return
    // Prevent admins from removing other admins or owners
    if (currentUserRole === 'admin' && ['admin', 'owner'].includes(memberRole)) return
    // Prevent removing yourself
    if (currentUser?.id === memberId) return

    removeMember({ memberId })
  }

  const handleRoleChange = (
    memberId: string,
    currentRole: App.OrganizationRole,
    newRole: App.OrganizationRole,
  ) => {
    // Prevent changing owner's role
    if (currentRole === 'owner') return
    // Prevent members from changing roles
    if (!canManageTeam) return
    // Prevent admins from changing other admins' roles
    if (currentUserRole === 'admin' && currentRole === 'admin') return
    // Prevent setting someone as owner
    if (newRole === 'owner') return

    updateMember({ memberId, data: { role: newRole } })
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.member')}</TableHead>
            <TableHead>{t('table.role')}</TableHead>
            <TableHead>{t('table.status')}</TableHead>
            <TableHead>{t('table.joinedAt')}</TableHead>
            <TableHead className="w-[50px]" />
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map(member => (
            <TableRow key={member.id}>
              <TableCell className="flex items-center gap-2">
                <div className="flex size-8 items-center justify-center rounded-full bg-muted">
                  {member.user.name?.[0] || member?.user?.email?.[0] || ''}
                </div>
                <div>
                  <div className="font-medium">{member.user.name || member?.user?.email || ''}</div>
                  {member.user.name && (
                    <div className="text-sm text-muted-foreground">{member?.user?.email ?? ''}</div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={member.role === 'owner' ? 'default' : 'secondary'}>
                  {t(`roles.${member.role}`)}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                  {t(`status.${member.status}`)}
                </Badge>
              </TableCell>
              <TableCell>{formatDate(member.created_at)}</TableCell>
              <TableCell>
                {canManageTeam && member.role !== 'owner' && currentUser?.id !== member.user.id && (
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">{t('table.actions.open')}</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {/* Only show role change options if current user can manage this role */}
                      {(currentUserRole === 'owner' ||
                        (currentUserRole === 'admin' && member.role === 'member')) && (
                        <>
                          <DropdownMenuItem
                            onSelect={() => handleRoleChange(member.id, member.role, 'admin')}
                            disabled={member.role === 'admin'}>
                            <Shield className="mr-2 h-4 w-4" />
                            {t('table.actions.makeAdmin')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() => handleRoleChange(member.id, member.role, 'member')}
                            disabled={member.role === 'member'}>
                            <Shield className="mr-2 h-4 w-4" />
                            {t('table.actions.makeMember')}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                        </>
                      )}
                      <DropdownMenuItem
                        onSelect={() => handleRemoveMember(member.id, member.role)}
                        className="text-destructive focus:text-destructive">
                        <UserX className="mr-2 h-4 w-4" />
                        {t('table.actions.remove')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

function TeamMembersTableSkeleton() {
  return (
    <div className="space-y-3">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  )
}
