import { <PERSON>, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useDeleteContact } from '@/query/mutations/contacts.mutation'

interface ContactsTableProps {
  contacts: App.CachedCRMContact[]
  isLoading: boolean
  organizationId: string
  onEdit: (contact: App.CachedCRMContact) => void
}

export function ContactsTable({ contacts, isLoading, onEdit, organizationId }: ContactsTableProps) {
  const t = useTranslations('Organizations.contacts')
  const deleteContact = useDeleteContact(organizationId)
  const { canManageContacts } = useOrganizationPermissions()
  const [contactToDelete, setContactToDelete] = useState<App.CachedCRMContact | null>(null)
  const [selectedLoadingContact, setSelectedLoadingContact] = useState<Record<string, boolean>>({})

  if (isLoading) {
    return <div>Loading...</div>
  }

  const handleDeleteContact = async () => {
    if (!contactToDelete) return
    try {
      setSelectedLoadingContact({ [contactToDelete.id]: true })
      await deleteContact.mutateAsync({
        connectionId: contactToDelete.crm_connection_id,
        contactId: contactToDelete.crm_entity_id,
      })
    } catch {
    } finally {
      setSelectedLoadingContact({ [contactToDelete.id]: false })
      setContactToDelete(null)
    }
  }

  const renderActions = (contact: App.CachedCRMContact) => {
    if (!canManageContacts) return null
    if (selectedLoadingContact[contact.id]) return <Loader2 className="h-4 w-4 animate-spin" />
    return (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('table.actions.open')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={() => onEdit(contact)}>
            <Edit className="mr-2 h-4 w-4" />
            {t('table.actions.edit')}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onSelect={() => setContactToDelete(contact)}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t('table.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('table.name')}</TableHead>
            <TableHead>{t('table.jobTitle')}</TableHead>
            <TableHead>{t('table.email')}</TableHead>
            <TableHead>{t('table.phone')}</TableHead>
            <TableHead className="w-[100px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {contacts.map(contact => (
            <TableRow key={contact.id}>
              <TableCell>
                {contact.first_name} {contact.last_name}
              </TableCell>
              <TableCell>{contact.job_title}</TableCell>
              <TableCell>{contact.email}</TableCell>
              <TableCell>{contact.phone}</TableCell>
              <TableCell className="flex items-center justify-end">
                {renderActions(contact)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={!!contactToDelete} onOpenChange={() => setContactToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>{t('deleteDialog.description')}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('deleteDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteContact}
              disabled={deleteContact.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteContact.isPending ? t('deleteDialog.deleting') : t('deleteDialog.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
