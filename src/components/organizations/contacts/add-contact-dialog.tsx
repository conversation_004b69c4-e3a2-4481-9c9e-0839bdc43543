import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import * as z from 'zod'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import { useCreateContact } from '@/query/mutations/contacts.mutation'
import { useGetOrganizationCRMConnections } from '@/query/queries/crm.query'

const formSchema = z.object({
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  email: z.string().email(),
  phone: z
    .string()
    .optional()
    .refine(val => !val || isValidPhoneNumber(val), {
      message: 'Invalid phone number',
    }),
  job_title: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface AddContactDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
}

export function AddContactDialog({ open, onClose, organizationId }: AddContactDialogProps) {
  const t = useTranslations('Organizations.contacts')
  const createContact = useCreateContact(organizationId)
  const { data: connections = [] } = useGetOrganizationCRMConnections(organizationId)

  const hubspotConnection = connections.find(c => c.crm_type === 'hubspot')

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
    },
  })

  const onSubmit = async (values: FormValues) => {
    if (!hubspotConnection) return

    await createContact.mutateAsync({
      connectionId: hubspotConnection.id,
      contactData: values,
    })
    onClose()
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('addDialog.title')}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="first_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('addDialog.firstName')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('addDialog.lastName')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('addDialog.email')}</FormLabel>
                  <FormControl>
                    <Input {...field} type="email" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('addDialog.phone')}</FormLabel>
                  <FormControl>
                    <PhoneInput
                      defaultCountry="US"
                      placeholder={t('phonePlaceholder')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="job_title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('addDialog.jobTitle')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={onClose}>
                {t('addDialog.cancel')}
              </Button>
              <Button type="submit" disabled={createContact.isPending}>
                {createContact.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                {t('addDialog.submit')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
