'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { isValidPhoneNumber } from 'react-phone-number-input'
import timezones from 'timezones-list'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PhoneInput } from '@/components/ui/phone-input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useOrganizations } from '@/providers/organizations-provider'
import { useUpdateCompany } from '@/query/mutations/companies.mutation'
import { logger } from '@/utils/logger'

const companySchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  domain: z
    .string()
    .min(1, { message: 'Domain is required.' })
    .regex(/^[a-zA-Z0-9][a-zA-Z0-9-_.]*\.[a-zA-Z]{2,}$/, {
      message: 'Please enter a valid domain (e.g., google.com).',
    }),
  industry: z.string().optional().nullable(),
  type: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  linkedin_url: z.string().url().optional().nullable().or(z.literal('')),
  annual_revenue: z.number().optional().nullable(),
  employee_count: z.number().optional().nullable(),
  phone: z
    .string()
    .optional()
    .refine(
      value => {
        if (!value) return true
        return isValidPhoneNumber(value)
      },
      {
        message: 'Please enter a valid phone number',
      },
    ),
  street_address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  postal_code: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  timezone: z.string().optional().nullable(),
})

type CompanyFormValues = z.infer<typeof companySchema>

interface EditCompanyDialogProps {
  open: boolean
  onClose: () => void
  organizationId: string
  company: App.CachedCRMCompany
}

export function EditCompanyDialog({
  open,
  onClose,
  organizationId,
  company,
}: EditCompanyDialogProps) {
  const t = useTranslations('Organizations.companies')
  const updateCompany = useUpdateCompany(organizationId)
  const { industries } = useOrganizations()

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: company.name,
      domain: company.domain || '',
      industry: company.industry || '',
      type: company.type || '',
      description: company.description || '',
      linkedin_url: company.linkedin_url || '',
      annual_revenue: company.annual_revenue,
      employee_count: company.employee_count,
      phone: company.phone || '',
      street_address: company.street_address || '',
      city: company.city || '',
      state: company.state || '',
      postal_code: company.postal_code || '',
      country: company.country || '',
      timezone: company.timezone || '',
    },
  })

  const onSubmit = async (data: CompanyFormValues) => {
    try {
      await updateCompany.mutateAsync({
        connectionId: company.crm_connection_id,
        companyId: company.crm_entity_id,
        companyData: {
          name: data.name,
          domain: data.domain || '',
          industry: data.industry || '',
          type: data.type || '',
          description: data.description || '',
          linkedin_url: data.linkedin_url || '',
          annual_revenue: data.annual_revenue || null,
          employee_count: data.employee_count || null,
          phone: data.phone || '',
          street_address: data.street_address || '',
          city: data.city || '',
          state: data.state || '',
          postal_code: data.postal_code || '',
          country: data.country || '',
          timezone: data.timezone || '',
        },
      })
      onClose()
    } catch (error) {
      logger.error(error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="px-4">
          <DialogTitle>{t('editCompany')}</DialogTitle>
          <DialogDescription>{t('editCompanyDescription')}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <div className="flex max-h-[70vh] flex-col gap-4 overflow-y-auto px-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.name')}</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="domain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.domain')}</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.industry')}</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value ?? ''}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('form.selectIndustry')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {industries?.map(industry => (
                          <SelectItem key={industry.id} value={industry.slug}>
                            {industry.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.type')}</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.description')}</FormLabel>
                    <FormControl>
                      <Textarea {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="linkedin_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.linkedin_url')}</FormLabel>
                    <FormControl>
                      <Input {...field} type="url" value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="annual_revenue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.annual_revenue')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value ?? undefined} type="number" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="employee_count"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.employee_count')}</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" value={field.value ?? undefined} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.phone')}</FormLabel>
                    <FormControl>
                      <PhoneInput
                        defaultCountry="US"
                        placeholder={t('form.phonePlaceholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="street_address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.street_address')}</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.city')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.state')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="postal_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.postal_code')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.country')}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value ?? ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="timezone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.timezone')}</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value ?? ''}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('form.selectTimezone')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {timezones.map(tz => (
                          <SelectItem key={tz.tzCode} value={tz.tzCode}>
                            {tz.tzCode}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter className="px-4">
              <Button type="submit" disabled={updateCompany.isPending}>
                {updateCompany.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                {t('save')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
