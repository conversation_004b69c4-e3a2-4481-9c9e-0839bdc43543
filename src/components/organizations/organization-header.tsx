'use client'

import { useTranslations } from 'next-intl'

import { LanguageSwitcher } from '@/components/language-switcher'
import { ThemeSwitcher } from '@/components/theme-switcher'
import { Button } from '@/components/ui/button'
import { UserMenu } from '@/components/user-menu'
import { Link } from '@/i18n/navigation'
import { useIsAuthenticated } from '@/stores/root.store'

export function OrganizationHeader() {
  const t = useTranslations('Header')
  const isAuthenticated = useIsAuthenticated()

  return (
    <div className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <h1 className="text-lg font-semibold">{t('organizations')}</h1>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <nav className="flex items-center space-x-2">
            <ThemeSwitcher />
            <LanguageSwitcher />
            {isAuthenticated ? (
              <UserMenu />
            ) : (
              <Button asChild variant="default" size="sm">
                <Link href="/login">{t('login')}</Link>
              </Button>
            )}
          </nav>
        </div>
      </div>
    </div>
  )
}
