'use client'

import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { LanguageSwitcher } from '@/components/language-switcher'
import { ThemeSwitcher } from '@/components/theme-switcher'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { UserMenu } from '@/components/user-menu'
import { Link } from '@/i18n/navigation'
import { useIsAuthenticated } from '@/stores/root.store'

export function Header() {
  const isAuthenticated = useIsAuthenticated()
  const t = useTranslations('Header')
  const pathname = usePathname()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <span className="hidden font-bold sm:inline-block">{t('title')}</span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            <Link
              href="/blog"
              className={`transition-colors hover:text-foreground/80 ${
                pathname === '/blog' ? 'text-foreground' : 'text-foreground/60'
              }`}>
              {t('blog')}
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger className="text-foreground/60 transition-colors hover:text-foreground/80">
                Legal
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem asChild>
                  <Link href="/privacy">{t('legal.privacy')}</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/terms">{t('legal.terms')}</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <nav className="flex items-center space-x-2">
            <ThemeSwitcher />
            <LanguageSwitcher />
            {isAuthenticated ? (
              <UserMenu />
            ) : (
              <Button asChild variant="default" size="sm">
                <Link href="/login">{t('login')}</Link>
              </Button>
            )}
          </nav>
        </div>
      </div>
    </header>
  )
}
