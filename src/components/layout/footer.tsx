'use client'

import { useTranslations } from 'next-intl'

import { Separator } from '@/components/ui/separator'
import { Link } from '@/i18n/navigation'

export function Footer() {
  const t = useTranslations('Footer')

  return (
    <footer className="w-full border-t">
      <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            {t('copyright')} © {new Date().getFullYear()}{' '}
            <Link href="/" className="font-medium underline underline-offset-4">
              Foundation
            </Link>
            . {t('rights')}
          </p>
        </div>
        <nav className="flex gap-4">
          <Link
            href="https://twitter.com/foundation"
            target="_blank"
            rel="noreferrer"
            className="text-sm text-muted-foreground underline-offset-4 hover:underline">
            {t('links.twitter')}
          </Link>
          <Link
            href="https://github.com/foundation"
            target="_blank"
            rel="noreferrer"
            className="text-sm text-muted-foreground underline-offset-4 hover:underline">
            {t('links.github')}
          </Link>
          <Separator orientation="vertical" className="my-auto h-4" />
          <Link
            href="/privacy"
            className="text-sm text-muted-foreground underline-offset-4 hover:underline">
            {t('links.privacy')}
          </Link>
          <Link
            href="/terms"
            className="text-sm text-muted-foreground underline-offset-4 hover:underline">
            {t('links.terms')}
          </Link>
        </nav>
      </div>
    </footer>
  )
}
