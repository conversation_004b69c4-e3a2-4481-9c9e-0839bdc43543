'use client'

import { useParams } from 'next/navigation'
import { createContext, useContext, useEffect, useMemo } from 'react'

import { useGetIndustries, useGetOrganizationCRMConnections } from '@/query/queries/crm.query'
import {
  useGetMemberRole,
  useGetOrganization,
  useGetUserOrganizations,
} from '@/query/queries/organizations.query'
import type { OrganizationWithMemberInfo } from '@/services/organization.service'
import { useGetUser, useSetCurrentOrganizationId } from '@/stores/root.store'

interface OrganizationsContextType {
  organizations: OrganizationWithMemberInfo[]
  currentOrganization?: OrganizationWithMemberInfo | undefined
  currentUserRole?: App.OrganizationRole | null
  isLoading: boolean
  industries?: App.CompanyIndustry[]
  connections: App.CRMConnection[]
  isLoadingConnections: boolean
}

const OrganizationsContext = createContext<OrganizationsContextType | undefined>(undefined)

export function OrganizationsProvider({ children }: { children: React.ReactNode }) {
  const params = useParams()
  const user = useGetUser()
  const setCurrentOrganizationId = useSetCurrentOrganizationId()
  const organizationId = params.id as string

  useEffect(() => {
    setCurrentOrganizationId(organizationId)
  }, [organizationId, setCurrentOrganizationId])

  const { data: organizations = [], isLoading: isLoadingOrgs } = useGetUserOrganizations()
  const { data: currentOrganization, isLoading: isLoadingOrg } = useGetOrganization(organizationId)
  const { data: currentUserRole, isLoading: isLoadingRole } = useGetMemberRole(
    organizationId,
    user?.id,
  )
  const { data: connections = [], isLoading: isLoadingConnections } =
    useGetOrganizationCRMConnections(organizationId!)

  const { data: industries = [] } = useGetIndustries()

  const value = useMemo(
    () => ({
      organizations,
      currentOrganization,
      currentUserRole,
      isLoading: isLoadingOrgs || isLoadingOrg || isLoadingRole,
      industries,
      connections,
      isLoadingConnections,
    }),
    [
      organizations,
      currentOrganization,
      currentUserRole,
      isLoadingOrgs,
      isLoadingOrg,
      isLoadingRole,
      industries,
      connections,
      isLoadingConnections,
    ],
  )

  return <OrganizationsContext.Provider value={value}>{children}</OrganizationsContext.Provider>
}

export function useOrganizations() {
  const context = useContext(OrganizationsContext)
  if (context === undefined) {
    throw new Error('useOrganizations must be used within an OrganizationsProvider')
  }
  return context
}
