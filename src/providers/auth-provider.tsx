'use client'

import { createContext, useContext, useEffect } from 'react'

import { useRouter } from '@/i18n/navigation'
import queryClient from '@/query/query-client'
import { auth } from '@/services/auth.service'
import type { RootStore } from '@/stores/root.store'
import { useStore } from '@/stores/root.store'
import { logger } from '@/utils/logger'
import { createClient } from '@/utils/supabase/client'

const supabase = createClient()

type AuthContextType = {
  syncProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const store = useStore<RootStore>(state => state)

  const syncProfile = async () => {
    try {
      store.setLoading(true)
      store.setError(null)

      const {
        data: { user },
        error: sessionError,
      } = await supabase.auth.getUser()

      if (sessionError) {
        throw sessionError
      }

      store.setUser(user)

      if (!user) {
        store.setProfile(null)
        return
      }

      const profile = await auth.getProfile(user.id)

      store.setProfile(profile)
    } catch (error) {
      logger.error('Error syncing profile:', error)
      store.setError(error as Error)
    } finally {
      store.setLoading(false)
    }
  }

  useEffect(() => {
    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setTimeout(() => {
        if (event === 'SIGNED_OUT') {
          queryClient.clear()
          store.reset()

          router.push('/login')
        } else if (
          event === 'SIGNED_IN' ||
          event === 'USER_UPDATED' ||
          (event === 'INITIAL_SESSION' && session?.user)
        ) {
          syncProfile()
        }
      }, 100)
    })

    return () => {
      subscription.unsubscribe()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return <AuthContext.Provider value={{ syncProfile }}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
