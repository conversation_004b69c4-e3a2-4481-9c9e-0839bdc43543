export const ORGANIZATION_TYPES: App.OrganizationType[] = [
  'personal',
  'education',
  'startup',
  'agency',
  'company',
]

export const COMPANY_SIZES: App.CompanySize[] = ['1-10', '10-49', '50-99', '100-299', '300+']

export const ORGANIZATION_TYPES_WITH_COMPANY_SIZE: App.OrganizationType[] = [
  'startup',
  'agency',
  'company',
]

export const hasCompanySize = (type: App.OrganizationType): boolean => {
  return ORGANIZATION_TYPES_WITH_COMPANY_SIZE.includes(type)
}
