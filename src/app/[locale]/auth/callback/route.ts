import { NextResponse } from 'next/server'

import { authLogger } from '@/utils/logger'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: Request) {
  // The `/auth/callback` route is required for the server-side auth flow implemented
  // by the SSR package. It exchanges an auth code for the user's session.
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const origin = requestUrl.origin
  const redirectTo = requestUrl.searchParams.get('redirect_to')?.toString()

  if (code) {
    try {
      const supabase = await createClient()
      await supabase.auth.exchangeCodeForSession(code)
      authLogger.info({ redirectTo }, 'Successfully exchanged code for session')
    } catch (error) {
      authLogger.error({ error, code }, 'Failed to exchange code for session')
      // You might want to redirect to an error page here
      return NextResponse.redirect(`${origin}/auth/error`)
    }
  }

  if (redirectTo) {
    authLogger.debug({ redirectTo }, 'Redirecting to specified path')
    return NextResponse.redirect(`${origin}${redirectTo}`)
  }

  authLogger.debug('Redirecting to protected route')
  return NextResponse.redirect(`${origin}/organizations`)
}
