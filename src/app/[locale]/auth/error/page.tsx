'use client'

import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import { Link } from '@/i18n/navigation'

export default function AuthErrorPage() {
  const t = useTranslations('auth')
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  // Map common error messages to translation keys
  const getErrorMessage = (error: string | null) => {
    if (!error) return t('error.default')

    const errorLowerCase = error.toLowerCase()

    if (errorLowerCase.includes('email not confirmed')) {
      return t('error.emailNotConfirmed')
    }
    if (errorLowerCase.includes('invalid credentials')) {
      return t('error.invalidCredentials')
    }
    if (errorLowerCase.includes('email already exists')) {
      return t('error.emailExists')
    }
    if (errorLowerCase.includes('invalid token')) {
      return t('error.invalidToken')
    }
    if (errorLowerCase.includes('expired')) {
      return t('error.expired')
    }

    return error
  }

  return (
    <AuthLayout title={t('error.title')} description={t('error.description')}>
      <div className="flex flex-col items-center space-y-6">
        <div className="space-y-2 text-center">
          <p className="text-sm text-muted-foreground">{getErrorMessage(error)}</p>
        </div>

        <div className="flex w-full flex-col gap-2">
          <Button asChild className="w-full">
            <Link href="/login">{t('error.backToLogin')}</Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/signup">{t('error.createAccount')}</Link>
          </Button>
        </div>
      </div>
    </AuthLayout>
  )
}
