'use client'

import { Loader2 } from 'lucide-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { redirect } from '@/i18n/navigation'
import { useSignOut } from '@/query/mutations/auth.mutation'
import { useAcceptOrganizationInvite } from '@/query/mutations/organizations.mutation'
import { useGetOrganizationInvite } from '@/query/queries/organizations.query'
import { useGetLoading, useGetProfile } from '@/stores/root.store'

export default function InvitePage() {
  const t = useTranslations('invite')
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const profile = useGetProfile()
  const isUserLoading = useGetLoading()

  const inviteId = params.id as string
  const { data: invite, isLoading, isError } = useGetOrganizationInvite(inviteId)
  const { mutate: signOut, isPending: isSigningOut } = useSignOut()

  const { mutate: acceptInvite, isPending: isAccepting } = useAcceptOrganizationInvite()

  const handleAcceptInvite = () => {
    acceptInvite(
      { inviteId },
      {
        onSuccess: () => {
          router.push(`/organizations/${invite?.organization_id}`)
        },
      },
    )
  }

  const handleSignOut = () => {
    signOut(undefined, {
      onSuccess: () => {
        setTimeout(() => {
          router.push(`/login?redirect_to=${encodeURIComponent(`/invite/${inviteId}`)}`)
        }, 500)
      },
    })
  }

  if (isLoading || isUserLoading || isSigningOut) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (isError) {
    return redirect({
      href: `/login?redirect_to=${encodeURIComponent(`/invite/${inviteId}`)}`,
      locale: 'en',
    })
  }

  if (profile && invite?.email !== profile.email) {
    return (
      <div className="container mx-auto flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>{t('wrongEmail.title')}</CardTitle>
            <CardDescription>
              {t('wrongEmail.description', {
                inviteEmail: invite?.email ?? '',
                currentEmail: profile?.email ?? '',
              })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="default" className="w-full" onClick={handleSignOut}>
              {t('wrongEmail.switchAccount')}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (invite?.status !== 'pending') {
    return (
      <div className="container mx-auto flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>{t('invalid.title')}</CardTitle>
            <CardDescription>{t('invalid.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full" onClick={() => router.push('/')}>
              {t('invalid.returnHome')}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto flex min-h-screen items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>{t('valid.title')}</CardTitle>
          <CardDescription>
            {t('valid.description', {
              organization: invite.organization.name,
              role: t(`roles.${invite.role}`),
            })}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg border p-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">{t('details.email')}</p>
              <p className="text-sm text-muted-foreground">{invite.email}</p>
            </div>
            <div className="mt-4 space-y-2">
              <p className="text-sm font-medium">{t('details.organization')}</p>
              <p className="text-sm text-muted-foreground">{invite.organization.name}</p>
            </div>
          </div>
          <div className="flex space-x-4">
            <Button variant="outline" className="w-full" onClick={() => router.push('/')}>
              {t('actions.decline')}
            </Button>
            <Button className="w-full" onClick={handleAcceptInvite} disabled={isAccepting}>
              {isAccepting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('actions.accept')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
