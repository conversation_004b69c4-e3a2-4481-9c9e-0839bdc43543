'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'
import { toast } from 'sonner'

import { redirect } from '@/i18n/navigation'
import { useConnectHubSpot } from '@/query/mutations/crm.mutation'
import { useGetCurrentOrganizationId } from '@/stores/root.store'

export default function HubSpotCallbackPage() {
  const t = useTranslations('Organizations.integrations.crm.hubspot')
  const router = useRouter()
  const searchParams = useSearchParams()
  const organizationId = useGetCurrentOrganizationId()
  const code = searchParams.get('code') as string
  const mode = searchParams.get('mode') as string
  const connectHubSpot = useConnectHubSpot(organizationId!)

  useEffect(() => {
    if (!organizationId || !code) return

    const handleConnection = async () => {
      try {
        await connectHubSpot.mutateAsync(code)

        router.push(`/organizations/${organizationId}/settings`)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        router.push(`/organizations/${organizationId}/settings`)
      }
    }

    if (!code) {
      toast.error(t('errors.noCode'))
      return redirect({ href: '/organizations', locale: 'en' })
    }

    handleConnection()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, code, mode])

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-xl font-semibold">{t('connecting')}</h1>
        <p className="mt-2 text-gray-600">{t('waitMessage')}</p>
      </div>
    </div>
  )
}
