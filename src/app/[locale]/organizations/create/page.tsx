'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { OrganizationHeader } from '@/components/organizations/organization-header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { COMPANY_SIZES, hasCompanySize, ORGANIZATION_TYPES } from '@/constants/organization'
import { useCreateOrganization } from '@/query/mutations/organizations.mutation'

const createOrganizationSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  type: z.custom<App.OrganizationType>(),
  company_size: z.custom<App.CompanySize>().nullable(),
})

type CreateOrganizationFormData = z.infer<typeof createOrganizationSchema>

export default function CreateOrganizationPage() {
  const t = useTranslations('Organizations')
  const { mutate: createOrganization, isPending } = useCreateOrganization()

  const form = useForm<CreateOrganizationFormData>({
    resolver: zodResolver(createOrganizationSchema),
    defaultValues: {
      name: '',
      type: 'personal',
      company_size: null,
    },
  })

  const watchOrganizationType = form.watch('type')
  const showCompanySize = hasCompanySize(watchOrganizationType)

  const onSubmit = (data: CreateOrganizationFormData) => {
    createOrganization(data)
  }

  return (
    <>
      <OrganizationHeader />
      <div className="container mx-auto max-w-2xl p-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('create.formTitle')}</CardTitle>
            <CardDescription>{t('create.formDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('create.fields.name.label')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormDescription>{t('create.fields.name.description')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('create.fields.type.label')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('create.fields.type.placeholder')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {ORGANIZATION_TYPES.map(type => (
                            <SelectItem key={type} value={type}>
                              {t(`types.${type}`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>{t('create.fields.type.description')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {showCompanySize && (
                  <FormField
                    control={form.control}
                    name="company_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('create.fields.size.label')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value ?? ''}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('create.fields.size.placeholder')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {COMPANY_SIZES.map(size => (
                              <SelectItem key={size} value={size}>
                                {size}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>{t('create.fields.size.description')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <Button type="submit" className="w-full" disabled={isPending}>
                  {isPending ? t('create.submitting') : t('create.submit')}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
