'use client'

import { Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { AddPhoneDialog } from '@/components/organizations/phones/add-phone-dialog'
import { PhonesTable } from '@/components/organizations/phones/phones-table'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useGetOrganizationPhones } from '@/query/queries/phones.query'

export default function PhonesPage() {
  const t = useTranslations('Organizations.phones')
  const params = useParams()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const { canManagePhones } = useOrganizationPermissions()

  const orgId = params.id as string
  const { data: phones = [], isLoading } = useGetOrganizationPhones(orgId)

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManagePhones && (
          <Button onClick={onOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addPhone')}
          </Button>
        )}
      </div>

      {phones.length === 0 && !isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noPhones')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManagePhones && <Button onClick={onOpen}>{t('getStarted')}</Button>}
            </div>
          </CardContent>
        </Card>
      ) : (
        <PhonesTable phones={phones} isLoading={isLoading} />
      )}

      <AddPhoneDialog open={isOpen} onClose={onClose} organizationId={orgId} />
    </div>
  )
}
