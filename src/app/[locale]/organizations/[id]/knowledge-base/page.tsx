'use client'

import { Eye, FileText, Link, MoreHorizontal, Plus, Trash2, Upload } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { CreateFileModal } from '@/components/organizations/knowledge-base/create-file-modal'
import { CreateTextModal } from '@/components/organizations/knowledge-base/create-text-modal'
import { CreateUrlModal } from '@/components/organizations/knowledge-base/create-url-modal'
import { DeleteKnowledgeBaseDialog } from '@/components/organizations/knowledge-base/delete-knowledge-base-dialog'
import { ViewKnowledgeBaseDialog } from '@/components/organizations/knowledge-base/view-knowledge-base-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useGetKnowledgeBases } from '@/query/queries/knowledge-base.query'
import { formatBytes } from '@/utils/format'
import { formatDate } from '@/utils/time'

export default function KnowledgeBasePage() {
  const t = useTranslations('Organizations.knowledgeBase')
  const params = useParams()
  const { canManageKnowledgeBase } = useOrganizationPermissions()

  // Modal states
  const {
    isOpen: isTextModalOpen,
    onOpen: onTextModalOpen,
    onClose: onTextModalClose,
  } = useDisclosure()

  const {
    isOpen: isUrlModalOpen,
    onOpen: onUrlModalOpen,
    onClose: onUrlModalClose,
  } = useDisclosure()

  const {
    isOpen: isViewModalOpen,
    onOpen: onViewModalOpen,
    onClose: onViewModalClose,
  } = useDisclosure()

  const {
    isOpen: isDeleteModalOpen,
    onOpen: onDeleteModalOpen,
    onClose: onDeleteModalClose,
  } = useDisclosure()

  const {
    isOpen: isFileModalOpen,
    onOpen: onFileModalOpen,
    onClose: onFileModalClose,
  } = useDisclosure()

  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<App.KnowledgeBase | null>(null)

  const orgId = params.id as string
  const { data: knowledgeBases = [], isLoading } = useGetKnowledgeBases(orgId)

  const handleView = (kb: App.KnowledgeBase) => {
    setSelectedKnowledgeBase(kb)
    onViewModalOpen()
  }

  const handleDelete = (kb: App.KnowledgeBase) => {
    setSelectedKnowledgeBase(kb)
    onDeleteModalOpen()
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div>
        <h3 className="text-lg font-medium">{t('title')}</h3>
        <p className="text-sm text-muted-foreground">{t('description')}</p>
      </div>

      {/* Action buttons */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <Button
          variant="outline"
          className="flex h-24 flex-col items-center justify-center gap-2"
          onClick={onUrlModalOpen}
          disabled={!canManageKnowledgeBase}>
          <Link className="h-6 w-6" />
          <span>{t('addUrl')}</span>
        </Button>
        <Button
          variant="outline"
          className="flex h-24 flex-col items-center justify-center gap-2"
          onClick={onFileModalOpen}
          disabled={!canManageKnowledgeBase}>
          <Upload className="h-6 w-6" />
          <span>{t('addFile')}</span>
        </Button>
        <Button
          variant="outline"
          className="flex h-24 flex-col items-center justify-center gap-2"
          onClick={onTextModalOpen}
          disabled={!canManageKnowledgeBase}>
          <FileText className="h-6 w-6" />
          <span>{t('buttonCreateText')}</span>
        </Button>
      </div>

      {/* Knowledge base table */}
      {isLoading ? (
        <div className="space-y-2">
          <div className="h-8 w-full animate-pulse rounded bg-muted"></div>
          <div className="h-16 w-full animate-pulse rounded bg-muted"></div>
          <div className="h-16 w-full animate-pulse rounded bg-muted"></div>
        </div>
      ) : knowledgeBases.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noKnowledgeBases')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageKnowledgeBase && (
                <Button onClick={onTextModalOpen}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('getStarted')}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('name')}</TableHead>
                <TableHead>{t('type')}</TableHead>
                <TableHead>{t('createdBy')}</TableHead>
                <TableHead>{t('createdAt')}</TableHead>
                <TableHead className="w-[100px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {knowledgeBases.map(kb => (
                <TableRow key={kb.id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-col gap-2">
                      <span>{kb.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {kb.metadata?.size_bytes
                          ? formatBytes(kb.metadata.size_bytes as number)
                          : null}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{t(`types.${kb.type}`)}</TableCell>
                  <TableCell>{kb.user?.name || t('unknown')}</TableCell>
                  <TableCell>{formatDate(kb.created_at)}</TableCell>
                  <TableCell className="flex items-center justify-end">
                    <DropdownMenu modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">{t('openMenu')}</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(kb)}>
                          <Eye className="mr-2 h-4 w-4" />
                          {t('view')}
                        </DropdownMenuItem>
                        {canManageKnowledgeBase && (
                          <DropdownMenuItem
                            onClick={() => handleDelete(kb)}
                            className="text-destructive focus:text-destructive">
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t('delete')}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Modals */}
      <CreateTextModal open={isTextModalOpen} onClose={onTextModalClose} organizationId={orgId} />
      <CreateUrlModal open={isUrlModalOpen} onClose={onUrlModalClose} organizationId={orgId} />
      <CreateFileModal open={isFileModalOpen} onClose={onFileModalClose} organizationId={orgId} />
      <ViewKnowledgeBaseDialog
        open={isViewModalOpen}
        onClose={onViewModalClose}
        knowledgeBase={selectedKnowledgeBase}
      />
      <DeleteKnowledgeBaseDialog
        open={isDeleteModalOpen}
        onClose={onDeleteModalClose}
        knowledgeBase={selectedKnowledgeBase}
        organizationId={orgId}
      />
    </div>
  )
}
