'use client'

import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

// TODO: Create TemplatesTable component or adapt AgentsTable
// import { TemplatesTable } from '@/components/organizations/agents/TemplatesTable'
import { AgentsTable } from '@/components/organizations/agents/AgentsTable' // Using AgentsTable for now
import { Card, CardContent } from '@/components/ui/card'
// import { useOrganizationPermissions } from '@/hooks/use-organization-permissions' // Removed unused import
import { useGetOrganizationTemplates } from '@/query/queries/agents.query'

export default function TemplatesPage() {
  const t = useTranslations('Organizations.templates') // Assuming translations exist here
  const params = useParams()

  const orgId = params.id as string
  // Fetch only agents marked as templates
  const { data: templates = [], isLoading } = useGetOrganizationTemplates(orgId)

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {/* Optional: Add a button to go to Create Agent page? */}
      </div>

      {templates.length === 0 && !isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noTemplates')}</h2>
              <p className="mb-4 text-muted-foreground">{t('createFirst')}</p>
              {/* Optional: Link to agents page or create page */}
            </div>
          </CardContent>
        </Card>
      ) : (
        // Using AgentsTable for now, might need refinement or a dedicated TemplatesTable
        // Pass templates data instead of all agents
        <AgentsTable agents={templates} isLoading={isLoading} organizationId={orgId} />
      )}
    </div>
  )
}
