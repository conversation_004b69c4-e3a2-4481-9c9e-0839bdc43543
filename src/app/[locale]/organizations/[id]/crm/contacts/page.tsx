'use client'

import { Plus } from 'lucide-react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { AddContactDialog } from '@/components/organizations/contacts/add-contact-dialog'
import { ContactsTable } from '@/components/organizations/contacts/contacts-table'
import { EditContactDialog } from '@/components/organizations/contacts/edit-contact-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useOrganizations } from '@/providers/organizations-provider'
import { useGetOrganizationContacts } from '@/query/queries/contacts.query'

function ContactsTableSkeleton() {
  const t = useTranslations('Organizations.contacts')

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('table.name')}</TableHead>
          <TableHead>{t('table.email')}</TableHead>
          <TableHead>{t('table.phone')}</TableHead>
          <TableHead>{t('table.jobTitle')}</TableHead>
          <TableHead className="w-[100px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {[...Array(5)].map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-4 w-[200px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[150px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[120px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-[100px]" />
            </TableCell>
            <TableCell>
              <Skeleton className="ml-auto h-8 w-8 rounded-md" />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export default function ContactsPage() {
  const t = useTranslations('Organizations.contacts')
  const params = useParams()
  const { connections, isLoadingConnections } = useOrganizations()
  const router = useRouter()
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()
  const { canManageContacts } = useOrganizationPermissions()
  const [selectedContact, setSelectedContact] = useState<App.CachedCRMContact | null>(null)
  // Check if HubSpot is connected
  const hubspotConnection = connections?.find(c => c.crm_type === 'hubspot')
  const isHubSpotConnected = !!hubspotConnection

  const orgId = params.id as string
  const { data: contacts = [], isLoading } = useGetOrganizationContacts(orgId, {
    enabled: isHubSpotConnected && !!orgId,
  })

  const handleEditContact = (contact: App.CachedCRMContact) => {
    setSelectedContact(contact)
    onEditOpen()
  }

  const handleConnectHubSpot = () => {
    router.push(`/organizations/${orgId}/settings`)
  }

  // If still loading connections, show skeleton
  if (isLoadingConnections) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <ContactsTableSkeleton />
      </div>
    )
  }

  // If HubSpot is not connected, show notice
  if (!isHubSpotConnected) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noHubSpotConnection')}</h2>
              <p className="mb-4 text-muted-foreground">{t('connectHubSpotFirst')}</p>
              <Button onClick={handleConnectHubSpot}>{t('connectHubSpot')}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageContacts && (
          <Button onClick={onAddOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addContact')}
          </Button>
        )}
      </div>

      {isLoading ? (
        <ContactsTableSkeleton />
      ) : contacts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noContacts')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageContacts && <Button onClick={onAddOpen}>{t('getStarted')}</Button>}
            </div>
          </CardContent>
        </Card>
      ) : (
        <ContactsTable
          organizationId={orgId}
          contacts={contacts}
          isLoading={isLoading}
          onEdit={handleEditContact}
        />
      )}

      <AddContactDialog open={isAddOpen} onClose={onAddClose} organizationId={orgId} />

      {selectedContact && (
        <EditContactDialog
          open={isEditOpen}
          onClose={() => {
            onEditClose()
            setSelectedContact(null)
          }}
          organizationId={orgId}
          contact={selectedContact}
        />
      )}
    </div>
  )
}
