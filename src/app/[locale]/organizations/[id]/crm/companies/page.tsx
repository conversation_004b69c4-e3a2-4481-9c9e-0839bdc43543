'use client'

import { Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { AddCompanyDialog } from '@/components/organizations/companies/add-company-dialog'
import {
  CompaniesTable,
  CompaniesTableSkeleton,
} from '@/components/organizations/companies/companies-table'
import { EditCompanyDialog } from '@/components/organizations/companies/edit-company-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useRouter } from '@/i18n/navigation'
import { useOrganizations } from '@/providers/organizations-provider'
import { useOrganizationCompanies } from '@/query/queries/companies.query'

export default function CompaniesPage() {
  const t = useTranslations('Organizations.companies')
  const params = useParams()
  const router = useRouter()
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()
  const { connections, isLoadingConnections } = useOrganizations()
  const { canManageCompanies } = useOrganizationPermissions()

  const [selectedCompany, setSelectedCompany] = useState<App.CachedCRMCompany | null>(null)

  const orgId = params.id as string

  const hubspotConnection = connections.find(c => c.crm_type === 'hubspot')
  const isHubSpotConnected = !!hubspotConnection

  const { data: companies = [], isLoading } = useOrganizationCompanies(orgId)

  // Check if HubSpot is connected

  const handleEditCompany = (company: App.CachedCRMCompany) => {
    setSelectedCompany(company)
    onEditOpen()
  }

  const handleConnectHubSpot = () => {
    router.push(`/organizations/${orgId}/settings`)
  }

  // If still loading connections, show skeleton
  if (isLoadingConnections) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <CompaniesTableSkeleton />
      </div>
    )
  }

  // If HubSpot is not connected, show notice
  if (!isHubSpotConnected) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noHubSpotConnection')}</h2>
              <p className="mb-4 text-muted-foreground">{t('connectHubSpotFirst')}</p>
              <Button onClick={handleConnectHubSpot}>{t('connectHubSpot')}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageCompanies && (
          <Button onClick={onAddOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addCompany')}
          </Button>
        )}
      </div>

      {companies.length === 0 && !isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noCompanies')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageCompanies && <Button onClick={onAddOpen}>{t('getStarted')}</Button>}
            </div>
          </CardContent>
        </Card>
      ) : (
        <CompaniesTable
          organizationId={orgId}
          companies={companies}
          isLoading={isLoading}
          onEdit={handleEditCompany}
        />
      )}

      <AddCompanyDialog
        open={isAddOpen}
        onClose={onAddClose}
        organizationId={orgId}
        connectionId={hubspotConnection?.id ?? ''}
      />

      {selectedCompany && (
        <EditCompanyDialog
          open={isEditOpen}
          onClose={() => {
            onEditClose()
            setSelectedCompany(null)
          }}
          organizationId={orgId}
          company={selectedCompany}
        />
      )}
    </div>
  )
}
