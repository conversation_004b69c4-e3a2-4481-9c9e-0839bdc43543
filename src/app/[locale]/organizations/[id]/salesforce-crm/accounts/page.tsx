'use client'

import { Plus } from 'lucide-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  AccountsTable,
  AccountsTableSkeleton,
} from '@/components/organizations/salesforce/accounts-table'
import { EditorAccountDialog } from '@/components/organizations/salesforce/editor-account-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useOrganizations } from '@/providers/organizations-provider'
import { useGetSalesforceAccounts } from '@/query/queries/crm.query'
import { GetSalesforceAccountsParameters } from '@/services/api/types'

export default function AccountsPage() {
  const t = useTranslations('Organizations.salesforce.accounts')
  const params = useParams()
  const router = useRouter()
  const { isOpen: isEditorOpen, onOpen: onEditorOpen, onClose: onEditorClose } = useDisclosure()
  const { canManageSalesforceAccounts } = useOrganizationPermissions()
  const [selectedAccount, setSelectedAccount] = useState<App.ParagonSalesforceAccount | null>(null)
  const { connections, isLoadingConnections } = useOrganizations()

  const orgId = params.id as string

  // Check if Salesforce is connected
  const salesforceConnection = connections?.find(c => c.crm_type === 'salesforce')
  const isSalesforceConnected = !!salesforceConnection

  const parameters: GetSalesforceAccountsParameters = {
    paginationParameters: {
      pageCursor: '0',
    },
  }

  const { data: accountsData, isLoading } = useGetSalesforceAccounts(orgId, parameters, {
    enabled: !!orgId && isSalesforceConnected,
  })

  const accounts = accountsData?.records.records ?? []

  const handleConnectSalesforce = () => {
    router.push(`/organizations/${orgId}/settings`)
  }
  const onAddOpen = () => {
    setSelectedAccount(null)
    onEditorOpen()
  }

  const onEditOpen = (account: App.ParagonSalesforceAccount) => {
    setSelectedAccount(account)
    onEditorOpen()
  }

  const onCloseEditor = () => {
    setSelectedAccount(null)
    onEditorClose()
  }

  // If still loading connections, show skeleton
  if (isLoadingConnections) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <AccountsTableSkeleton />
      </div>
    )
  }

  // If Salesforce is not connected, show notice
  if (!isSalesforceConnected) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noSalesforceConnection')}</h2>
              <p className="mb-4 text-muted-foreground">{t('connectSalesforceFirst')}</p>
              <Button onClick={handleConnectSalesforce}>{t('connectSalesforce')}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageSalesforceAccounts && (
          <Button onClick={onAddOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addAccount')}
          </Button>
        )}
      </div>
      {isLoading ? (
        <AccountsTableSkeleton />
      ) : accounts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noAccounts')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageSalesforceAccounts && (
                <Button onClick={onAddOpen}>{t('getStarted')}</Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <AccountsTable
          accounts={accounts}
          isLoading={isLoading}
          onEdit={onEditOpen}
          organizationId={orgId}
          parameters={parameters}
        />
      )}
      {/* TODO: Implement AddAccountDialog and EditAccountDialog components */}
      <EditorAccountDialog
        open={isEditorOpen}
        onClose={onCloseEditor}
        organizationId={orgId}
        account={selectedAccount}
        parameters={parameters}
      />
    </div>
  )
}
