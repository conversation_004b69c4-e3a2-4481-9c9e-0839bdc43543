'use client'

import { Plus } from 'lucide-react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { EditorLeadDialog } from '@/components/organizations/salesforce/editor-lead-dialog'
import { LeadsTable, LeadsTableSkeleton } from '@/components/organizations/salesforce/leads-table'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useOrganizations } from '@/providers/organizations-provider'
import { useGetSalesforceLeads } from '@/query/queries/crm.query'
import { GetSalesforceLeadsParameters } from '@/services/api/types'

export default function SalesPage() {
  const t = useTranslations('Organizations.salesforce.sales')
  const params = useParams()
  const router = useRouter()
  const { isOpen: isEditorOpen, onOpen: onEditorOpen, onClose: onEditorClose } = useDisclosure()
  const { canManageSalesforceLeads } = useOrganizationPermissions()
  const [selectedLead, setSelectedLead] = useState<App.ParagonSalesforceLead | null>(null)
  const { connections, isLoadingConnections } = useOrganizations()

  const orgId = params.id as string

  // Check if Salesforce is connected
  const salesforceConnection = connections?.find(c => c.crm_type === 'salesforce')
  const isSalesforceConnected = !!salesforceConnection

  const parameters: GetSalesforceLeadsParameters = {
    paginationParameters: {
      pageCursor: '0',
    },
  }

  const { data: leadsData, isLoading } = useGetSalesforceLeads(orgId, parameters, {
    enabled: !!orgId && isSalesforceConnected,
  })

  const leads = leadsData?.records.records ?? []

  const handleConnectSalesforce = () => {
    router.push(`/organizations/${orgId}/settings`)
  }

  const onAddOpen = () => {
    setSelectedLead(null)
    onEditorOpen()
  }

  const onEditOpen = (lead: App.ParagonSalesforceLead) => {
    setSelectedLead(lead)
    onEditorOpen()
  }

  const onCloseEditor = () => {
    setSelectedLead(null)
    onEditorClose()
  }

  // If still loading connections, show skeleton
  if (isLoadingConnections) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <LeadsTableSkeleton />
      </div>
    )
  }

  // If Salesforce is not connected, show notice
  if (!isSalesforceConnected) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noSalesforceConnection')}</h2>
              <p className="mb-4 text-muted-foreground">{t('connectSalesforceFirst')}</p>
              <Button onClick={handleConnectSalesforce}>{t('connectSalesforce')}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageSalesforceLeads && (
          <Button onClick={onAddOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addLead')}
          </Button>
        )}
      </div>
      {isLoading ? (
        <LeadsTableSkeleton />
      ) : leads.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noLeads')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageSalesforceLeads && <Button onClick={onAddOpen}>{t('getStarted')}</Button>}
            </div>
          </CardContent>
        </Card>
      ) : (
        <LeadsTable
          leads={leads}
          isLoading={isLoading}
          onEdit={onEditOpen}
          organizationId={orgId}
          parameters={parameters}
        />
      )}
      <EditorLeadDialog
        open={isEditorOpen}
        onClose={onCloseEditor}
        organizationId={orgId}
        lead={selectedLead}
        parameters={parameters}
      />
    </div>
  )
}
