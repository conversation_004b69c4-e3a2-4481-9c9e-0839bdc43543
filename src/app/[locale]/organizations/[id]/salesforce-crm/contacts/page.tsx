'use client'

import { Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import {
  ContactsTable,
  ContactsTableSkeleton,
} from '@/components/organizations/salesforce/contacts-table'
import { EditorContactDialog } from '@/components/organizations/salesforce/editor-contact-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useRouter } from '@/i18n/navigation'
import { useOrganizations } from '@/providers/organizations-provider'
import { useGetSalesforceContacts } from '@/query/queries/crm.query'
import { GetSalesforceContactsParameters } from '@/services/api/types'

export default function ContactsPage() {
  const t = useTranslations('Organizations.salesforce.contacts')
  const params = useParams()
  const router = useRouter()
  const { isOpen: isEditorOpen, onOpen: onEditorOpen, onClose: onEditorClose } = useDisclosure()
  const { canManageSalesforceContacts } = useOrganizationPermissions()
  const [selectedContact, setSelectedContact] = useState<App.ParagonSalesforceContact | null>(null)
  const { connections, isLoadingConnections } = useOrganizations()

  const orgId = params.id as string

  // Check if Salesforce is connected
  const salesforceConnection = connections?.find(c => c.crm_type === 'salesforce')
  const isSalesforceConnected = !!salesforceConnection

  const parameters: GetSalesforceContactsParameters = {
    paginationParameters: {
      pageCursor: '0',
    },
  }

  const { data: contactsData, isLoading } = useGetSalesforceContacts(orgId, parameters, {
    enabled: !!orgId && isSalesforceConnected,
  })

  const contacts = contactsData?.records.records ?? []

  const handleConnectSalesforce = () => {
    router.push(`/organizations/${orgId}/settings`)
  }

  const onAddOpen = () => {
    setSelectedContact(null)
    onEditorOpen()
  }

  const onEditOpen = (contact: App.ParagonSalesforceContact) => {
    setSelectedContact(contact)
    onEditorOpen()
  }

  const onCloseEditor = () => {
    setSelectedContact(null)
    onEditorClose()
  }

  // If still loading connections, show skeleton
  if (isLoadingConnections) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <ContactsTableSkeleton />
      </div>
    )
  }

  // If Salesforce is not connected, show notice
  if (!isSalesforceConnected) {
    return (
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">{t('title')}</h3>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noSalesforceConnection')}</h2>
              <p className="mb-4 text-muted-foreground">{t('connectSalesforceFirst')}</p>
              <Button onClick={handleConnectSalesforce}>{t('connectSalesforce')}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageSalesforceContacts && (
          <Button onClick={onAddOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addContact')}
          </Button>
        )}
      </div>
      {isLoading ? (
        <ContactsTableSkeleton />
      ) : contacts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noContacts')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageSalesforceContacts && (
                <Button onClick={onAddOpen}>{t('getStarted')}</Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <ContactsTable contacts={contacts} isLoading={isLoading} onEdit={onEditOpen} />
      )}
      <EditorContactDialog
        open={isEditorOpen}
        onClose={onCloseEditor}
        organizationId={orgId}
        contact={selectedContact}
        parameters={parameters}
      />
    </div>
  )
}
