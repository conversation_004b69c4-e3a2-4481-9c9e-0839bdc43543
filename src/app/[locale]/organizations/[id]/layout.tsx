'use client'

import { AppSidebar } from '@/components/organizations/app-sidebar'
import { OrganizationLayoutContent } from '@/components/organizations/organization-layout-content'
import { SidebarProvider } from '@/components/ui/sidebar'
import { OrganizationsProvider } from '@/providers/organizations-provider'

export default function OrganizationLayout({ children }: { children: React.ReactNode }) {
  return (
    <OrganizationsProvider>
      <SidebarProvider>
        <AppSidebar />
        <OrganizationLayoutContent>{children}</OrganizationLayoutContent>
      </SidebarProvider>
    </OrganizationsProvider>
  )
}
