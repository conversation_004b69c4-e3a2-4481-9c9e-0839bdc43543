'use client'

import { Calculator, Clock, DollarSign, Phone } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useGetOrganization } from '@/query/queries/organizations.query'
import { getTimeOfDay } from '@/utils/time'

export default function OrganizationDashboardPage() {
  const params = useParams()
  const t = useTranslations('Organizations')
  const { data: organization } = useGetOrganization(params.id as string)

  const timeOfDay = getTimeOfDay()
  const greeting = t(`dashboard.greetings.${timeOfDay}`)

  const stats = [
    {
      title: t('dashboard.stats.calls'),
      value: '0',
      icon: Phone,
      subtitle: t('dashboard.stats.numberOfCalls'),
    },
    {
      title: t('dashboard.stats.duration'),
      value: '0:00',
      icon: Clock,
      subtitle: t('dashboard.stats.averageDuration'),
    },
    {
      title: t('dashboard.stats.totalCost'),
      value: '0',
      icon: DollarSign,
      subtitle: t('dashboard.stats.credits'),
    },
    {
      title: t('dashboard.stats.averageCost'),
      value: '0',
      icon: Calculator,
      subtitle: t('dashboard.stats.creditsPerCall'),
    },
  ]

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('dashboard.greeting', { greeting, name: organization?.name })}
          </h2>
          <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('dashboard.filters.allAgents')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('dashboard.filters.allAgents')}</SelectItem>
              {/* Add agent options here */}
            </SelectContent>
          </Select>
          <Select defaultValue="month">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('dashboard.filters.lastMonth')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t('dashboard.filters.today')}</SelectItem>
              <SelectItem value="week">{t('dashboard.filters.lastWeek')}</SelectItem>
              <SelectItem value="month">{t('dashboard.filters.lastMonth')}</SelectItem>
              <SelectItem value="year">{t('dashboard.filters.lastYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map(stat => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.subtitle}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="col-span-4">
        <CardHeader>
          <CardTitle>{t('dashboard.metrics.title')}</CardTitle>
        </CardHeader>
        <CardContent className="pl-2">
          {/* No metrics state */}
          <div className="flex h-[400px] flex-col items-center justify-center">
            <div className="mb-4">
              <svg
                className="h-12 w-12 text-muted-foreground/50"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round">
                <path d="M3 3v18h18" />
                <path d="m19 9-5 5-4-4-3 3" />
              </svg>
            </div>
            <p className="text-lg font-medium">{t('dashboard.metrics.noMetrics')}</p>
            <p className="text-sm text-muted-foreground">
              {t('dashboard.metrics.noMetricsDescription')}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
