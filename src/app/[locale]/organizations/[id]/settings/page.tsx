'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { BadgeCheck, BadgeX, Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Skeleton } from '@/components/ui/skeleton'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useOrganizations } from '@/providers/organizations-provider'
import {
  useDeleteCRMConnection,
  useGetHubSpotAuthUrl,
  useSyncCRMData,
} from '@/query/mutations/crm.mutation'
import { useUpdateOrganization } from '@/query/mutations/organizations.mutation'
import { useGetOrganizationCRMConnections } from '@/query/queries/crm.query'
import { useSetCurrentOrganizationId } from '@/stores/root.store'
import { logger } from '@/utils/logger'

const organizationSchema = z.object({
  name: z.string().min(2, {
    message: 'Organization name must be at least 2 characters.',
  }),
})

type OrganizationFormValues = z.infer<typeof organizationSchema>

export default function SettingsPage() {
  const t = useTranslations('Settings')
  const { currentOrganization } = useOrganizations()
  const organizationId = currentOrganization?.id
  const { canManageOrganization } = useOrganizationPermissions()
  const updateOrganization = useUpdateOrganization()
  const setCurrentOrganizationId = useSetCurrentOrganizationId()

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: currentOrganization?.name || '',
    },
  })

  const onSubmit = async (data: OrganizationFormValues) => {
    if (!organizationId) return

    try {
      await updateOrganization.mutateAsync({
        id: organizationId,
        data,
      })
      toast.success(t('organization.updateSuccess'))
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      toast.error(t('organization.updateError'))
    }
  }

  const { data: connections, isLoading } = useGetOrganizationCRMConnections(organizationId!)
  const deleteCRMConnection = useDeleteCRMConnection(organizationId!)
  const getHubSpotAuthUrl = useGetHubSpotAuthUrl()
  const syncCRMData = useSyncCRMData(currentOrganization?.id ?? '')

  const handleConnectHubSpot = () => {
    if (organizationId) {
      setCurrentOrganizationId(organizationId)
      getHubSpotAuthUrl.mutate()
    }
  }

  const handleSync = async (connectionId: string) => {
    try {
      await syncCRMData.mutateAsync({ connectionId })
      toast.success(t('crm.hubspot.syncSuccess'))
    } catch (error) {
      logger.error(error)
      toast.error(t('crm.hubspot.syncError'))
    }
  }

  useEffect(() => {
    if (!currentOrganization) return
    form.reset({
      name: currentOrganization.name || '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentOrganization])

  if (!organizationId) return null

  return (
    <div className="container mx-auto space-y-8 py-8">
      {/* Organization Settings Card - Only visible to admins/owners */}
      {canManageOrganization && (
        <Card>
          <CardHeader>
            <CardTitle>{t('organization.title')}</CardTitle>
            <CardDescription>{t('organization.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('organization.nameLabel')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" disabled={updateOrganization.isPending}>
                  {updateOrganization.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  {t('organization.save')}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {/* CRM Integration Card */}
      <Card>
        <CardHeader>
          <CardTitle>{t('crm.hubspot.title')}</CardTitle>
          <CardDescription>{t('crm.hubspot.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between rounded border p-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-9 w-24" />
              </div>
              <div className="flex items-center justify-between rounded border p-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-9 w-24" />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {connections?.map(
                connection =>
                  connection.crm_type === 'hubspot' && (
                    <div key={connection.id} className="flex flex-col space-y-4 rounded border p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{t('crm.hubspot.connected')}</p>
                          <p className="text-sm text-gray-500">
                            {connection.crm_instance_identifier}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          {(!connection.is_synced_contacts ||
                            !connection.is_synced_companies ||
                            !connection.is_synced_deals) && (
                            <Button
                              variant="secondary"
                              onClick={() => handleSync(connection.id)}
                              disabled={syncCRMData.isPending}>
                              {syncCRMData.isPending && (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              )}
                              {t('crm.hubspot.sync')}
                            </Button>
                          )}
                          <Button
                            variant="destructive"
                            onClick={() => deleteCRMConnection.mutate(connection.id)}
                            disabled={deleteCRMConnection.isPending}>
                            {t('crm.hubspot.disconnect')}
                          </Button>
                        </div>
                      </div>

                      {/* Sync Status Section */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {connection.is_synced_contacts ? (
                            <BadgeCheck className="h-4 w-4 text-green-600" />
                          ) : (
                            <BadgeX className="h-4 w-4 text-red-600" />
                          )}
                          <Label>{t('crm.hubspot.syncedContacts')}</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          {connection.is_synced_companies ? (
                            <BadgeCheck className="h-4 w-4 text-green-600" />
                          ) : (
                            <BadgeX className="h-4 w-4 text-red-600" />
                          )}
                          <Label>{t('crm.hubspot.syncedCompanies')}</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          {connection.is_synced_deals ? (
                            <BadgeCheck className="h-4 w-4 text-green-600" />
                          ) : (
                            <BadgeX className="h-4 w-4 text-red-600" />
                          )}
                          <Label>{t('crm.hubspot.syncedDeals')}</Label>
                        </div>
                      </div>
                    </div>
                  ),
              )}

              {!connections?.some(c => c.crm_type === 'hubspot') && (
                <Button onClick={handleConnectHubSpot} disabled={getHubSpotAuthUrl.isPending}>
                  {getHubSpotAuthUrl.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t('crm.hubspot.connect')}
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
