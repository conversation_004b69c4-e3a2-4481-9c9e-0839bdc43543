'use client'

import { Plus } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { InviteMemberDialog } from '@/components/organizations/team/invite-member-dialog'
import { TeamInvitesTable } from '@/components/organizations/team/team-invites-table'
import { TeamMembersTable } from '@/components/organizations/team/team-members-table'
import { Button } from '@/components/ui/button'
import { useDisclosure } from '@/hooks/use-disclosure'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import {
  useGetOrganizationInvites,
  useGetOrganizationMembers,
} from '@/query/queries/organizations.query'

export default function TeamSettingsPage() {
  const t = useTranslations('Settings.team')
  const params = useParams()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const { canInviteMembers } = useOrganizationPermissions()

  const orgId = params.id as string
  const { data: members = [], isLoading: isLoadingMembers } = useGetOrganizationMembers(orgId)
  const { data: invites = [], isLoading: isLoadingInvites } = useGetOrganizationInvites(orgId)

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canInviteMembers && (
          <Button onClick={onOpen}>
            <Plus className="mr-2 h-4 w-4" />
            {t('inviteMember')}
          </Button>
        )}
      </div>

      <TeamMembersTable members={members} isLoading={isLoadingMembers} />

      {canInviteMembers && <TeamInvitesTable invites={invites} isLoading={isLoadingInvites} />}

      <InviteMemberDialog open={isOpen} onClose={onClose} organizationId={orgId} />
    </div>
  )
}
