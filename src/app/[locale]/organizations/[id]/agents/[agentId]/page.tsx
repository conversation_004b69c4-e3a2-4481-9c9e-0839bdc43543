'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { AgentForm } from '@/components/organizations/agents/AgentForm'
import { Skeleton } from '@/components/ui/skeleton'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { useUpdateAgent } from '@/query/mutations/agents.mutation'
import { useGetAgent } from '@/query/queries/agents.query'
import { UpdateAgentPayload } from '@/services/api/types'

export default function EditAgentPage() {
  const t = useTranslations('Organizations.agents.edit')
  const params = useParams()
  const router = useRouter()
  const { canManageAgents } = useOrganizationPermissions()
  const updateAgent = useUpdateAgent()

  const orgId = params.id as string
  const agentId = params.agentId as string

  const { data: agentData, isLoading: isLoadingAgent } = useGetAgent(orgId, agentId)

  // Redirect if user doesn't have permission (or if agent loading fails)
  // Note: This check might be better handled by middleware or layout checks
  if (!isLoadingAgent && !agentData) {
    // Agent not found or access denied based on RLS potentially
    // router.push(`/organizations/${orgId}/agents`);
    return <p>Agent not found or access denied.</p> // Placeholder
  }
  if (!canManageAgents) {
    return <p>Access Denied</p> // Placeholder
  }

  const handleSubmit = async (values: UpdateAgentPayload) => {
    try {
      await updateAgent.mutateAsync({
        organization_id: orgId,
        agent_id: agentId,
        agentData: values,
      })
      // Redirect to the agents list page on success
      router.push(`/organizations/${orgId}/agents`)
    } catch (error) {
      // Error is handled by the mutation hook's onError
      console.error('Update agent failed:', error)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
      </div>

      {isLoadingAgent ? (
        <div className="space-y-8">
          <Skeleton className="h-10 w-1/2" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-16 w-full" />
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
      ) : (
        <AgentForm
          initialData={agentData}
          onSubmit={handleSubmit}
          isSubmitting={updateAgent.isPending}
          submitButtonText={t('submitButton')}
        />
      )}
    </div>
  )
}
