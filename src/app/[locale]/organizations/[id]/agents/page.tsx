'use client'

import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

import { AgentCreateSheet } from '@/components/organizations/agents/AgentCreateSheet'
import { AgentsTable } from '@/components/organizations/agents/AgentsTable'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useOrganizationPermissions } from '@/hooks/use-organization-permissions'
import { Link } from '@/i18n/navigation' // Use the i18n Link
import { useGetOrganizationAgents } from '@/query/queries/agents.query'
// TODO: Add route for create agent page if needed
// import { Routes } from '@/i18n/routing'

export default function AgentsPage() {
  const t = useTranslations('Organizations.agents')
  const params = useParams()
  const { canManageAgents } = useOrganizationPermissions()

  const orgId = params.id as string
  const { data: agents = [], isLoading } = useGetOrganizationAgents(orgId)

  // Construct create link manually
  const createHref = `/organizations/${orgId}/agents/create`

  return (
    <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t('title')}</h3>
          <p className="text-sm text-muted-foreground">{t('description')}</p>
        </div>
        {canManageAgents && <AgentCreateSheet />}
      </div>

      {agents.length === 0 && !isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold">{t('noAgents')}</h2>
              <p className="mb-4 text-muted-foreground">{t('addFirst')}</p>
              {canManageAgents && (
                <Button asChild>
                  <Link href={createHref}>{t('getStarted')}</Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <AgentsTable agents={agents} isLoading={isLoading} organizationId={orgId} />
      )}

      {/* TODO: Add Create/Edit Agent Dialog/Page logic if needed */}
    </div>
  )
}
