'use client'

import { Plus, Users } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { OrganizationHeader } from '@/components/organizations/organization-header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Link } from '@/i18n/navigation'
import { useGetUserOrganizations } from '@/query/queries/organizations.query'

export default function OrganizationsPage() {
  const t = useTranslations('Organizations')
  const { data: organizations = [], isLoading: isLoadingOrgs } = useGetUserOrganizations()

  const getOrganizationTypeLabel = (type: App.OrganizationType) => {
    const labels = {
      personal: t('types.personal'),
      education: t('types.education'),
      startup: t('types.startup'),
      agency: t('types.agency'),
      company: t('types.company'),
    }
    return labels[type]
  }

  if (isLoadingOrgs) {
    return (
      <>
        <OrganizationHeader />
        <div className="container mx-auto p-6">
          <div className="mb-6 flex items-center justify-between">
            <div />
            <Skeleton className="h-9 w-[140px]" />
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle>
                    <Skeleton className="h-6 w-[200px]" />
                  </CardTitle>
                  <Skeleton className="mt-2 h-4 w-[150px]" />
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Skeleton className="mr-2 h-4 w-4" />
                    <Skeleton className="h-4 w-[100px]" />
                    <Skeleton className="mx-2 h-4 w-[4px]" />
                    <Skeleton className="h-4 w-[60px]" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <OrganizationHeader />
      <div className="container mx-auto p-6">
        <div className="mb-6 flex items-center justify-between">
          <div />
          <Button asChild>
            <Link href="/organizations/create">
              <Plus className="mr-2 h-4 w-4" />
              {t('createNew')}
            </Link>
          </Button>
        </div>

        {organizations.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center p-6">
              <div className="text-center">
                <h2 className="mb-2 text-xl font-semibold">{t('noOrganizations')}</h2>
                <p className="mb-4 text-muted-foreground">{t('createFirst')}</p>
                <Button asChild>
                  <Link href="/organizations/create">{t('getStarted')}</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {organizations.map(org => (
              <Card key={org.id} className="transition-shadow hover:shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <Link
                      href={`/organizations/${org.id}`}
                      className="transition-colors hover:text-primary">
                      {org.name}
                    </Link>
                  </CardTitle>
                  <div className="text-sm text-muted-foreground">
                    {getOrganizationTypeLabel(org.type)}
                    {org.company_size && ` • ${org.company_size}`}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Users className="mr-2 h-4 w-4" />
                    {t('members', { count: org.member_count })}
                    <span className="mx-2">•</span>
                    {t(`roles.${org.role}`)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </>
  )
}
