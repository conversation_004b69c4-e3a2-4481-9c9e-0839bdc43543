import { getTranslations } from 'next-intl/server'

import { Footer } from '@/components/layout/footer'
import { Header } from '@/components/layout/header'

export default async function TermsPage() {
  const t = await getTranslations('legal.terms')

  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="container max-w-3xl py-12">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold">{t('title')}</h1>
              <p className="text-lg text-muted-foreground">{t('description')}</p>
              <p className="text-sm text-muted-foreground">{t('lastUpdated')}</p>
            </div>

            <div className="space-y-8">
              <section className="space-y-4">
                <h2 className="text-2xl font-semibold">{t('sections.acceptance.title')}</h2>
                <p className="leading-7">{t('sections.acceptance.content')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold">{t('sections.changes.title')}</h2>
                <p className="leading-7">{t('sections.changes.content')}</p>
              </section>

              <section className="space-y-4">
                <h2 className="text-2xl font-semibold">{t('sections.privacy.title')}</h2>
                <p className="leading-7">{t('sections.privacy.content')}</p>
              </section>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
