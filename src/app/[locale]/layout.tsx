import './globals.css'

import { Geist } from 'next/font/google'
import { notFound } from 'next/navigation'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'

import { Locale, routing } from '@/i18n/routing'
import { AppProvider } from '@/providers/app-providers'

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000'

export const metadata = {
  metadataBase: new URL(defaultUrl),
  title: 'Foundation - Build Better Software Faster',
  description:
    'A modern foundation for building products. Powerful, extensible, and designed for innovation.',
}

const geistSans = Geist({
  display: 'swap',
  subsets: ['latin'],
})

type Params = Promise<{ locale: Locale }>

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: Params
}) {
  const { locale = 'en' } = await params

  if (!routing.locales.includes(locale)) {
    notFound()
  }

  const messages = await getMessages()

  return (
    <html lang={locale} className={geistSans.className} suppressHydrationWarning>
      <body className="bg-background text-foreground">
        <NextIntlClientProvider messages={messages}>
          <AppProvider>
            <main className="flex min-h-screen flex-col items-center">{children}</main>
          </AppProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
