import { getTranslations } from 'next-intl/server'

import { Footer } from '@/components/layout/footer'
import { Header } from '@/components/layout/header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Link } from '@/i18n/navigation'

export default async function Home() {
  const t = await getTranslations('Home')

  const features = [
    {
      title: t('features.feature1.title'),
      description: t('features.feature1.description'),
      icon: '🚀',
    },
    {
      title: t('features.feature2.title'),
      description: t('features.feature2.description'),
      icon: '🛡️',
    },
    {
      title: t('features.feature3.title'),
      description: t('features.feature3.description'),
      icon: '⚡',
    },
  ]

  return (
    <>
      <Header />
      <div className="flex flex-col items-center justify-center">
        {/* Hero Section */}
        <section className="container flex flex-col items-center justify-center gap-4 py-24 text-center md:py-32">
          <h1 className="text-3xl font-bold sm:text-5xl md:text-6xl lg:text-7xl">
            {t('hero.title')}
          </h1>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            {t('hero.description')}
          </p>
          <div className="flex gap-4">
            <Button asChild size="lg">
              <Link href="/get-started">{t('hero.primaryCTA')}</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/docs">{t('hero.secondaryCTA')}</Link>
            </Button>
          </div>
        </section>

        {/* Features Section */}
        <section className="container py-24 sm:py-32">
          <div className="mx-auto grid items-center gap-8 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="mb-4 text-4xl">{feature.icon}</div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="container py-24 sm:py-32">
          <div className="mx-auto rounded-lg bg-muted px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">{t('cta.title')}</h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
                {t('cta.description')}
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button asChild size="lg">
                  <Link href="/signup">{t('cta.primaryCTA')}</Link>
                </Button>
                <Button asChild variant="link" size="lg">
                  <Link href="/contact">{t('cta.secondaryCTA')}</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  )
}
