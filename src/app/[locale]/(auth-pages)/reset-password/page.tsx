'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Link } from '@/i18n/navigation'
import { useResetPassword } from '@/query/mutations/auth.mutation'

export default function ResetPasswordPage() {
  const t = useTranslations('auth')
  const { mutate: resetPassword, isPending } = useResetPassword()

  const resetPasswordSchema = z
    .object({
      password: z.string().min(6, t('resetPassword.passwordMinLength')),
      confirmPassword: z.string(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('resetPassword.passwordMismatch'),
      path: ['confirmPassword'],
    })

  const form = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: z.infer<typeof resetPasswordSchema>) => {
    resetPassword(data.password)
  }

  return (
    <AuthLayout title={t('resetPassword.title')} description={t('resetPassword.description')}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('password')}</FormLabel>
                <FormControl>
                  <Input {...field} type="password" disabled={isPending} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('confirmPassword')}</FormLabel>
                <FormControl>
                  <Input {...field} type="password" disabled={isPending} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending ? t('resetPassword.resetting') : t('resetPassword.submit')}
          </Button>

          <div className="text-center text-sm">
            <Button variant="link" className="h-auto px-0 py-0" asChild>
              <Link href="/login">{t('resetPassword.backToLogin')}</Link>
            </Button>
          </div>
        </form>
      </Form>
    </AuthLayout>
  )
}
