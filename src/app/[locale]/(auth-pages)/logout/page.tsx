'use client'

import { Loader2 } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect } from 'react'

import { AuthLayout } from '@/components/auth/auth-layout'
import { redirect } from '@/i18n/navigation'
import { useSignOut } from '@/query/mutations/auth.mutation'
import { useIsAuthenticated } from '@/stores/root.store'

export default function LogoutPage() {
  const t = useTranslations('auth')
  const isAuthenticated = useIsAuthenticated()
  const { mutate: signOut, isPending } = useSignOut()

  useEffect(() => {
    signOut()
  }, [signOut])

  useEffect(() => {
    if (!isAuthenticated) {
      redirect({ href: '/login', locale: 'en' })
    }
  }, [isAuthenticated])

  return (
    <AuthLayout title={t('logout.title')} description={t('logout.description')}>
      <div className="flex flex-col items-center justify-center space-y-4">
        {isPending && <Loader2 className="h-8 w-8 animate-spin" />}
        <p className="text-muted-foreground">{t('logout.signingOut')}</p>
      </div>
    </AuthLayout>
  )
}
