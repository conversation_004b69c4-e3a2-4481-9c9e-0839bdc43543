'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp'
import { useResendVerification, useVerifyOtp } from '@/query/mutations/auth.mutation'

const verifySchema = z.object({
  otp: z.string().length(6, 'Please enter a valid verification code'),
})

type VerifyFormData = z.infer<typeof verifySchema>

export default function VerifyPage() {
  const t = useTranslations('auth')
  const searchParams = useSearchParams()
  const email = searchParams.get('email')
  const redirectTo = searchParams.get('redirect_to')
  const [seconds, setSeconds] = useState(60)
  const isActive = seconds > 0
  const verifyOtpMutation = useVerifyOtp(redirectTo || undefined)
  const resendMutation = useResendVerification()

  const form = useForm<VerifyFormData>({
    resolver: zodResolver(verifySchema),
    defaultValues: {
      otp: '',
    },
  })

  useEffect(() => {
    if (!email) return

    let timer: NodeJS.Timeout
    if (isActive) {
      timer = setInterval(() => {
        setSeconds(prev => prev - 1)
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [email, isActive])

  const onSubmit = (data: VerifyFormData) => {
    verifyOtpMutation.mutate({ email: email!, token: data.otp })
  }

  const handleResendCode = () => {
    if (!email || isActive) return
    resendMutation.mutate(email)
    setSeconds(60)
  }

  const formatCountdown = (seconds: number) => {
    return t('verify.resendCountdown', { seconds })
  }

  if (!email) {
    return (
      <AuthLayout title={t('verify.error.title')} description={t('verify.error.description')}>
        <div className="flex w-full flex-col items-center space-y-6"></div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout title={t('verify.title')} description={t('verify.description')}>
      <div className="flex w-full flex-col items-center space-y-6">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">{t('verify.emailSent', { email })}</p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="otp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('verify.codeLabel')}</FormLabel>
                  <FormControl>
                    <InputOTP
                      maxLength={6}
                      value={field.value}
                      onChange={field.onChange}
                      disabled={verifyOtpMutation.isPending}>
                      <InputOTPGroup>
                        <InputOTPSlot index={0} />
                        <InputOTPSlot index={1} />
                        <InputOTPSlot index={2} />
                        <InputOTPSlot index={3} />
                        <InputOTPSlot index={4} />
                        <InputOTPSlot index={5} />
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <Button type="submit" className="w-full" disabled={verifyOtpMutation.isPending}>
                {verifyOtpMutation.isPending ? t('verify.verifying') : t('verify.submit')}
              </Button>

              <Button
                type="button"
                variant="ghost"
                className="w-full"
                onClick={handleResendCode}
                disabled={resendMutation.isPending || isActive}>
                {resendMutation.isPending
                  ? t('verify.resending')
                  : isActive
                    ? formatCountdown(seconds)
                    : t('verify.resend')}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </AuthLayout>
  )
}
