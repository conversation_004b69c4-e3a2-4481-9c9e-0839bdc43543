'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Link } from '@/i18n/navigation'
import { useResetPasswordRequest } from '@/query/mutations/auth.mutation'

export default function ForgotPasswordPage() {
  const t = useTranslations('auth')
  const [isSuccess, setIsSuccess] = useState(false)
  const { mutate: requestReset, isPending } = useResetPasswordRequest()

  const forgotPasswordSchema = z.object({
    email: z.string().email(t('forgotPassword.invalidEmail')),
  })

  const form = useForm<z.infer<typeof forgotPasswordSchema>>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  const onSubmit = (data: z.infer<typeof forgotPasswordSchema>) => {
    requestReset(data.email, {
      onSuccess: () => setIsSuccess(true),
    })
  }

  if (isSuccess) {
    return (
      <AuthLayout
        title={t('forgotPassword.success.title')}
        description={t('forgotPassword.success.description')}>
        <div className="space-y-6">
          <p className="text-sm text-muted-foreground">{t('forgotPassword.success.message')}</p>
          <Button className="w-full" asChild>
            <Link href="/login">{t('forgotPassword.backToLogin')}</Link>
          </Button>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout title={t('forgotPassword.title')} description={t('forgotPassword.description')}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('email')}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending ? t('forgotPassword.requesting') : t('forgotPassword.submit')}
          </Button>

          <div className="text-center text-sm">
            <Button variant="link" className="h-auto px-0 py-0" asChild>
              <Link href="/login">{t('forgotPassword.backToLogin')}</Link>
            </Button>
          </div>
        </form>
      </Form>
    </AuthLayout>
  )
}
