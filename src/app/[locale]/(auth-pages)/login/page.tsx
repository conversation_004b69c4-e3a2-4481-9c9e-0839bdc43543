'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Provider } from '@supabase/supabase-js'
import { Loader2 } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Link } from '@/i18n/navigation'
import { useSignIn, useSignInWithOAuth } from '@/query/mutations/auth.mutation'

export default function LoginPage() {
  const t = useTranslations('auth')
  const { mutate: signIn, isPending: isSigningIn } = useSignIn()
  const { mutate: signInWithOAuth } = useSignInWithOAuth()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirect_to')

  const loginSchema = z.object({
    email: z.string().email(t('login.invalidEmail')),
    password: z.string().min(6, t('login.passwordMinLength')),
  })

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = (data: z.infer<typeof loginSchema>) => {
    signIn(data)
  }

  const handleSocialLogin = (provider: Provider) => {
    signInWithOAuth({ provider })
  }

  return (
    <AuthLayout title={t('login.title')} description={t('login.description')}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('email')}</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>{t('password')}</FormLabel>
                </div>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full" disabled={isSigningIn}>
            {isSigningIn && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t('login.submit')}
          </Button>
        </form>
      </Form>
      <div className="mt-4 flex flex-col gap-1">
        <div className="flex items-center justify-center">
          <Button variant="link" className="h-auto px-0 py-0" asChild>
            <Link href="/forgot-password">{t('login.forgotPassword')}</Link>
          </Button>
        </div>
        <div className="text-center text-sm">
          {t('login.noAccount')}{' '}
          <Button variant="link" className="h-auto px-1" asChild>
            <Link href={`/signup${redirectTo ? `?redirect_to=${redirectTo}` : ''}`}>
              {t('login.signupLink')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">{t('social.or')}</span>
        </div>
      </div>

      <div className="grid gap-2">
        <Button variant="outline" onClick={() => handleSocialLogin('google')} type="button">
          <svg role="img" viewBox="0 0 24 24" className="mr-2 h-4 w-4">
            <path
              fill="currentColor"
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
            />
          </svg>
          {t('social.google')}
        </Button>
        <Button variant="outline" onClick={() => handleSocialLogin('azure')} type="button">
          <svg role="img" viewBox="0 0 24 24" className="mr-2 h-4 w-4">
            <path
              fill="currentColor"
              d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zm12.6 0H12.6V0H24v11.4z"
            />
          </svg>
          {t('social.microsoft')}
        </Button>
      </div>
    </AuthLayout>
  )
}
