'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Provider } from '@supabase/supabase-js'
import { Loader2 } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { AuthLayout } from '@/components/auth/auth-layout'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Link } from '@/i18n/navigation'
import { useSignInWithOAuth, useSignUp } from '@/query/mutations/auth.mutation'

export default function SignUpPage() {
  const t = useTranslations('auth')
  const { mutate: signUp, isPending: isSigningUp } = useSignUp()
  const { mutate: signInWithOAuth } = useSignInWithOAuth()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirect_to')

  const signupSchema = z
    .object({
      email: z.string().email(t('signup.invalidEmail')),
      password: z.string().min(6, t('signup.passwordMinLength')),
      confirmPassword: z.string().min(6, t('signup.passwordMinLength')),
      name: z.string().min(1, t('signup.nameRequired')),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('signup.passwordMismatch'),
      path: ['confirmPassword'],
    })

  const form = useForm<z.infer<typeof signupSchema>>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = (data: z.infer<typeof signupSchema>) => {
    signUp({
      email: data.email,
      password: data.password,
      name: data.name || data.email.split('@')[0], // Use email username as fallback name
    })
  }

  const handleSocialSignup = (provider: Provider) => {
    signInWithOAuth({ provider })
  }

  return (
    <AuthLayout title={t('signup.title')} description={t('signup.description')}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('name')}</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('email')}</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('password')}</FormLabel>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('confirmPassword')}</FormLabel>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full" disabled={isSigningUp}>
            {isSigningUp && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t('signup.submit')}
          </Button>
        </form>
      </Form>

      <div className="mt-4 text-center text-sm">
        {t('signup.haveAccount')}{' '}
        <Button variant="link" className="h-auto px-1" asChild>
          <Link href={`/login${redirectTo ? `?redirect_to=${redirectTo}` : ''}`}>
            {t('signup.loginLink')}
          </Link>
        </Button>
      </div>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">{t('social.or')}</span>
        </div>
      </div>

      <div className="grid gap-2">
        <Button variant="outline" onClick={() => handleSocialSignup('google')} type="button">
          <svg role="img" viewBox="0 0 24 24" className="mr-2 h-4 w-4">
            <path
              fill="currentColor"
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
            />
          </svg>
          {t('social.google')}
        </Button>
        <Button variant="outline" onClick={() => handleSocialSignup('azure')} type="button">
          <svg role="img" viewBox="0 0 24 24" className="mr-2 h-4 w-4">
            <path
              fill="currentColor"
              d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zm12.6 0H12.6V0H24v11.4z"
            />
          </svg>
          {t('social.microsoft')}
        </Button>
      </div>
    </AuthLayout>
  )
}
