'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2, Mail } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const Microsoft = ({ className }: { className: string }) => {
  return (
    <svg role="img" viewBox="0 0 24 24" className={className}>
      <path
        fill="currentColor"
        d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zm12.6 0H12.6V0H24v11.4z"
      />
    </svg>
  )
}

const Google = ({ className }: { className: string }) => {
  return (
    <svg role="img" viewBox="0 0 24 24" className={className}>
      <path
        fill="currentColor"
        d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
      />
    </svg>
  )
}

import { useEffect } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import UploadAvatar from '@/components/ui/upload-avatar'
import { useUploadAvatar } from '@/query/mutations/file.mutation'
import { useUpdatePassword, useUpdateProfile } from '@/query/mutations/profile.mutation'
import { getAuthError } from '@/services/auth-error'
import { CustomFile } from '@/services/types'
import { useGetProfile } from '@/stores/root.store'

const ProviderIcon = ({ provider }: { provider: string }) => {
  switch (provider) {
    case 'google':
      return <Google className="h-5 w-5" />
    case 'azure':
      return <Microsoft className="h-5 w-5" />
    case 'email':
      return <Mail className="h-5 w-5" />
    default:
      return null
  }
}

export default function ProfilePage() {
  const t = useTranslations('profile')
  const profile = useGetProfile()

  const { mutateAsync: updateProfile } = useUpdateProfile()
  const { mutate: updatePassword, isPending: isUpdatingPassword } = useUpdatePassword()
  const { mutateAsync: uploadAvatar } = useUploadAvatar(profile?.id ?? '')

  const isEmailProvider = profile?.provider === 'email'

  const profileSchema = z.object({
    avatar: z.custom<CustomFile>().optional(),
    name: z.string().min(1, t('nameRequired')),
    email: z.string().email(t('invalidEmail')),
  })

  const passwordSchema = z
    .object({
      currentPassword: z.string().min(6, t('passwordMinLength')),
      newPassword: z.string().min(6, t('passwordMinLength')),
      confirmPassword: z.string().min(6, t('passwordMinLength')),
    })
    .refine(data => data.newPassword === data.confirmPassword, {
      message: t('passwordMismatch'),
      path: ['confirmPassword'],
    })

  const profileForm = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      avatar: profile?.avatar ? { url: profile?.avatar ?? '' } : undefined,
      name: profile?.name ?? '',
      email: profile?.email ?? '',
    },
  })

  const passwordForm = useForm<z.infer<typeof passwordSchema>>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  })

  const onProfileSubmit = async (data: z.infer<typeof profileSchema>) => {
    try {
      let avatar = undefined
      if (data.avatar instanceof File) {
        avatar = await uploadAvatar(data.avatar)
      } else {
        avatar = data?.avatar?.url
      }
      await updateProfile({
        data: { name: data.name, avatar },
        id: profile?.id || '',
      })
    } catch (error) {
      const { message } = getAuthError(error)
      toast.error(message)
    }
  }

  const onPasswordSubmit = (data: z.infer<typeof passwordSchema>) => {
    updatePassword({
      email: profile?.email || '',
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
    })
  }

  useEffect(() => {
    if (!profile) return
    profileForm.reset({
      name: profile.name ?? '',
      email: profile.email ?? '',
      avatar: profile.avatar ? { url: profile.avatar } : undefined,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profile])

  if (!profile) {
    return (
      <div className="container mx-auto max-w-2xl py-8">
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-7 w-48" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-5 w-96" />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* Profile Form Skeleton */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>

            <Separator />

            {/* Authentication Section Skeleton */}
            <div className="space-y-4">
              <div className="space-y-1">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-5 w-64" />
              </div>
              <div className="flex items-center space-x-4 rounded-lg border p-4">
                <Skeleton className="h-5 w-5" />
                <div className="space-y-1">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-48" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto max-w-2xl py-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('profileInformation')}</CardTitle>
          <CardDescription>{t('profileDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...profileForm}>
            <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
              <FormField
                control={profileForm.control}
                name="avatar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('avatar')}</FormLabel>
                    <FormControl>
                      <UploadAvatar value={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('name')}</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('email')}</FormLabel>
                    <FormControl>
                      <Input {...field} disabled />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={profileForm.formState.isSubmitting}>
                {profileForm.formState.isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('updateProfile')}
              </Button>
            </form>
          </Form>

          <Separator className="my-8" />

          {/* Authentication Provider Section */}
          <div>
            <h3 className="text-lg font-medium">{t('authentication')}</h3>
            <p className="text-sm text-muted-foreground">{t('authenticationDescription')}</p>

            <div className="mt-4 flex items-center space-x-4 rounded-lg border p-4">
              <ProviderIcon provider={profile?.provider || 'email'} />
              <div>
                <p className="font-medium">{t(`provider.${profile?.provider || 'email'}`)}</p>
                <p className="text-sm text-muted-foreground">
                  {isEmailProvider
                    ? t('emailAuthentication')
                    : t('socialAuthentication', {
                        provider: profile ? t(`provider.${profile?.provider}`) : '',
                      })}
                </p>
              </div>
            </div>
          </div>

          {isEmailProvider && (
            <>
              <Separator className="my-8" />
              <div>
                <h3 className="text-lg font-medium">{t('changePassword')}</h3>
                <p className="text-sm text-muted-foreground">{t('changePasswordDescription')}</p>
                <Form {...passwordForm}>
                  <form
                    onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                    className="mt-4 space-y-4">
                    <FormField
                      control={passwordForm.control}
                      name="currentPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('currentPassword')}</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={passwordForm.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('newPassword')}</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={passwordForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('confirmPassword')}</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" disabled={isUpdatingPassword}>
                      {isUpdatingPassword && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {t('updatePassword')}
                    </Button>
                  </form>
                </Form>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
