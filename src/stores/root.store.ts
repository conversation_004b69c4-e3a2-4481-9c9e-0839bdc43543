import { createOrganizationsSlice, OrganizationsStore } from './slices/organizations.slice'
import { createProfileSlice, type ProfileStore } from './slices/profile.slice'
import { createStore } from './store.utils'

// Root store type
export type RootStore = ProfileStore & OrganizationsStore

// Create the root store
export const useStore = createStore<RootStore>(
  (...params) => ({
    ...createProfileSlice(...params),
    ...createOrganizationsSlice(...params),
  }),
  {
    name: 'Foundation-Store',
    devtools: true,
    persist: true,
  },
)

// Export hooks for specific slices
export const useGetProfile = () => useStore(state => state.profile)
export const useGetUser = () => useStore(state => state.user)
export const useGetLoading = () => useStore(state => state.isLoading)
export const useGetError = () => useStore(state => state.error)
export const useGetCurrentOrganizationId = () => useStore(state => state.organizationId)

export const useIsAuthenticated = () =>
  useStore(state => state.user !== null && state.profile !== null)

export const useSetUser = () => useStore(state => state.setUser)
export const useSetProfile = () => useStore(state => state.setProfile)
export const useSetCurrentOrganizationId = () => useStore(state => state.setOrganizationId)
