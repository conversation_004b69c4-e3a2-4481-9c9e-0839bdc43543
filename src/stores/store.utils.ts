import { create, type StateCreator } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
import { createJSONStorage, persist } from 'zustand/middleware'

import type { StoreMiddlewares } from '.'

// Helper to create store with middlewares
export function createStore<T>(
  initializer: StateCreator<T, [], [], T>,
  options: StoreMiddlewares & { name: string } = {
    name: 'store',
    persist: false,
    devtools: true,
    subscribeWithSelector: true,
  },
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let store: any = initializer

  // Add subscribeWithSelector middleware if enabled
  if (options.subscribeWithSelector) {
    store = subscribeWithSelector(store) as StateCreator<T, [], [], T>
  }

  // Add devtools middleware if enabled
  if (options.devtools) {
    store = devtools(store) as StateCreator<T, [], [['zustand/devtools', never]], T>
  }

  // Add persist middleware if enabled
  if (options.persist) {
    store = persist(store, {
      name: options.name,
      storage: createJSONStorage(() => localStorage),
    })
  }

  return create<T>(store)
}
