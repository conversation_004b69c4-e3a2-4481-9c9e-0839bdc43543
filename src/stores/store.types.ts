// Base state interface that all slices should extend
export interface BaseState {
  version: number
}

// Base actions interface that all slices should extend
export interface BaseActions<T extends BaseState> {
  reset: () => void
  setState: (state: Partial<T>) => void
}

// Combine state and actions
export type StateWithActions<State extends BaseState, Actions extends BaseActions<State>> = State &
  Actions
