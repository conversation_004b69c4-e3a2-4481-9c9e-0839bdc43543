import type { User } from '@supabase/supabase-js'
import type { StateCreator } from 'zustand'

import type { BaseActions, BaseState, StateWithActions } from '../store.types'

// Profile state
export interface ProfileState extends BaseState {
  user: User | null
  profile: App.Profile | null
  isLoading: boolean
  error: Error | null
}

// Profile actions
export interface ProfileActions extends BaseActions<ProfileState> {
  setUser: (user: User | null) => void
  setProfile: (profile: ProfileState['profile']) => void
  setLoading: (isLoading: boolean) => void
  setError: (error: Error | null) => void
}

// Combine state and actions
export type ProfileStore = StateWithActions<ProfileState, ProfileActions>

// Initial state
const initialState: ProfileState = {
  version: 1,
  user: null,
  profile: null,
  isLoading: false,
  error: null,
}

// Create the store slice
export const createProfileSlice: StateCreator<
  ProfileStore,
  [['zustand/devtools', never]],
  [],
  ProfileStore
> = set => ({
  ...initialState,

  // Actions
  setUser: user => set({ user }, false, 'profile/setUser'),
  setProfile: profile => set({ profile }, false, 'profile/setProfile'),
  setLoading: isLoading => set({ isLoading }, false, 'profile/setLoading'),
  setError: error => set({ error }, false, 'profile/setError'),
  reset: () => set(initialState, false, 'profile/reset'),
  setState: state => set(state, false, 'profile/setState'),
})
