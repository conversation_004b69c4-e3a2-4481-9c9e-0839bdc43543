import type { StateCreator } from 'zustand'

import type { BaseActions, BaseState, StateWithActions } from '../store.types'

// Organizations state
export interface OrganizationsState extends BaseState {
  organizationId: string | null
}

// Organizations actions
export interface OrganizationsActions extends BaseActions<OrganizationsState> {
  setOrganizationId: (id: string | null) => void
}

// Combine state and actions
export type OrganizationsStore = StateWithActions<OrganizationsState, OrganizationsActions>

// Initial state
const initialState: OrganizationsState = {
  version: 1,
  organizationId: null,
}

// Create the store slice
export const createOrganizationsSlice: StateCreator<
  OrganizationsStore,
  [['zustand/devtools', never]],
  [],
  OrganizationsStore
> = set => ({
  ...initialState,
  // Actions
  setOrganizationId: organizationId =>
    set({ organizationId }, false, 'organizations/setOrganizationId'),
  reset: () => set(initialState, false, 'organizations/reset'),
  setState: state => set(state, false, 'organizations/setState'),
})
