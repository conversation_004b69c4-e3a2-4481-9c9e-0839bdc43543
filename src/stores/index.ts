import { type StateCreator } from 'zustand'

// Define middleware types
export interface StoreMiddlewares {
  persist?: boolean
  devtools?: boolean
  subscribeWithSelector?: boolean
}

// Helper type for creating store slices with middleware
export type StoreSliceCreator<T extends object> = StateCreator<
  T,
  [
    ['zustand/devtools', never],
    ['zustand/persist', unknown],
    ['zustand/subscribeWithSelector', never],
  ]
>
