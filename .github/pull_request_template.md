## Description

_(Briefly describe the changes in this PR)_

Fixes #<issue_number> / Relates to TICKET-XYZ

## Motivation and Context

_(Explain why these changes are necessary. What problem is being solved? What feature is being added?)_

## How Has This Been Implemented?

_(Provide a brief technical overview if necessary. Highlight key decisions, new libraries, or complex logic.)_

- Updated the `UserProfile` component to fetch data via `useSWR`.
- Added new Supabase RLS policy for organization members.
- Refactored `auth-service.ts` to simplify token handling.

## How Has This Been Tested?

_(Describe the manual testing steps performed. Mention relevant automated tests.)_

- [x] Manual login/logout flow tested successfully.
- [ ] Added unit tests for `auth-service`.
- [x] Verified profile updates correctly in the UI.

## Screenshots (if applicable)

_(Add screenshots or GIFs for UI changes)_
