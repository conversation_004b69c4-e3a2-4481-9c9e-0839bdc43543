create type public.user_status as enum (
  'active',
  'inactive',
  'deleted'
);

create table public.profiles (
  id uuid not null references auth.users on delete cascade,
  name text,
  email text,
  avatar character varying null,
  provider text null,
  phone text null,
  status user_status not null default 'active',
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone null,
  deleted_at timestamp with time zone null,
  primary key (id)
);

-- Enable RLS
alter table public.profiles enable row level security;

-- Create RLS policies
create policy "Public users are viewable by everyone."
  on profiles for select
  using (true);

create policy "Users can insert their own profile."
  on profiles for insert
  with check (auth.uid() = id);

create policy "Users can update own profile."
  on profiles for update
  using (auth.uid() = id);

create function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = ''
as $$
begin
  insert into public.profiles (id, email, name, provider, avatar)
  values (new.id, new.email, new.raw_user_meta_data ->> 'full_name', new.raw_app_meta_data ->> 'provider', new.raw_user_meta_data ->> 'avatar_url');
  return new;
end;
$$;

-- trigger the function every time a user is created
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

create type public.organization_type as enum (
  'personal',
  'education',
  'startup',
  'agency',
  'company'
);

create type public.company_size as enum (
  '1-10',
  '10-49',
  '50-99',
  '100-299',
  '300+'
);

-- Organizations table
create table public.organizations (
  id uuid not null default gen_random_uuid(),
  name text not null,
  logo_url text null,
  type organization_type not null default 'personal',
  use_case text null,
  how_did_you_hear text null,
  company_size company_size null,
  paragon_credentials JSONB null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone null,
  deleted_at timestamp with time zone null,
  constraint organizations_pkey primary key (id),
  -- Add constraint to ensure company_size is required for certain org types
  constraint organization_size_check check (
    (type in ('personal', 'education') AND company_size is null) OR
    (type in ('startup', 'agency', 'company') AND company_size is not null)
  )
) TABLESPACE pg_default;

create type public.organization_role as enum (
  'owner',
  'admin',
  'member',
  'guest'
);

create type public.organization_status as enum (
  'active',
  'inactive'
);

-- Organization members table
create table public.organization_members (
  id uuid not null default gen_random_uuid(),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone null,
  deleted_at timestamp with time zone null,
  organization_id uuid not null,
  user_id uuid not null,
  role organization_role not null default 'member',
  status organization_status not null default 'active',
  constraint organization_members_pkey primary key (id),
  constraint organization_members_organization_id_fkey foreign key (organization_id) references public.organizations (id) on delete cascade,
  constraint organization_members_user_id_fkey foreign key (user_id) references public.profiles (id) on delete cascade,
  constraint organization_members_unique unique (organization_id, user_id)
) TABLESPACE pg_default;

create index idx_org_members_org_id on public.organization_members (organization_id);
create index idx_org_members_user_id on public.organization_members (user_id);

create type public.organization_invite_status as enum ('pending', 'accepted', 'declined', 'expired');

-- Create organization invites table
create table public.organization_invites (
    id uuid not null default gen_random_uuid(),
    organization_id uuid not null,
    email text not null,
    role organization_role not null default 'member',
    status organization_invite_status not null default 'pending',
    invited_by uuid not null,
    expires_at timestamp with time zone not null default (now() + interval '7 days'),
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null,
    deleted_at timestamp with time zone null,
    constraint organization_invites_pkey primary key (id),
    constraint organization_invites_organization_id_fkey foreign key (organization_id) references public.organizations (id) on delete cascade,
    constraint organization_invites_invited_by_fkey foreign key (invited_by) references public.profiles (id) on delete cascade
);

-- Create the partial unique index after table creation
CREATE UNIQUE INDEX organization_invites_unique_pending
ON public.organization_invites (organization_id, email)
WHERE status = 'pending';

-- Create other useful indexes
create index idx_org_invites_org_id on public.organization_invites (organization_id);
create index idx_org_invites_email on public.organization_invites (email);
create index idx_org_invites_status on public.organization_invites (status);

-- Enum for phone provider types
create type public.phone_provider as enum (
  'twilio',
  'sip_trunk'
);

-- Phones table
create table public.phones (
  id uuid not null default gen_random_uuid(),
  eleven_labs_phone_id text not null,
  organization_id uuid not null references public.organizations(id) on delete cascade,
  user_id uuid references public.profiles(id) on delete set null,  -- Added user_id field
  phone_number text not null,
  label text not null,
  sid text,                    -- Twilio SID (only used when provider is 'twilio')
  token text,                  -- Twilio token (only used when provider is 'twilio')
  provider phone_provider not null,
  termination_uri text,        -- Used for SIP trunk configuration
  credentials jsonb default '{"username": null, "password": null}'::jsonb,  -- Used for SIP trunk configuration
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone,
  primary key (id),
  constraint unique_phone_number unique (phone_number)
);

-- Enable RLS
alter table public.phones enable row level security;

-- RLS Policies
create policy "Users can view phones in their organizations"
  on phones for select
  using (
    organization_id in (
      select organization_id
      from organization_members
      where user_id = auth.uid()
    )
  );

create policy "Users can insert phones in their organizations"
  on phones for insert
  with check (
    organization_id in (
      select organization_id
      from organization_members
      where user_id = auth.uid()
      and role in ('owner', 'admin')
    )
  );

create policy "Users can update phones in their organizations"
  on phones for update
  using (
    organization_id in (
      select organization_id
      from organization_members
      where user_id = auth.uid()
      and role in ('owner', 'admin')
    )
  );

-- Indexes for better query performance
create index idx_phones_organization on phones(organization_id);
create index idx_phones_user on phones(user_id);  -- Added index for user_id

-- Enable required extensions
create extension if not exists pg_cron with schema extensions;
create extension if not exists pg_net with schema extensions;

-- 1. Create agents table
CREATE TABLE public.agents (
    agent_id TEXT PRIMARY KEY, -- ID from ElevenLabs
    agent_name TEXT NOT NULL,
    first_message_prompt TEXT,
    system_prompt TEXT,
    language TEXT DEFAULT 'en',
    llm TEXT DEFAULT 'gemini-2.0-flash-001',
    temperature FLOAT DEFAULT 0,
    max_tokens INTEGER DEFAULT -1,
    voice_id TEXT,
    agent_output_audio_format TEXT,
    model_id TEXT DEFAULT 'eleven_turbo_v2',
    optimize_streaming_latency INTEGER DEFAULT 3,
    similarity_boost FLOAT DEFAULT 0.8,
    speed FLOAT DEFAULT 1,
    stability FLOAT DEFAULT 0.5,
    evaluation_criteria JSONB,
    data_collection_points JSONB,
    turn_timeout INTEGER,
    max_duration_seconds INTEGER,
    rag_enabled BOOLEAN DEFAULT FALSE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    last_updated TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for agents table
CREATE INDEX idx_agents_organization_id ON public.agents(organization_id);
CREATE INDEX idx_agents_user_id ON public.agents(user_id);

-- Enable RLS for agents table
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agents table
CREATE POLICY "Users can view agents in their organizations"
  ON public.agents FOR SELECT
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert agents in their organizations"
  ON public.agents FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Users can update agents in their organizations"
  ON public.agents FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );

-- 2. Create conversations table (partitioned)
CREATE TABLE public.conversations (
    conversation_id TEXT NOT NULL,
    agent_id TEXT NOT NULL, -- References public.agents(agent_id)
    status TEXT,
    transcript JSONB,
    analysis JSONB,
    metadata JSONB,
    conversation_initiation_client_data JSONB,
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL,
    last_synced TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT conversations_pkey PRIMARY KEY (conversation_id, created_at)
) PARTITION BY RANGE (created_at);

-- Add indexes for conversations table
CREATE INDEX idx_conversations_agent_id ON public.conversations(agent_id);
CREATE INDEX idx_conversations_status ON public.conversations(status);
CREATE INDEX idx_conversations_created_at_idx ON public.conversations(created_at);

-- Enable RLS for conversations table
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversations table
CREATE POLICY "Users can view conversations in their organizations"
  ON public.conversations FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
        )
    )
  );

CREATE POLICY "Users can insert conversations in their organizations"
  ON public.conversations FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );

CREATE POLICY "Users can update conversations in their organizations"
  ON public.conversations FOR UPDATE
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );

-- 3. Create initial partition for conversations (adjust month/year as needed)
-- Assuming current date is April 2025, create partition for May 2025
CREATE TABLE public.conversations_y2025m05 PARTITION OF public.conversations
    FOR VALUES FROM ('2025-05-01 00:00:00+00') TO ('2025-06-01 00:00:00+00');

-- 4. Function to create monthly partitions automatically
CREATE OR REPLACE FUNCTION public.create_monthly_conversation_partition(p_date date)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    partition_name TEXT;
    start_of_month TIMESTAMPTZ;
    end_of_month TIMESTAMPTZ;
BEGIN
    start_of_month := date_trunc('month', p_date)::timestamptz;
    end_of_month := (start_of_month + interval '1 month')::timestamptz;
    partition_name := 'conversations_y' || to_char(start_of_month, 'YYYY') || 'm' || to_char(start_of_month, 'MM');

    -- Check if partition already exists
    IF NOT EXISTS (
        SELECT 1
        FROM   pg_catalog.pg_class c
        JOIN   pg_catalog.pg_namespace n ON n.oid = c.relnamespace
        WHERE  n.nspname = 'public'
        AND    c.relname = partition_name
        AND    c.relkind = 'r' -- 'r' for relation (table)
    ) THEN
        -- Create the partition
        EXECUTE format(
            'CREATE TABLE public.%I PARTITION OF public.conversations FOR VALUES FROM (%L) TO (%L);',
            partition_name,
            start_of_month,
            end_of_month
        );
        RAISE NOTICE 'Created partition %', partition_name;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
    END IF;
END;
$$;

-- 5. Schedule monthly partition creation job with pg_cron
-- Runs at 00:00 on the 1st day of every month
-- Creates partition for the *next* month
SELECT cron.schedule(
    'create-monthly-conversation-partition',
    '0 0 1 * *', -- At 00:00 on day-of-month 1
    $$ SELECT public.create_monthly_conversation_partition(CURRENT_DATE + interval '1 month'); $$
);

ALTER TABLE public.agents
ADD COLUMN tools TEXT[];

-- Add is_template column to agents table
ALTER TABLE public.agents
ADD COLUMN is_template BOOLEAN NOT NULL DEFAULT FALSE;

-- Optional: Add an index if querying templates frequently
-- CREATE INDEX idx_agents_is_template ON public.agents(is_template) WHERE is_template = TRUE;

-- Ensure pgcrypto extension is enabled for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;

-- Create ENUMs for better data consistency
CREATE TYPE public.crm_type AS ENUM ('hubspot', 'salesforce', 'paragon');
CREATE TYPE public.crm_entity_type AS ENUM ('contact', 'deal', 'company', 'call_log');
CREATE TYPE public.crm_change_type AS ENUM ('created', 'updated', 'deleted');
CREATE TYPE public.observation_status AS ENUM ('pending', 'processing', 'completed', 'failed');

-- Stores connection details and credentials for external CRMs
CREATE TABLE public.crm_connections (
    id UUID PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    integration_id UUID NOT NULL,
    credential_id UUID NOT NULL,
    crm_type crm_type NOT NULL,
    credentials JSONB NOT NULL,
    crm_owner_id TEXT,
    crm_owner_email TEXT,
    crm_instance_identifier TEXT,
    schema_definition JSONB,
    is_synced_contacts BOOLEAN DEFAULT FALSE,
    is_synced_deals BOOLEAN DEFAULT FALSE,
    is_synced_companies BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add this before any triggers that use set_updated_at
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.crm_connections
    FOR EACH ROW
    EXECUTE FUNCTION public.set_updated_at();

-- The Observation Log table: Core of the sync engine
CREATE TABLE public.observations (
    id BIGSERIAL PRIMARY KEY,
    crm_connection_id UUID REFERENCES public.crm_connections(id) NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT now(),
    source TEXT NOT NULL,
    entity_type crm_entity_type NOT NULL,
    entity_id TEXT NOT NULL,
    change_type crm_change_type NOT NULL,
    state_snapshot JSONB NOT NULL,
    -- Add composite index for efficient querying
    CONSTRAINT unique_observation UNIQUE (crm_connection_id, entity_type, entity_id, timestamp)
);

-- Indexes for observations
CREATE INDEX idx_observations_crm_connection_entity ON public.observations(crm_connection_id, entity_type, entity_id);
CREATE INDEX idx_observations_timestamp ON public.observations(timestamp DESC);

-- Processing status tracking
CREATE TABLE public.observation_processing_status (
    id BIGSERIAL PRIMARY KEY,
    observation_id BIGINT NOT NULL REFERENCES public.observations(id) ON DELETE CASCADE,
    worker_id TEXT,
    status observation_status NOT NULL DEFAULT 'pending',
    started_at TIMESTAMPTZ,
    finished_at TIMESTAMPTZ,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (observation_id)
);

-- Create trigger for updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.observation_processing_status
    FOR EACH ROW
    EXECUTE FUNCTION public.set_updated_at();

-- Indexes for observation processing
CREATE INDEX idx_observation_processing_status_pending ON public.observation_processing_status(status) WHERE status = 'pending';
CREATE INDEX idx_observation_processing_status_observation_id ON public.observation_processing_status(observation_id);

-- Cache table for contacts
CREATE TABLE public.cached_crm_contacts (
    id UUID PRIMARY KEY DEFAULT extensions.gen_random_uuid(),
    crm_connection_id UUID REFERENCES public.crm_connections(id) NOT NULL,
    crm_entity_id TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    email TEXT,
    phone TEXT,
    job_title TEXT,
    hubspot_owner_id TEXT,
    properties JSONB,
    last_synced_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (crm_connection_id, crm_entity_id)
);

-- Create trigger for updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.cached_crm_contacts
    FOR EACH ROW
    EXECUTE FUNCTION public.set_updated_at();

-- Indexes for cached contacts
CREATE INDEX idx_cached_crm_contacts_crm_connection ON public.cached_crm_contacts(crm_connection_id);
CREATE INDEX idx_cached_crm_contacts_email ON public.cached_crm_contacts(email);
CREATE INDEX idx_cached_crm_contacts_phone ON public.cached_crm_contacts(phone);
CREATE INDEX idx_cached_crm_contacts_last_synced ON public.cached_crm_contacts(last_synced_at DESC);

-- Enable RLS
ALTER TABLE public.crm_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.observation_processing_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cached_crm_contacts ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- CRM Connections
CREATE POLICY "Users can view CRM connections in their organizations"
    ON public.crm_connections FOR SELECT
    USING (
        organization_id IN (
            SELECT organization_id
            FROM public.organization_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage CRM connections in their organizations"
    ON public.crm_connections FOR ALL
    USING (
        organization_id IN (
            SELECT organization_id
            FROM public.organization_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    );

-- Observations
CREATE POLICY "Users can view observations for their organizations' CRM connections"
    ON public.observations FOR SELECT
    USING (
        crm_connection_id IN (
            SELECT id FROM public.crm_connections
            WHERE organization_id IN (
                SELECT organization_id
                FROM public.organization_members
                WHERE user_id = auth.uid()
            )
        )
    );

-- Observation Processing Status
CREATE POLICY "Users can view processing status for their organizations' observations"
    ON public.observation_processing_status FOR SELECT
    USING (
        observation_id IN (
            SELECT id FROM public.observations
            WHERE crm_connection_id IN (
                SELECT id FROM public.crm_connections
                WHERE organization_id IN (
                    SELECT organization_id
                    FROM public.organization_members
                    WHERE user_id = auth.uid()
                )
            )
        )
    );

-- Cached CRM Contacts
CREATE POLICY "Users can view cached contacts in their organizations"
    ON public.cached_crm_contacts FOR SELECT
    USING (
        crm_connection_id IN (
            SELECT id FROM public.crm_connections
            WHERE organization_id IN (
                SELECT organization_id
                FROM public.organization_members
                WHERE user_id = auth.uid()
            )
        )
    );
-- Industries table
create table public.company_industries (
  id uuid not null default gen_random_uuid(),
  name text not null,
  slug text not null,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  constraint company_industries_pkey primary key (id),
  constraint company_industries_slug_unique unique (slug)
);

-- Create index for industry slugs
create index idx_company_industries_slug on public.company_industries(slug);

-- Modify the companies table to use the industries table
create table public.cached_crm_companies (
  id uuid not null default gen_random_uuid(),
  crm_connection_id uuid not null references public.crm_connections(id) on delete cascade,
  crm_entity_id text not null,
  name text not null,
  domain text,
  industry text,
  type text,
  description text,
  linkedin_url text,
  annual_revenue numeric,
  employee_count integer,
  phone text,
  street_address text,
  city text,
  state text,
  postal_code text,
  country text,
  timezone text,
  hubspot_owner_id text,
  properties jsonb default '{}',
  last_synced_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  constraint cached_crm_companies_pkey primary key (id),
  constraint cached_crm_companies_connection_entity unique (crm_connection_id, crm_entity_id)
);

-- Enable RLS on industries table
alter table public.company_industries enable row level security;

-- Allow read access to industries for all authenticated users
create policy "Industries are viewable by everyone."
  on public.company_industries for select
  using (true);

  -- Enable RLS for cached_crm_companies
ALTER TABLE public.cached_crm_companies ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing companies
CREATE POLICY "Users can view cached companies in their organizations"
  ON public.cached_crm_companies FOR SELECT
  USING (
    crm_connection_id IN (
      SELECT id 
      FROM public.crm_connections
      WHERE organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

-- Create policy for inserting/updating companies
CREATE POLICY "Users can insert/update cached companies in their organizations"
  ON public.cached_crm_companies FOR ALL
  USING (
    crm_connection_id IN (
      SELECT id 
      FROM public.crm_connections
      WHERE organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

-- Add indexes if not already present
CREATE INDEX IF NOT EXISTS idx_cached_crm_companies_crm_connection 
  ON public.cached_crm_companies(crm_connection_id);

CREATE INDEX IF NOT EXISTS idx_cached_crm_companies_last_synced 
  ON public.cached_crm_companies(last_synced_at DESC);

-- Create enum for knowledge base types
CREATE TYPE public.knowledge_base_type AS ENUM ('file', 'url', 'text');

-- Knowledge base table
CREATE TABLE public.knowledge_bases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  eleven_labs_id TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  content TEXT,
  url TEXT,
  prompt_injectable BOOLEAN NOT NULL DEFAULT false,
  type knowledge_base_type NOT NULL,
  metadata JSONB DEFAULT '{}'::jsonb,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  deleted_at TIMESTAMPTZ
);

-- Create indexes for knowledge_bases
CREATE INDEX idx_knowledge_bases_organization_id ON public.knowledge_bases(organization_id);
CREATE INDEX idx_knowledge_bases_user_id ON public.knowledge_bases(user_id);
CREATE INDEX idx_knowledge_bases_type ON public.knowledge_bases(type);

-- Create trigger for updated_at
CREATE TRIGGER set_knowledge_bases_updated_at
  BEFORE UPDATE ON public.knowledge_bases
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

-- Enable RLS
ALTER TABLE public.knowledge_bases ENABLE ROW LEVEL SECURITY;

-- RLS Policies for knowledge_bases
CREATE POLICY "Users can view knowledge bases in their organizations"
  ON public.knowledge_bases FOR SELECT
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert knowledge bases in their organizations"
  ON public.knowledge_bases FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update knowledge bases in their organizations"
  ON public.knowledge_bases FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete knowledge bases in their organizations"
  ON public.knowledge_bases FOR DELETE
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

-- Junction table to connect agents with knowledge bases
CREATE TABLE public.agent_knowledge_bases (
  agent_id TEXT NOT NULL REFERENCES public.agents(agent_id) ON DELETE CASCADE,
  knowledge_base_id UUID NOT NULL REFERENCES public.knowledge_bases(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  primary key (agent_id, knowledge_base_id)
);

-- Create indexes for agent_knowledge_bases
CREATE INDEX idx_agent_knowledge_bases_agent_id ON public.agent_knowledge_bases(agent_id);
CREATE INDEX idx_agent_knowledge_bases_knowledge_base_id ON public.agent_knowledge_bases(knowledge_base_id);

-- Enable RLS
ALTER TABLE public.agent_knowledge_bases ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_knowledge_bases
CREATE POLICY "Users can view agent knowledge bases in their organizations"
  ON public.agent_knowledge_bases FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.agent_knowledge_bases.agent_id
      AND public.agents.organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage agent knowledge bases in their organizations"
  ON public.agent_knowledge_bases FOR ALL
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.agent_knowledge_bases.agent_id
      AND public.agents.organization_id IN (
        SELECT organization_id
        FROM public.organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE TABLE public.cached_crm_deals (
  id uuid not null default gen_random_uuid(),
  crm_connection_id uuid not null references public.crm_connections(id) on delete cascade,
  crm_entity_id text not null,
  name text not null,
  amount numeric,
  pipeline text not null default 'default',
  stage text not null,
  close_date timestamptz,
  priority text,
  type text,
  hubspot_owner_id text,
  properties jsonb default '{}'::jsonb,
  last_synced_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  constraint cached_crm_deals_pkey primary key (id),
  constraint cached_crm_deals_connection_entity unique (crm_connection_id, crm_entity_id)
);

-- Create trigger for updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.cached_crm_deals
    FOR EACH ROW
    EXECUTE FUNCTION public.set_updated_at();

-- Indexes for cached deals
CREATE INDEX idx_cached_crm_deals_crm_connection ON public.cached_crm_deals(crm_connection_id);
CREATE INDEX idx_cached_crm_deals_pipeline_stage ON public.cached_crm_deals(pipeline, stage);
CREATE INDEX idx_cached_crm_deals_close_date ON public.cached_crm_deals(close_date);
CREATE INDEX idx_cached_crm_deals_last_synced ON public.cached_crm_deals(last_synced_at DESC);

-- Enable RLS
ALTER TABLE public.cached_crm_deals ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing deals
CREATE POLICY "Users can view cached deals in their organizations"
  ON public.cached_crm_deals FOR SELECT
  USING (
    crm_connection_id IN (
      SELECT id 
      FROM public.crm_connections
      WHERE organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

-- Create policy for inserting/updating deals
CREATE POLICY "Users can insert/update cached deals in their organizations"
  ON public.cached_crm_deals FOR ALL
  USING (
    crm_connection_id IN (
      SELECT id 
      FROM public.crm_connections
      WHERE organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

create function full_query(cached_crm_contacts) returns text as $$
  select $1.first_name || ' ' || $1.last_name || ' ' || $1.email || ' ' || $1.phone;
$$ language sql immutable;

-- Conversation threads table - one per user-organization pair
CREATE TABLE public.conversation_threads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  title TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure one thread per user-organization pair
  CONSTRAINT unique_user_org_thread UNIQUE (user_id, organization_id)
);

-- Message types enum
CREATE TYPE public.message_source AS ENUM ('ai', 'user');

-- Thread messages table
CREATE TABLE public.thread_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  thread_id UUID NOT NULL REFERENCES public.conversation_threads(id) ON DELETE CASCADE,
  source message_source NOT NULL,
  type TEXT default 'text',
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  local_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  elevenlabs_conversation_id TEXT
);

-- Indexes for conversation_threads
CREATE INDEX idx_conversation_threads_user_id ON public.conversation_threads(user_id);
CREATE INDEX idx_conversation_threads_organization_id ON public.conversation_threads(organization_id);
CREATE INDEX idx_conversation_threads_updated_at ON public.conversation_threads(updated_at DESC);

-- Indexes for thread_messages
CREATE INDEX idx_thread_messages_thread_id ON public.thread_messages(thread_id);
CREATE INDEX idx_thread_messages_created_at ON public.thread_messages(created_at DESC);
CREATE INDEX idx_thread_messages_local_timestamp ON public.thread_messages(local_timestamp DESC);
CREATE INDEX idx_thread_messages_thread_local_order ON public.thread_messages(thread_id, local_timestamp ASC);

-- Enable RLS
ALTER TABLE public.conversation_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.thread_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversation_threads
CREATE POLICY "Users can view their own threads in their organizations"
  ON public.conversation_threads FOR SELECT
  USING (
    user_id = auth.uid() AND
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create threads in their organizations"
  ON public.conversation_threads FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own threads"
  ON public.conversation_threads FOR UPDATE
  USING (user_id = auth.uid());

-- RLS Policies for thread_messages
CREATE POLICY "Users can view messages in their threads"
  ON public.thread_messages FOR SELECT
  USING (
    thread_id IN (
      SELECT id
      FROM public.conversation_threads
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert messages in their threads"
  ON public.thread_messages FOR INSERT
  WITH CHECK (
    thread_id IN (
      SELECT id
      FROM public.conversation_threads
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update messages in their threads"
  ON public.thread_messages FOR UPDATE
  USING (
    thread_id IN (
      SELECT id
      FROM public.conversation_threads
      WHERE user_id = auth.uid()
    )
  );

-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA extensions;

-- Simple user documents table for RAG
CREATE TABLE public.user_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  embedding vector(1536),
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Index for vector search
CREATE INDEX idx_user_documents_embedding ON public.user_documents 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Index for user/org queries
CREATE INDEX idx_user_documents_user_org ON public.user_documents(user_id, organization_id);

-- Enable RLS
ALTER TABLE public.user_documents ENABLE ROW LEVEL SECURITY;

-- Users can view their documents in their organizations
CREATE POLICY "Users can view documents in their organizations"
  ON public.user_documents FOR SELECT
  USING (
    user_id = auth.uid() AND
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

-- Users can insert documents
CREATE POLICY "Users can insert documents"
  ON public.user_documents FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

-- Simple function to find similar documents
CREATE OR REPLACE FUNCTION match_user_documents (
  query_embedding vector(1536),
  target_user_id UUID,
  target_organization_id UUID,
  match_threshold float DEFAULT 0.8,
  match_count int DEFAULT 5
)
RETURNS TABLE (
  id UUID,
  title text,
  content text,
  similarity float
)
LANGUAGE sql STABLE
AS $$
  SELECT
    user_documents.id,
    user_documents.title,
    user_documents.content,
    1 - (user_documents.embedding <=> query_embedding) as similarity
  FROM user_documents
  WHERE 
    user_documents.user_id = target_user_id
    AND user_documents.organization_id = target_organization_id
    AND user_documents.embedding IS NOT NULL
    AND 1 - (user_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY (user_documents.embedding <=> query_embedding) ASC
  LIMIT match_count;
$$;


-- Create policy for inserting/updating contacts
CREATE POLICY "Users can insert/update cached contacts in their organizations"
  ON public.cached_crm_contacts FOR ALL
  USING (
    crm_connection_id IN (
      SELECT id 
      FROM public.crm_connections
      WHERE organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
      )
    )
  );

-- Function to cleanup CRM data before connection deletion
CREATE OR REPLACE FUNCTION public.cleanup_crm_connection_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Delete cached contacts
    DELETE FROM public.cached_crm_contacts 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete cached companies
    DELETE FROM public.cached_crm_companies 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete cached deals
    DELETE FROM public.cached_crm_deals 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete observation processing status first (due to foreign key)
    DELETE FROM public.observation_processing_status 
    WHERE observation_id IN (
        SELECT id FROM public.observations 
        WHERE crm_connection_id = OLD.id
    );
    
    -- Delete observations
    DELETE FROM public.observations 
    WHERE crm_connection_id = OLD.id;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to cleanup data before CRM connection deletion
CREATE TRIGGER cleanup_crm_connection_data_trigger
    BEFORE DELETE ON public.crm_connections
    FOR EACH ROW
    EXECUTE FUNCTION public.cleanup_crm_connection_data();
  
