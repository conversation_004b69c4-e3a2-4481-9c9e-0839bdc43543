<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Your Password</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .button {
        display: inline-block;
        padding: 12px 24px;
        background-color: #2563eb;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        margin: 16px 0;
        font-weight: 500;
      }
      .button:hover {
        background-color: #1d4ed8;
      }
      .footer {
        margin-top: 24px;
        font-size: 14px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Reset Your Password</h1>
      <p>Hello,</p>
      <p>
        We received a request to reset your password. Click the button below to choose a new
        password:
      </p>

      <a href="{{ .ConfirmationURL }}" class="button">Reset Password</a>

      <p>If you didn't request a password reset, you can safely ignore this email.</p>

      <div class="footer">
        <p>This link will expire in 24 hours.</p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all">{{ .ConfirmationURL }}</p>
      </div>
    </div>
  </body>
</html>
