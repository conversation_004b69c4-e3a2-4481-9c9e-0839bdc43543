// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { Hono, Context } from 'jsr:@hono/hono'
import { google } from 'googleapis'
import { corsHeaders } from '../_shared/cors.ts'
import { verifyAuth, supabase } from '../_shared/utils.ts'

const functionName = 'tools'
const app = new Hono().basePath(`/${functionName}`)

// Helper function to get Google OAuth2 client with user's token
async function getGoogleAuth(user: any) {
  const { data: identity } = await supabase
    .from('identities')
    .select('provider_token')
    .eq('user_id', user.id)
    .eq('provider', 'google')
    .single()

  if (!identity?.provider_token) {
    throw new Error('Google provider token not found')
  }

  const oauth2Client = new google.auth.OAuth2()
  oauth2Client.setCredentials({
    access_token: identity.provider_token,
  })

  return oauth2Client
}

// === GOOGLE CALENDAR ENDPOINTS ===

// List calendar events
app.get('/calendar/events', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const calendar = google.calendar({ version: 'v3', auth })

    const timeMin = c.req.query('timeMin') || new Date().toISOString()
    const timeMax = c.req.query('timeMax')
    const maxResults = parseInt(c.req.query('maxResults') || '10')

    const response = await calendar.events.list({
      calendarId: 'primary',
      timeMin,
      timeMax,
      maxResults,
      singleEvents: true,
      orderBy: 'startTime',
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create calendar event
app.post('/calendar/events', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const calendar = google.calendar({ version: 'v3', auth })

    const eventData = await c.req.json()

    const response = await calendar.events.insert({
      calendarId: 'primary',
      requestBody: eventData,
    })

    return new Response(JSON.stringify(response.data), {
      status: 201,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Update calendar event
app.put('/calendar/events/:eventId', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const calendar = google.calendar({ version: 'v3', auth })

    const eventId = c.req.param('eventId')
    const eventData = await c.req.json()

    const response = await calendar.events.update({
      calendarId: 'primary',
      eventId,
      requestBody: eventData,
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Delete calendar event
app.delete('/calendar/events/:eventId', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const calendar = google.calendar({ version: 'v3', auth })

    const eventId = c.req.param('eventId')

    await calendar.events.delete({
      calendarId: 'primary',
      eventId,
    })

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// === GMAIL ENDPOINTS ===

// List emails
app.get('/gmail/messages', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const gmail = google.gmail({ version: 'v1', auth })

    const q = c.req.query('q') || ''
    const maxResults = parseInt(c.req.query('maxResults') || '10')
    const labelIds = c.req.query('labelIds')?.split(',')

    const response = await gmail.users.messages.list({
      userId: 'me',
      q,
      maxResults,
      labelIds,
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get specific email
app.get('/gmail/messages/:messageId', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const gmail = google.gmail({ version: 'v1', auth })

    const messageId = c.req.param('messageId')
    const format = c.req.query('format') || 'full'

    const response = await gmail.users.messages.get({
      userId: 'me',
      id: messageId,
      format: format as any,
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Send email
app.post('/gmail/send', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const gmail = google.gmail({ version: 'v1', auth })

    const { to, subject, body, cc, bcc } = await c.req.json()

    if (!to || !subject || !body) {
      return new Response(JSON.stringify({ error: 'to, subject, and body are required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Create email message
    const messageParts = [
      `To: ${to}`,
      cc ? `Cc: ${cc}` : '',
      bcc ? `Bcc: ${bcc}` : '',
      `Subject: ${subject}`,
      '',
      body,
    ]
      .filter(Boolean)
      .join('\n')

    const encodedMessage = btoa(messageParts)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '')

    const response = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedMessage,
      },
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Gmail labels
app.get('/gmail/labels', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const gmail = google.gmail({ version: 'v1', auth })

    const response = await gmail.users.labels.list({
      userId: 'me',
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Mark email as read/unread
app.post('/gmail/messages/:messageId/modify', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const auth = await getGoogleAuth(user)
    const gmail = google.gmail({ version: 'v1', auth })

    const messageId = c.req.param('messageId')
    const { addLabelIds, removeLabelIds } = await c.req.json()

    const response = await gmail.users.messages.modify({
      userId: 'me',
      id: messageId,
      requestBody: {
        addLabelIds,
        removeLabelIds,
      },
    })

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// OPTIONS handler for CORS
app.options('*', _ => {
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  })
})

Deno.serve(app.fetch)
