// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { Hono, Context } from 'jsr:@hono/hono'
import { createClient } from '@supabase/supabase-js'
import OpenAI from 'npm:openai'
import { corsHeaders } from '../_shared/cors.ts'
import { verifyAuth } from '../_shared/utils.ts'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
)

const openai = new OpenAI({
  apiKey: Deno.env.get('OPENAI_API_KEY')!,
})

// Generate embedding using OpenAI
async function generateEmbedding(text: string): Promise<number[]> {
  const embedding = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text.replace(/\n/g, ' '),
    encoding_format: 'float',
  })

  return embedding.data[0].embedding
}

const functionName = 'rag'
const app = new Hono().basePath(`/${functionName}`)

// Create document
app.post('/documents', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const { title, content, organization_id } = await c.req.json()

    if (!title || !content || !organization_id) {
      return new Response(
        JSON.stringify({ error: 'Title, content, and organization_id are required' }),
        {
          status: 400,
          headers: corsHeaders,
        },
      )
    }

    // Generate embedding using OpenAI
    const embedding = await generateEmbedding(content)

    // Store document
    const { data, error } = await supabase
      .from('user_documents')
      .insert({
        user_id: user.id,
        organization_id,
        title,
        content,
        embedding,
      })
      .select()
      .single()

    if (error) throw error

    return new Response(JSON.stringify(data), {
      status: 201,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ app.post ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Search documents
app.post('/search', async (c: Context) => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const { query, organization_id, match_threshold = 0.8, match_count = 5 } = await c.req.json()

    if (!query || !organization_id) {
      return new Response(JSON.stringify({ error: 'Query and organization_id are required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Generate embedding for query
    const queryEmbedding = await generateEmbedding(query)

    // Search similar documents
    const { data, error } = await supabase.rpc('match_user_documents', {
      query_embedding: queryEmbedding,
      target_user_id: user.id,
      target_organization_id: organization_id,
      match_threshold,
      match_count,
    })

    if (error) throw error

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ app.post ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

Deno.serve(app.fetch)
