// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { Hono, Context } from 'jsr:@hono/hono'
import { createClient } from '@supabase/supabase-js'
import { ElevenLabsClient } from 'npm:elevenlabs@1.52.0'
import { corsHeaders } from '../_shared/cors.ts'
import { getHubSportAuthorizationUrl, getHubSpotAccessToken } from '../_shared/hubspot.ts'
import { getParagonOrganizationToken, disconnectCRM } from '../_shared/paragon.ts'
const APP_URL = Deno.env.get('APP_URL') ?? 'http://localhost:3000'
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
)
const client = new ElevenLabsClient({
  apiKey: Deno.env.get('ELEVENLABS_API_KEY'),
})

// Add OPTIONS handler for CORS preflight requests

// You can set the basePath with Hono
const functionName = 'organizations'
const app = new Hono().basePath(`/${functionName}`)

const verifyAuth = async (c: Context) => {
  try {
    const authHeader = c.req.raw.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const {
      data: { user },
    } = await supabase.auth.getUser(token)

    return user
  } catch (_error) {
    return null
  }
}

app.get('/:id/phones', async c => {
  const organizationId = c.req.param('id')
  const user = await verifyAuth(c)
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: corsHeaders,
    })
  }

  const { data: phones, error } = await supabase
    .from('phones')
    .select('*')
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false })

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
  return new Response(JSON.stringify(phones), {
    status: 200,
    headers: corsHeaders,
  })
})

app.post('/:id/phones', async c => {
  const organization_id = c.req.param('id')
  const body = await c.req.json()
  const { phone_number, label, provider, sid, token, termination_uri, credentials } = body
  const user = await verifyAuth(c)
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: corsHeaders,
    })
  }
  if (!organization_id || !phone_number || !label || !provider) {
    return new Response(JSON.stringify({ error: 'Missing required fields' }), {
      status: 400,
      headers: corsHeaders,
    })
  }

  if (provider === 'twilio' && (!sid || !token)) {
    return new Response(JSON.stringify({ error: 'Missing required fields for Twilio' }), {
      status: 400,
      headers: corsHeaders,
    })
  }

  if (provider === 'sip_trunk' && (!termination_uri || !credentials)) {
    return new Response(JSON.stringify({ error: 'Missing required fields for SIP trunk' }), {
      status: 400,
      headers: corsHeaders,
    })
  }

  const payload = provider === 'twilio' ? { sid, token } : { termination_uri, credentials }
  const elevenLabsPhone = await client.conversationalAi.createPhoneNumber({
    label,
    phone_number,
    provider,
    ...payload,
    // deno-lint-ignore no-explicit-any
  } as any)
  console.log('🚀 ~ elevenLabsPhone:', elevenLabsPhone)
  const { data: phone, error } = await supabase
    .from('phones')
    .insert({
      eleven_labs_phone_id: elevenLabsPhone.phone_number_id,
      organization_id,
      phone_number,
      label,
      provider,
      user_id: user.id,
      ...payload,
    })
    .select()
    .single()

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    })
  }

  return new Response(JSON.stringify({ data: phone }), {
    status: 200,
    headers: corsHeaders,
  })
})

app.delete('/:id/phones/:phoneId', async c => {
  try {
    const id = c.req.param('id')
    console.log('🚀 ~ id:', id)
    const phoneId = c.req.param('phoneId')
    console.log('🚀 ~ phoneId:', phoneId)
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const { data: phone, error } = await supabase
      .from('phones')
      .select('*')
      .eq('id', phoneId)
      .single()
    if (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: corsHeaders,
      })
    }
    if (phone.organization_id !== id) {
      return new Response(JSON.stringify({ error: 'Phone not found' }), {
        status: 404,
        headers: corsHeaders,
      })
    }

    const { error: deleteError } = await supabase.from('phones').delete().eq('id', phoneId)
    if (deleteError) {
      return new Response(JSON.stringify({ error: deleteError.message }), {
        status: 500,
        headers: corsHeaders,
      })
    }

    await client.conversationalAi.deletePhoneNumber(phone.eleven_labs_phone_id)

    return new Response(JSON.stringify({ data: phone }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get(':id/crm/paragon-token', async c => {
  const id = c.req.param('id')
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const token = await getParagonOrganizationToken(id)

    return new Response(JSON.stringify({ token }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.post('/:id/crm/integrations', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const id = c.req.param('id')
    const { crm_type, integration_id, credential_id, credentials, provider_id } = await c.req.json()

    if (!crm_type || !integration_id || !credential_id || !provider_id) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Store in database
    const { data: connection, error } = await supabase
      .from('crm_connections')
      .insert({
        organization_id: id,
        user_id: user.id,
        crm_type,
        crm_instance_identifier: provider_id,
        integration_id,
        credential_id,
        credentials,
      })
      .select()
      .single()

    if (error) throw error

    return new Response(JSON.stringify({ data: connection }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    console.log('🚀 ~ error:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.post('/:id/crm/disconnect', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const id = c.req.param('id')
    const { crm_type, connection_id } = await c.req.json()

    if (!crm_type || !connection_id) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    await disconnectCRM(connection_id, crm_type)

    return new Response(JSON.stringify({ data: 'success' }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    console.log('🚀 ~ error:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/crm/hubspot', async c => {
  const redirectUri = c.req.query('redirect_to') ?? `${APP_URL}/organizations/hubspot/callback`
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const url = await getHubSportAuthorizationUrl(redirectUri)

    return new Response(JSON.stringify({ url }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.post('/:id/crm/hubspot', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }
    const id = c.req.param('id')
    console.log('🚀 ~ id:', id)
    const body = await c.req.json()
    const { code, redirect_to } = body
    if (!code) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: corsHeaders,
      })
    }
    const redirectUri = redirect_to ?? `${APP_URL}/organizations/hubspot/callback`
    const { tokens, details, ownerId, ownerEmail } = await getHubSpotAccessToken(code, redirectUri)
    // Calculate token expiration in UTC
    const now = new Date()
    const expiresAt = new Date(now.getTime() + tokens.expiresIn * 1000).toISOString()

    // Structure the credentials and portal details
    const credentials = {
      access_token: tokens.accessToken,
      refresh_token: tokens.refreshToken,
      token_type: tokens.tokenType,
      expires_in: tokens.expiresIn,
      expires_at: expiresAt, // This is now in ISO string format (UTC)
      portal: {
        id: details.portalId,
        account_type: details.accountType,
        timezone: details.timeZone,
        currency: details.companyCurrency,
        additional_currencies: details.additionalCurrencies,
        utc_offset: details.utcOffset,
        utc_offset_milliseconds: details.utcOffsetMilliseconds,
        ui_domain: details.uiDomain,
        data_hosting_location: details.dataHostingLocation,
      },
    }

    // Store in database
    const { data: connection, error } = await supabase
      .from('crm_connections')
      .insert({
        organization_id: id,
        user_id: user.id,
        crm_type: 'hubspot',
        crm_instance_identifier: details.portalId,
        credentials,
        crm_owner_id: ownerId,
        crm_owner_email: ownerEmail,
        user_id: user.id,
        integration_id: id,
        credential_id: id,
      })
      .select()
      .single()

    if (error) throw error

    return new Response(JSON.stringify({ data: connection }), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error) {
    console.log('🚀 ~ error:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/:id/crm/contacts', async c => {
  const id = c.req.param('id')
  const user = await verifyAuth(c)
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: corsHeaders,
    })
  }

  const { data: connections, error } = await supabase
    .from('crm_connections')
    .select('*')
    .eq('organization_id', id)
    .eq('crm_type', 'hubspot')

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    })
  }

  return new Response(JSON.stringify(connections), {
    status: 200,
    headers: corsHeaders,
  })
})

app.options('*', _ => {
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  })
})

// Add CORS headers middleware for all routes
app.use('*', async (c, next) => {
  // Handle preflight requests
  if (c.req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    })
  }

  // deno-lint-ignore no-explicit-any
  const response: any = await next()
  // Add CORS headers to all responses
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  return response
})

Deno.serve(app.fetch)
