import { createClient, User } from '@supabase/supabase-js'
import { Context } from 'jsr:@hono/hono'

export const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
)

export const verifyAuth = async (c: Context): Promise<User | null> => {
  try {
    const internalRequestHeader = c.req.raw.headers.get('X-Internal-Request')
    const authHeader = c.req.raw.headers.get('Authorization')
    const serviceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!authHeader) {
      return null
    }

    const token = authHeader.replace('Bearer ', '')

    // If it's an internal request, verify it's using the service key
    if (internalRequestHeader === 'true') {
      if (token === serviceKey) {
        // This is a trusted internal call.
        // We can return a dummy user object or a specific identifier.
        // The important part is that it's not null.
        return {
          id: 'service-role-user',
          aud: 'authenticated',
          role: 'service_role',
          email: '<EMAIL>',
          app_metadata: {},
          user_metadata: {},
          created_at: new Date().toISOString(),
        }
      } else {
        // Invalid token for an internal request
        return null
      }
    }

    // For external requests, verify the user's JWT
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token)

    if (error) {
      console.error('Auth error:', error.message)
      return null
    }

    return user
  } catch (_error) {
    return null
  }
}
