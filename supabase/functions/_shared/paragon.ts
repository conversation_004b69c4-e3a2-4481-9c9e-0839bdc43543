import jwt from 'jsonwebtoken'
import { supabase } from './utils.ts'

const PARAGON_API_URL = Deno.env.get('PARAGON_API_URL')
const paragonProjectID = Deno.env.get('PARAGON_PROJECT_ID')
const paragonSigningKey = Deno.env.get('PARAGON_SIGNING_KEY')
  ? atob(Deno.env.get('PARAGON_SIGNING_KEY')!)
  : undefined

export const getParagonOrganizationToken = async (organizationId: string) => {
  if (!paragonProjectID || !paragonSigningKey) {
    throw new Error('Paragon credentials not configured')
  }

  const { data: organization, error } = await supabase
    .from('organizations')
    .select('paragon_credentials')
    .eq('id', organizationId)
    .single()

  if (error) throw error

  // Check if token is expired
  const now = new Date()
  const expiresAt = new Date(organization.paragon_credentials?.expires_at)
  if (!!organization?.paragon_credentials && now < expiresAt) {
    return organization.paragon_credentials.token
  }

  const currentTime = Math.floor(Date.now() / 1000)
  const token = jwt.sign(
    {
      sub: organizationId,
      iat: currentTime,
      exp: currentTime + 60 * 60, // 1 hour from now
    },
    paragonSigningKey,
    {
      algorithm: 'RS256',
    },
  )

  const { error: updateError } = await supabase
    .from('organizations')
    .update({
      paragon_credentials: {
        token,
        expires_at: new Date(currentTime + 60 * 60 * 1000).toISOString(),
      },
    })
    .eq('id', organizationId)

  if (updateError) throw updateError

  return token
}

export const disconnectCRM = async (connectionId: string, type: string) => {
  try {
    if (!PARAGON_API_URL || !paragonProjectID) {
      throw new Error('Paragon API configuration missing')
    }

    const { data: connection, error } = await supabase
      .from('crm_connections')
      .select('*')
      .eq('id', connectionId)
      .single()
    if (error) throw error

    const token = await getParagonOrganizationToken(connection.organization_id)

    // Call Paragon API to uninstall integration using fetch
    const response = await fetch(
      `${PARAGON_API_URL}/projects/${paragonProjectID}/sdk/integrations/${connection.integration_id}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
      throw new Error(`Paragon API error: ${errorData.message || response.statusText}`)
    }

    const { error: deleteError } = await supabase
      .from('crm_connections')
      .delete()
      .eq('id', connectionId)

    if (deleteError) throw deleteError
  } catch (error) {
    console.error('Error disconnecting CRM:', error)
    throw error
  }
}
