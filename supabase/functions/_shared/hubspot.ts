import { Client, AssociationTypes } from 'npm:@hubspot/api-client'
// @ts-ignore: SDK type issue
import { SimplePublicObject } from 'npm:@hubspot/api-client/lib/codegen/crm/deals/api.js'
import { supabase } from './utils.ts'
const HUBSPOT_CLIENT_ID = Deno.env.get('HUBSPOT_CLIENT_ID')!
const HUBSPOT_CLIENT_SECRET = Deno.env.get('HUBSPOT_CLIENT_SECRET')!

const hubspotClient = new Client()

export interface ContactData {
  first_name: string
  last_name: string
  email: string
  phone?: string
  job_title?: string
  hubspot_owner_id?: string
  properties?: Record<string, unknown>
}

export interface CompanyData {
  name: string
  domain?: string
  industry?: string
  type?: string
  description?: string
  linkedin_url?: string
  annual_revenue?: number
  employee_count?: number
  phone?: string
  street_address?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  timezone?: string
  hubspot_owner_id?: string
  properties?: Record<string, unknown>
}

interface CrmConnectionCredentials {
  access_token: string
  refresh_token: string
  expires_at: string // ISO Date string
  // Potentially other credential fields if they exist
}

export interface CrmConnection {
  // Exporting in case it's needed by other files using these utils
  id: string
  credentials: CrmConnectionCredentials
  is_synced_contacts?: boolean
  is_synced_companies?: boolean
  is_synced_deals?: boolean
  crm_type?: string
  crm_instance_identifier?: string
  // other fields from crm_connections table if needed
}

// Deal data interface
export interface DealData {
  name: string
  amount?: number
  pipeline?: string
  stage?: string
  close_date?: string
  priority?: string
  type?: string
  hubspot_owner_id?: string
  properties?: Record<string, unknown>
}

export const getHubSportAuthorizationUrl = (redirectUri: string = ''): string => {
  const scope = [
    'crm.lists.read',
    'crm.lists.write',
    'crm.objects.carts.read',
    'crm.objects.carts.write',
    'crm.objects.companies.read',
    'crm.objects.companies.write',
    'crm.objects.contacts.write',
    'crm.objects.contacts.read',
    'crm.objects.deals.write',
    'crm.objects.deals.read',
    'crm.objects.leads.write',
    'crm.objects.leads.read',
    'crm.objects.orders.read',
    'crm.objects.orders.write',
    'crm.objects.owners.read',
    'oauth',
  ].join(' ')

  return hubspotClient.oauth.getAuthorizationUrl(HUBSPOT_CLIENT_ID, redirectUri, scope)
}

export const getHubSpotAccessToken = async (code: string, redirectUri: string) => {
  try {
    const tokens = await hubspotClient.oauth.tokensApi.create(
      'authorization_code',
      code, // the code you received from the oauth flow
      redirectUri,
      HUBSPOT_CLIENT_ID,
      HUBSPOT_CLIENT_SECRET,
    )
    hubspotClient.setAccessToken(tokens.accessToken)

    // Get account details
    const responseDetails = await hubspotClient.apiRequest({
      method: 'GET',
      path: '/account-info/v3/details',
    })
    const details = await responseDetails.json()
    const userResponse = await hubspotClient.oauth.accessTokensApi.get(tokens.accessToken)
    // Get user details from the access token
    return { tokens, details, ownerId: userResponse.userId, ownerEmail: userResponse.user }
  } catch (error) {
    console.error('Error fetching access token:', error)
    throw error
  }
}

export const getRefreshToken = (refreshToken: string) => {
  return hubspotClient.oauth.tokensApi.create(
    'refresh_token',
    undefined,
    undefined,
    HUBSPOT_CLIENT_ID,
    HUBSPOT_CLIENT_SECRET,
    refreshToken,
  )
}

// Helper to get HubSpot client from connection
export async function getHubSpotClient(connectionId: string) {
  const { data: connection, error } = await supabase
    .from('crm_connections')
    .select('credentials')
    .eq('id', connectionId)
    .single()

  if (error) throw new Error('Connection not found')

  // Check if token is expired
  const now = new Date()
  const expiresAt = new Date(connection.credentials.expires_at)

  if (now >= expiresAt) {
    // Token is expired, refresh it
    try {
      const tokens = await getRefreshToken(connection.credentials.refresh_token)

      // Calculate new expiration
      const newExpiresAt = new Date(now.getTime() + tokens.expiresIn * 1000).toISOString()

      // Update connection with new tokens
      const { error: updateError } = await supabase
        .from('crm_connections')
        .update({
          credentials: {
            ...connection.credentials,
            access_token: tokens.accessToken,
            refresh_token: tokens.refreshToken,
            expires_at: newExpiresAt,
          },
        })
        .eq('id', connectionId)

      if (updateError) throw updateError

      // Return client with new access token
      return new Client({ accessToken: tokens.accessToken })
    } catch (refreshError) {
      console.error('Error refreshing token:', refreshError)
      throw new Error('Failed to refresh access token')
    }
  }

  // Token is still valid
  return new Client({ accessToken: connection.credentials.access_token })
}

// Contact operations
export async function createHubSpotContact(connectionId: string, contactData: ContactData) {
  const hubspot = await getHubSpotClient(connectionId)

  // Create contact in HubSpot
  const response = await hubspot.crm.contacts.basicApi.create({
    properties: {
      firstname: contactData.first_name,
      lastname: contactData.last_name ?? '',
      email: contactData.email ?? '',
      phone: contactData.phone ?? '',
      jobtitle: contactData.job_title ?? '',
      hubspot_owner_id: contactData.hubspot_owner_id ?? undefined,
      ...contactData.properties,
    },
  })

  // Create cache entry
  await supabase.from('cached_crm_contacts').insert({
    crm_connection_id: connectionId,
    crm_entity_id: response.id,
    first_name: contactData.first_name,
    last_name: contactData.last_name,
    email: contactData.email,
    phone: contactData.phone ?? '',
    job_title: contactData.job_title ?? '',
    hubspot_owner_id: contactData.hubspot_owner_id ?? undefined,
    properties: contactData.properties,
    last_synced_at: new Date().toISOString(),
  })

  return response
}

export async function updateHubSpotContact(
  connectionId: string,
  contactId: string,
  contactData: ContactData,
) {
  const hubspot = await getHubSpotClient(connectionId)

  // Update contact in HubSpot
  const response = await hubspot.crm.contacts.basicApi.update(contactId, {
    properties: {
      firstname: contactData.first_name,
      lastname: contactData.last_name,
      email: contactData.email,
      phone: contactData.phone ?? '',
      jobtitle: contactData.job_title ?? '',
      hubspot_owner_id: contactData.hubspot_owner_id ?? undefined,
      ...contactData.properties,
    },
  })

  // Update cache
  await supabase
    .from('cached_crm_contacts')
    .update({
      first_name: contactData.first_name,
      last_name: contactData.last_name,
      email: contactData.email,
      phone: contactData.phone ?? '',
      job_title: contactData.job_title ?? '',
      hubspot_owner_id: contactData.hubspot_owner_id ?? undefined,
      properties: contactData.properties,
      last_synced_at: new Date().toISOString(),
    })
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', contactId)

  return response
}

export async function deleteHubSpotContact(connectionId: string, contactId: string) {
  const hubspot = await getHubSpotClient(connectionId)

  // Delete contact in HubSpot
  await hubspot.crm.contacts.basicApi.archive(contactId)

  // Delete from cache
  await supabase
    .from('cached_crm_contacts')
    .delete()
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', contactId)

  return { success: true }
}

export async function syncHubSpotContacts(connection: CrmConnection) {
  const connectionId = connection.id
  const hubspot = await getHubSpotClient(connectionId)

  if (connection.is_synced_contacts) {
    return {
      success: true,
      stats: {
        contacts: 0,
      },
    }
  }

  try {
    // Fetch all contacts
    const contacts = await hubspot.crm.contacts.getAll(undefined, '0', [
      // Changed null to undefined
      'email',
      'firstname',
      'lastname',
      'phone',
      'jobtitle',
      'hubspot_owner_id',
    ])

    // Batch upsert contacts to cached_crm_contacts
    const { error: contactsError } = await supabase.from('cached_crm_contacts').upsert(
      contacts.map(contact => ({
        crm_connection_id: connectionId,
        crm_entity_id: contact.id,
        first_name: contact.properties.firstname,
        last_name: contact.properties.lastname,
        email: contact.properties.email,
        phone: contact.properties.phone,
        job_title: contact.properties.jobtitle,
        hubspot_owner_id: contact.properties.hubspot_owner_id ?? undefined,
        properties: contact.properties,
        last_synced_at: new Date().toISOString(),
        created_at: contact.properties.createdate,
        updated_at: contact.properties.lastmodifieddate,
      })),
    )

    if (contactsError) throw contactsError

    // Update connection to mark contacts as synced
    const { error: updateError } = await supabase
      .from('crm_connections')
      .update({
        is_synced_contacts: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', connectionId)

    if (updateError) throw updateError

    return {
      success: true,
      stats: {
        contacts: contacts.length,
      },
    }
  } catch (error) {
    console.error('Contact sync error:', error)
    throw new Error('Failed to sync contacts from HubSpot')
  }
}

export async function syncHubSpotCompanies(connection: CrmConnection) {
  const connectionId = connection.id
  const hubspot = await getHubSpotClient(connectionId)

  if (connection.is_synced_companies) {
    return {
      success: true,
      stats: {
        companies: 0,
      },
    }
  }

  try {
    // Fetch all companies
    const companies = await hubspot.crm.companies.getAll(undefined, '0', [
      // Changed null to undefined
      'name',
      'domain',
      'industry',
      'type',
      'description',
      'linkedin_company_page',
      'annualrevenue',
      'numberofemployees',
      'phone',
      'address',
      'city',
      'state',
      'zip',
      'country',
      'timezone',
      'website',
      'hubspot_owner_id',
      'createdate',
      'lastmodifieddate',
      'hs_object_id',
    ])

    // Batch upsert companies to cached_crm_companies
    const { error: companiesError } = await supabase.from('cached_crm_companies').upsert(
      companies.map(company => ({
        crm_connection_id: connectionId,
        crm_entity_id: company.id,
        name: company.properties.name || '', // Add fallback for null names
        domain: company.properties.domain ?? undefined,
        industry: company.properties.industry ?? undefined,
        type: company.properties.type ?? undefined,
        description: company.properties.description ?? undefined,
        linkedin_url: company.properties.linkedin_company_page ?? undefined,
        annual_revenue: company.properties.annualrevenue ?? undefined,
        employee_count: company.properties.numberofemployees ?? undefined,
        phone: company.properties.phone ?? undefined,
        street_address: company.properties.address ?? undefined,
        city: company.properties.city ?? undefined,
        state: company.properties.state ?? undefined,
        postal_code: company.properties.zip ?? undefined,
        country: company.properties.country ?? undefined,
        timezone: company.properties.timezone ?? undefined,
        hubspot_owner_id: company.properties.hubspot_owner_id ?? undefined,
        properties: company.properties,
        last_synced_at: new Date().toISOString(),
        created_at: company.properties.createdate ?? undefined,
        updated_at: company.properties.lastmodifieddate ?? undefined,
      })),
    )

    if (companiesError) throw companiesError

    // Update connection to mark companies as synced
    const { error: updateError } = await supabase
      .from('crm_connections')
      .update({
        is_synced_companies: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', connectionId)

    if (updateError) throw updateError

    return {
      success: true,
      stats: {
        companies: companies.length,
      },
    }
  } catch (error) {
    console.error('Company sync error:', error)
    throw new Error('Failed to sync companies from HubSpot')
  }
}

export async function syncHubSpotDeals(connection: CrmConnection) {
  const connectionId = connection.id
  const hubspot = await getHubSpotClient(connectionId)

  if (connection.is_synced_deals) {
    return {
      success: true,
      stats: {
        deals: 0,
      },
    }
  }

  try {
    // Fetch all deals
    const deals = await hubspot.crm.deals.getAll(undefined, '0', [
      // Changed null to undefined
      'dealname',
      'amount',
      'pipeline',
      'dealstage',
      'closedate',
      'hs_priority',
      'dealtype',
      'hubspot_owner_id',
    ])

    // Batch upsert deals to cached_crm_deals
    const { error: dealsError } = await supabase.from('cached_crm_deals').upsert(
      deals.map(deal => ({
        crm_connection_id: connectionId,
        crm_entity_id: deal.id,
        name: deal.properties.dealname || '',
        amount: deal.properties.amount,
        pipeline: deal.properties.pipeline || 'default',
        stage: deal.properties.dealstage || 'appointmentscheduled',
        close_date: deal.properties.closedate,
        priority: deal.properties.hs_priority,
        type: deal.properties.dealtype,
        hubspot_owner_id: deal.properties.hubspot_owner_id ?? undefined,
        properties: deal.properties,
        last_synced_at: new Date().toISOString(),
        created_at: deal.properties.createdate,
        updated_at: deal.properties.lastmodifieddate,
      })),
    )

    if (dealsError) throw dealsError

    // Update connection to mark deals as synced
    const { error: updateError } = await supabase
      .from('crm_connections')
      .update({
        is_synced_deals: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', connectionId)

    if (updateError) throw updateError

    return {
      success: true,
      stats: {
        deals: deals.length,
      },
    }
  } catch (error) {
    console.error('Deal sync error:', error)
    throw new Error('Failed to sync deals from HubSpot')
  }
}

export async function syncHubSpotData(connectionId: string) {
  // Get the connection details
  const { data: connection, error: connectionError } = await supabase
    .from('crm_connections')
    .select('*')
    .eq('id', connectionId)
    .single()

  if (connectionError) throw new Error('Failed to get connection details')

  try {
    // Sync contacts, companies, and deals
    const [contactsResult, companiesResult, dealsResult] = await Promise.all([
      syncHubSpotContacts(connection),
      syncHubSpotCompanies(connection),
      syncHubSpotDeals(connection),
    ])

    return {
      success: true,
      stats: {
        contacts: contactsResult.stats.contacts,
        companies: companiesResult.stats.companies,
        deals: dealsResult.stats.deals,
      },
    }
  } catch (error) {
    console.error('Sync error:', error)
    throw new Error('Failed to sync data from HubSpot')
  }
}

// Company operations
export async function createHubSpotCompany(connectionId: string, companyData: CompanyData) {
  const hubspot = await getHubSpotClient(connectionId)

  // Create company in HubSpot
  const response = await hubspot.crm.companies.basicApi.create({
    properties: {
      name: companyData.name,
      domain: companyData.domain ?? '',
      industry: companyData.industry ?? '',
      type: companyData.type ?? '',
      description: companyData.description ?? '',
      linkedin_company_page: companyData.linkedin_url ?? '',
      annualrevenue: companyData.annual_revenue?.toString() ?? '',
      numberofemployees: companyData.employee_count?.toString() ?? '',
      phone: companyData.phone ?? '',
      address: companyData.street_address ?? '',
      city: companyData.city ?? '',
      state: companyData.state ?? '',
      zip: companyData.postal_code ?? '',
      country: companyData.country ?? '',
      timezone: companyData.timezone ?? '',
      hubspot_owner_id: companyData.hubspot_owner_id ?? undefined,
      ...companyData.properties,
    },
  })

  // Create cache entry
  await supabase.from('cached_crm_companies').insert({
    crm_connection_id: connectionId,
    crm_entity_id: response.id,
    name: companyData.name,
    domain: companyData.domain ?? '',
    industry: companyData.industry ?? '',
    type: companyData.type ?? '',
    description: companyData.description ?? '',
    linkedin_url: companyData.linkedin_url ?? '',
    annual_revenue: companyData.annual_revenue,
    employee_count: companyData.employee_count,
    phone: companyData.phone ?? '',
    street_address: companyData.street_address ?? '',
    city: companyData.city ?? '',
    state: companyData.state ?? '',
    postal_code: companyData.postal_code ?? '',
    country: companyData.country ?? '',
    timezone: companyData.timezone ?? '',
    hubspot_owner_id: companyData.hubspot_owner_id ?? undefined,
    properties: companyData.properties,
    last_synced_at: new Date().toISOString(),
  })

  return response
}

export async function updateHubSpotCompany(
  connectionId: string,
  companyId: string,
  companyData: CompanyData,
) {
  const hubspot = await getHubSpotClient(connectionId)

  // Update company in HubSpot
  const response = await hubspot.crm.companies.basicApi.update(companyId, {
    properties: {
      name: companyData.name,
      domain: companyData.domain ?? '',
      industry: companyData.industry ?? '',
      type: companyData.type ?? '',
      description: companyData.description ?? '',
      linkedin_company_page: companyData.linkedin_url ?? '',
      annualrevenue: companyData.annual_revenue?.toString() ?? '',
      numberofemployees: companyData.employee_count?.toString() ?? '',
      phone: companyData.phone ?? '',
      address: companyData.street_address ?? '',
      city: companyData.city ?? '',
      state: companyData.state ?? '',
      zip: companyData.postal_code ?? '',
      country: companyData.country ?? '',
      timezone: companyData.timezone ?? '',
      hubspot_owner_id: companyData.hubspot_owner_id ?? undefined,
      ...companyData.properties,
    },
  })

  // Update cache
  await supabase
    .from('cached_crm_companies')
    .update({
      name: companyData.name,
      domain: companyData.domain ?? '',
      industry: companyData.industry ?? '',
      type: companyData.type ?? '',
      description: companyData.description ?? '',
      linkedin_url: companyData.linkedin_url ?? '',
      annual_revenue: companyData.annual_revenue,
      employee_count: companyData.employee_count,
      phone: companyData.phone ?? '',
      street_address: companyData.street_address ?? '',
      city: companyData.city ?? '',
      state: companyData.state ?? '',
      postal_code: companyData.postal_code ?? '',
      country: companyData.country ?? '',
      timezone: companyData.timezone ?? '',
      hubspot_owner_id: companyData.hubspot_owner_id ?? undefined,
      properties: companyData.properties,
      last_synced_at: new Date().toISOString(),
    })
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', companyId)

  return response
}

export async function deleteHubSpotCompany(connectionId: string, companyId: string) {
  const hubspot = await getHubSpotClient(connectionId)

  // Delete company in HubSpot
  await hubspot.crm.companies.basicApi.archive(companyId)

  // Delete from cache
  await supabase
    .from('cached_crm_companies')
    .delete()
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', companyId)

  return { success: true }
}

export async function getCompanyAssociatedContacts(connectionId: string, companyId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'company',
    companyId,
    'contact',
    undefined,
    500, // Changed '500' to 500
  )
  console.log('🚀 ~ getCompanyAssociatedContacts ~ response:', response?.results)

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []
  console.log('🚀 ~ getCompanyAssociatedContacts ~ itemIds:', itemIds)

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_contacts')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

export async function getCompanyAssociatedDeals(connectionId: string, companyId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'company',
    companyId,
    'deal',
    undefined,
    500, // Changed '500' to 500
  )

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_deals')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

export async function getContactAssociatedCompanies(connectionId: string, contactId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'contact',
    contactId,
    'company',
    undefined,
    500, // Changed '500' to 500
  )

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_companies')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

export async function getContactAssociatedDeals(connectionId: string, contactId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'contact',
    contactId,
    'deal',
    undefined,
    500, // Changed '500' to 500
  )

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_deals')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

export async function getDealAssociatedContacts(connectionId: string, dealId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'deal',
    dealId,
    'contact',
    undefined,
    500, // Changed '500' to 500
  )

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_contacts')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

export async function getDealAssociatedCompanies(connectionId: string, dealId: string) {
  const hubspotClient = await getHubSpotClient(connectionId)

  const response = await hubspotClient.crm.associations.v4.basicApi.getPage(
    'deal',
    dealId,
    'company',
    undefined,
    500, // Changed '500' to 500
  )

  const itemIds = response?.results?.map((el: { toObjectId: string }) => el.toObjectId) ?? []

  if (!itemIds.length) return []

  const { data, error } = await supabase
    .from('cached_crm_companies')
    .select('*')
    .in('crm_entity_id', itemIds)
    .eq('crm_connection_id', connectionId)

  if (error) {
    throw error
  }

  return data
}

interface HubSpotEvent {
  portalId: number
  objectId: number
  subscriptionType: string
  occurredAt: number
  changeFlag: string
  changeSource: string
  sourceId: string
}

async function handleContactEvent(crmConnection: CrmConnection, event: HubSpotEvent) {
  const hubspotClient = await getHubSpotClient(crmConnection.id)
  const { objectId, subscriptionType } = event

  if (subscriptionType === 'contact.deletion') {
    await supabase.from('cached_crm_contacts').delete().match({
      crm_connection_id: crmConnection.id,
      crm_entity_id: objectId.toString(),
    })
    return
  }

  if (subscriptionType === 'contact.creation' || subscriptionType === 'contact.propertyChange') {
    const contact = await hubspotClient.crm.contacts.basicApi.getById(objectId.toString(), [
      // Added .toString()
      'email',
      'firstname',
      'lastname',
      'phone',
      'jobtitle',
      'hubspot_owner_id',
    ])

    await supabase.from('cached_crm_contacts').upsert(
      {
        crm_connection_id: crmConnection.id,
        crm_entity_id: objectId.toString(),
        first_name: contact.properties.firstname,
        last_name: contact.properties.lastname,
        email: contact.properties.email,
        phone: contact.properties.phone,
        job_title: contact.properties.jobtitle,
        hubspot_owner_id: contact.properties.hubspot_owner_id ?? undefined,
        properties: contact.properties,
        last_synced_at: new Date().toISOString(),
      },
      {
        onConflict: 'crm_connection_id,crm_entity_id',
      },
    )
  }
}

async function handleCompanyEvent(crmConnection: CrmConnection, event: HubSpotEvent) {
  const hubspotClient = await getHubSpotClient(crmConnection.id)

  const { objectId, subscriptionType } = event

  if (subscriptionType === 'company.deletion') {
    await supabase.from('cached_crm_companies').delete().match({
      crm_connection_id: crmConnection.id,
      crm_entity_id: objectId.toString(),
    })
    return
  }

  if (subscriptionType === 'company.creation' || subscriptionType === 'company.propertyChange') {
    const company = await hubspotClient.crm.companies.basicApi.getById(objectId.toString(), [
      // Added .toString()
      'name',
      'domain',
      'industry',
      'type',
      'description',
      'linkedin_company_page',
      'annualrevenue',
      'numberofemployees',
      'phone',
      'address',
      'city',
      'state',
      'zip',
      'country',
      'timezone',
      'website',
      'hubspot_owner_id',
      'createdate',
      'lastmodifieddate',
      'hs_object_id',
    ])

    const { data: _data, error } = await supabase.from('cached_crm_companies').upsert(
      {
        crm_connection_id: crmConnection.id,
        crm_entity_id: objectId.toString(),
        name: company.properties.name,
        domain: company.properties.domain,
        industry: company.properties.industry,
        type: company.properties.type,
        description: company.properties.description,
        linkedin_url: company.properties.linkedin_company_page,
        annual_revenue: company.properties.annualrevenue,
        employee_count: company.properties.numberofemployees,
        phone: company.properties.phone,
        street_address: company.properties.address,
        city: company.properties.city,
        state: company.properties.state,
        postal_code: company.properties.zip,
        country: company.properties.country,
        timezone: company.properties.timezone,
        hubspot_owner_id: company.properties.hubspot_owner_id ?? undefined,
        properties: company.properties,
        last_synced_at: new Date().toISOString(),
        created_at: company.properties.createdate,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: 'crm_connection_id,crm_entity_id',
        ignoreDuplicates: false,
      },
    )
    if (error) {
      console.log(error)
    }
  }
}

async function handleDealEvent(crmConnection: CrmConnection, event: HubSpotEvent) {
  const hubspotClient = await getHubSpotClient(crmConnection.id)

  const { objectId, subscriptionType } = event

  if (subscriptionType === 'deal.deletion') {
    await supabase.from('cached_crm_deals').delete().match({
      crm_connection_id: crmConnection.id,
      crm_entity_id: objectId.toString(),
    })
    return
  }

  if (subscriptionType === 'deal.creation' || subscriptionType === 'deal.propertyChange') {
    const deal = await hubspotClient.crm.deals.basicApi.getById(objectId.toString(), [
      // Added .toString()
      'dealname',
      'amount',
      'pipeline',
      'dealstage',
      'closedate',
      'hs_priority',
      'dealtype',
      'hubspot_owner_id',
    ])

    await supabase.from('cached_crm_deals').upsert(
      {
        crm_connection_id: crmConnection.id,
        crm_entity_id: objectId.toString(),
        name: deal.properties.dealname,
        amount: deal.properties.amount,
        pipeline: deal.properties.pipeline,
        stage: deal.properties.dealstage,
        close_date: deal.properties.closedate,
        priority: deal.properties.hs_priority,
        type: deal.properties.dealtype,
        hubspot_owner_id: deal.properties.hubspot_owner_id ?? undefined,
        properties: deal.properties,
        last_synced_at: new Date().toISOString(),
      },
      {
        onConflict: 'crm_connection_id,crm_entity_id',
      },
    )
  }
}

const getChangeType = (type: string) => {
  if (type.includes('creation')) return 'created'
  if (type.includes('deletion')) return 'deleted'
  return 'updated'
}

export const processHubSpotWebhookEvent = async (event: HubSpotEvent) => {
  const { portalId, objectId, subscriptionType, occurredAt, changeSource, sourceId } = event

  // Convert milliseconds timestamp to ISO string
  const timestamp = new Date(occurredAt).toISOString()

  // Find the CRM connection for this portal
  const { data: crmConnections, error: crmError } = await supabase
    .from('crm_connections')
    .select('*')
    .eq('crm_type', 'hubspot')
    .eq('crm_instance_identifier', portalId.toString())

  if (crmError || !crmConnections?.length) {
    console.error(`No CRM connection found for portal ${portalId}`)
    return
  }

  const [entityType] = subscriptionType.split('.')
  console.log('🚀 ~ processHubSpotWebhookEvent ~ entityType:', entityType)

  try {
    // Handle different entity types
    switch (entityType) {
      case 'contact':
        await Promise.all(
          crmConnections.map(crmConnection => handleContactEvent(crmConnection, event)),
        )
        break

      case 'company':
        await Promise.all(
          crmConnections.map(crmConnection => handleCompanyEvent(crmConnection, event)),
        )
        break

      case 'deal':
        Promise.all(crmConnections.map(crmConnection => handleDealEvent(crmConnection, event)))
        break

      default:
        console.warn(`Unhandled entity type: ${entityType}`)
    }

    // Create observation record for all events
    await Promise.all(
      crmConnections.map(crmConnection =>
        supabase.from('observations').insert({
          crm_connection_id: crmConnection.id,
          source: 'hubspot',
          entity_type: entityType,
          entity_id: objectId.toString(),
          change_type: getChangeType(subscriptionType),
          timestamp: timestamp,
          state_snapshot: {
            event,
            source: changeSource,
            source_id: sourceId,
          },
        }),
      ),
    )
    console.log(`Processed ${entityType} event for ${objectId}`)
  } catch (error) {
    console.error(`Error processing ${entityType}:`, error)
    throw error
  }
}

// Properties to fetch for companies (align with sync and cache)
const COMPANY_PROPERTIES = [
  'name',
  'domain',
  'industry',
  'type',
  'description',
  'linkedin_company_page', // actual HubSpot property name might be linkedin_company_page or similar
  'annualrevenue',
  'numberofemployees',
  'phone',
  'address', // Full street address
  'city',
  'state',
  'zip', // Postal code
  'country',
  'timezone',
  'website',
  'hs_object_id', // Ensure HubSpot Object ID is fetched
  'createdate',
  'lastmodifieddate',
]

export async function getHubSpotCompanyById(
  connectionId: string,
  companyId: string,
  forceFetch = false, // Option to bypass cache
) {
  // Attempt to read from cache first, unless forceFetch is true
  if (!forceFetch) {
    const { data: cachedCompany, error: cacheError } = await supabase
      .from('cached_crm_companies')
      .select('*')
      .eq('crm_connection_id', connectionId)
      .eq('crm_entity_id', companyId)
      .maybeSingle()

    if (cacheError) {
      console.warn(`Cache read error for company ${companyId}:`, cacheError.message)
    } else if (cachedCompany) {
      return cachedCompany as unknown // Cast to unknown
    }
  }

  const hubspot = await getHubSpotClient(connectionId)
  try {
    const company = await hubspot.crm.companies.basicApi.getById(companyId, COMPANY_PROPERTIES)

    const companyToCache = {
      crm_connection_id: connectionId,
      crm_entity_id: company.id,
      name: company.properties.name,
      domain: company.properties.domain,
      industry: company.properties.industry,
      type: company.properties.type,
      description: company.properties.description,
      linkedin_url: company.properties.linkedin_company_page,
      annual_revenue: company.properties.annualrevenue,
      employee_count: company.properties.numberofemployees,
      phone: company.properties.phone,
      street_address: company.properties.address,
      city: company.properties.city,
      state: company.properties.state,
      postal_code: company.properties.zip,
      country: company.properties.country,
      timezone: company.properties.timezone,
      properties: company.properties,
      last_synced_at: new Date().toISOString(),
      created_at: company.properties.createdate || new Date(company.createdAt).toISOString(),
      updated_at: company.properties.lastmodifieddate || new Date(company.updatedAt).toISOString(),
    }

    const { error: upsertError } = await supabase
      .from('cached_crm_companies')
      .upsert(companyToCache, { onConflict: 'crm_connection_id, crm_entity_id' })

    if (upsertError) {
      console.error('Error caching HubSpot company:', upsertError.message)
    }
    return company
  } catch (error: unknown) {
    // Changed error to error: unknown
    // @ts-ignore TODO: Fix this type error
    if (error.response && error.response.status === 404) {
      await supabase
        .from('cached_crm_companies')
        .delete()
        .eq('crm_connection_id', connectionId)
        .eq('crm_entity_id', companyId)
      return null
    }
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error(`Error fetching company ${companyId} from HubSpot:`, message)
    throw new Error(`Failed to fetch company ${companyId}: ${message}`)
  }
}

export async function searchHubSpotCompanies(
  connectionId: string,
  searchQuery: string,
  limit = 10,
  after?: string,
) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    const searchRequest = {
      query: searchQuery,
      limit,
      after,
      properties: COMPANY_PROPERTIES,
      filterGroups: [],
    }
    // Assuming hubspot.crm.companies.searchApi.doSearch exists
    const response = await hubspot.crm.companies.searchApi.doSearch(searchRequest)
    return response.results
  } catch (error: unknown) {
    // Changed error to error: unknown
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error searching companies in HubSpot:', message)
    throw new Error(`Failed to search companies: ${message}`)
  }
}

// Properties to fetch for contacts (align with sync and cache)
const CONTACT_PROPERTIES = [
  'email',
  'firstname',
  'lastname',
  'phone',
  'jobtitle',
  'hs_object_id', // Ensure HubSpot Object ID is fetched
  'createdate',
  'lastmodifieddate',
]

export async function getHubSpotContactById(
  connectionId: string,
  contactId: string,
  forceFetch = false, // Option to bypass cache
) {
  // Attempt to read from cache first, unless forceFetch is true
  if (!forceFetch) {
    const { data: cachedContact, error: cacheError } = await supabase
      .from('cached_crm_contacts')
      .select('*')
      .eq('crm_connection_id', connectionId)
      .eq('crm_entity_id', contactId)
      .maybeSingle()

    if (cacheError) {
      console.warn(`Cache read error for contact ${contactId}:`, cacheError.message)
      // Don't throw, proceed to fetch from API
    } else if (cachedContact) {
      // Optional: Check how old the cache is if needed, then decide to refetch
      // For now, return if found
      return cachedContact as unknown // Cast to unknown
    }
  }

  // If not in cache or forceFetch is true, fetch from HubSpot API
  const hubspot = await getHubSpotClient(connectionId)
  try {
    const contact = await hubspot.crm.contacts.basicApi.getById(contactId, CONTACT_PROPERTIES)

    // Update cache with the fetched contact
    const contactToCache = {
      crm_connection_id: connectionId,
      crm_entity_id: contact.id, // contact.id from API is the crm_entity_id
      first_name: contact.properties.firstname,
      last_name: contact.properties.lastname,
      email: contact.properties.email,
      phone: contact.properties.phone,
      job_title: contact.properties.jobtitle,
      properties: contact.properties, // Store all fetched properties
      last_synced_at: new Date().toISOString(),
      created_at: contact.properties.createdate || new Date(contact.createdAt).toISOString(),
      updated_at: contact.properties.lastmodifieddate || new Date(contact.updatedAt).toISOString(),
    }

    const { error: upsertError } = await supabase
      .from('cached_crm_contacts')
      .upsert(contactToCache, { onConflict: 'crm_connection_id, crm_entity_id' })

    if (upsertError) {
      console.error('Error caching HubSpot contact:', upsertError.message)
    }
    return contact // Return the direct HubSpot API response structure
  } catch (error: unknown) {
    // Changed error to error: unknown
    // @ts-ignore TODO: Fix this type error
    if (error.response && error.response.status === 404) {
      // If contact not found in HubSpot, attempt to delete from cache
      await supabase
        .from('cached_crm_contacts')
        .delete()
        .eq('crm_connection_id', connectionId)
        .eq('crm_entity_id', contactId)
      return null
    }
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error(`Error fetching contact ${contactId} from HubSpot:`, message)
    throw new Error(`Failed to fetch contact ${contactId}: ${message}`)
  }
}

export async function searchHubSpotContacts(
  connectionId: string,
  searchQuery: string, // e.g., "John Doe" or "<EMAIL>"
  limit = 10,
  after?: string,
) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    // Ensure you have the correct import for PublicObjectSearchRequest if needed by your SDK version for strong typing
    // import { PublicObjectSearchRequest } from 'npm:@hubspot/api-client/lib/codegen/crm/contacts/api/PublicObjectApi';
    const searchRequest = {
      // Type this with PublicObjectSearchRequest if available
      query: searchQuery, // HubSpot attempts to search across default fields
      limit,
      after,
      properties: CONTACT_PROPERTIES,
      filterGroups: [], // Keep empty for broad search by query, or add specific filters
    }

    // The exact path to search API might vary slightly by SDK version or object type
    // Common paths: hubspot.crm.contacts.searchApi.doSearch(searchRequest)
    // or a more generic one like hubspot.crm.objects.searchApi.doSearch('contacts', searchRequest)
    const response = await hubspot.crm.contacts.searchApi.doSearch(searchRequest)
    return response.results // Returns an array of contacts (SimplePublicObjectWithAssociations[])
  } catch (error: unknown) {
    // Changed error to error: unknown
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error searching contacts in HubSpot:', message)
    throw new Error(`Failed to search contacts: ${message}`)
  }
}

// Engagement Association Data interface
export interface EngagementAssociationData {
  toObjectType: 'contact' | 'company' | 'deal'
  toObjectId: string
  associationType: string // e.g., 'note_to_contact', 'call_to_deal' (HubSpot defined)
}

// Note data interface for creation
export interface HubSpotNotePayload {
  properties: {
    hs_timestamp: string // ISO 8601 UTC timestamp for when the note occurred
    hs_note_body: string
    // hubspot_owner_id?: string; // Optional: if you want to assign an owner
  }
  associations?: Array<{
    to: { id: string }
    types: Array<{
      associationCategory: 'HUBSPOT_DEFINED'
      associationTypeId: number
    }>
  }>
}

export async function createHubSpotNoteEngagement(
  connectionId: string,
  noteBody: string,
  timestamp?: string, // Optional, defaults to now
  associations?: Array<{ toObjectType: 'contact' | 'company' | 'deal'; toObjectId: string }>,
) {
  const hubspot = await getHubSpotClient(connectionId)

  const noteTimestamp = timestamp || new Date().toISOString()

  const notePayload: HubSpotNotePayload = {
    properties: {
      hs_timestamp: noteTimestamp,
      hs_note_body: noteBody,
    },
  }

  if (associations && associations.length > 0) {
    notePayload.associations = associations.map(assoc => {
      let associationTypeId
      if (assoc.toObjectType === 'contact') {
        associationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_CONTACT
      } else if (assoc.toObjectType === 'company') {
        associationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_COMPANY
      } else if (assoc.toObjectType === 'deal') {
        associationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_DEAL
      } else {
        throw new Error(`Unsupported association type: ${assoc.toObjectType}`)
      }
      return {
        to: { id: assoc.toObjectId },
        types: [
          {
            associationCategory: 'HUBSPOT_DEFINED' as const,
            associationTypeId: associationTypeId,
          },
        ],
      }
    })
  }

  try {
    // Use the correct objects.notes API
    const response = await hubspot.crm.objects.notes.basicApi.create(notePayload)
    return response
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error creating HubSpot note engagement:', message, error)
    throw new Error(`Failed to create note engagement: ${message}`)
  }
}

// Call data interface for creation
export interface HubSpotCallPayload {
  properties: {
    hs_timestamp: string // ISO 8601 UTC timestamp for when the call occurred
    hs_call_body?: string // Notes or transcription of the call
    hs_call_direction?: 'INBOUND' | 'OUTBOUND'
    hs_call_duration?: string // Duration in milliseconds
    hs_call_status?: string // e.g., 'COMPLETED', 'BUSY', 'NO_ANSWER'
    hs_call_title?: string // Title of the call
    // hubspot_owner_id?: string;
  }
  associations?: Array<{
    to: { id: string }
    types: Array<{
      associationCategory: 'HUBSPOT_DEFINED' | string // Loosen type for SDK compatibility
      associationTypeId: number
    }>
  }>
}

export async function createHubSpotCallEngagement(
  connectionId: string,
  callData: {
    body?: string
    direction?: 'INBOUND' | 'OUTBOUND'
    durationMs?: number
    status?: string
    title?: string
    timestamp?: string // Optional, defaults to now
  },
  associations?: Array<{ toObjectType: 'contact' | 'company' | 'deal'; toObjectId: string }>,
) {
  const hubspot = await getHubSpotClient(connectionId)

  const callTimestamp = callData.timestamp || new Date().toISOString()

  const callPayload: HubSpotCallPayload = {
    properties: {
      hs_timestamp: callTimestamp,
      hs_call_body: callData.body,
      hs_call_direction: callData.direction,
      hs_call_duration: callData.durationMs?.toString(),
      hs_call_status: callData.status,
      hs_call_title: callData.title,
    },
  }

  if (associations && associations.length > 0) {
    callPayload.associations = associations.map(assoc => {
      let associationTypeId
      if (assoc.toObjectType === 'contact') {
        associationTypeId = ASSOCIATION_TYPE_IDS.CALL_TO_CONTACT
      } else if (assoc.toObjectType === 'company') {
        associationTypeId = ASSOCIATION_TYPE_IDS.CALL_TO_COMPANY
      } else if (assoc.toObjectType === 'deal') {
        associationTypeId = ASSOCIATION_TYPE_IDS.CALL_TO_DEAL
      } else {
        throw new Error(`Unsupported association type: ${assoc.toObjectType}`)
      }
      return {
        to: { id: assoc.toObjectId },
        types: [
          {
            associationCategory: 'HUBSPOT_DEFINED' as AssociationSpecAssociationCategoryEnum,
            associationTypeId: associationTypeId,
          },
        ],
      }
    })
  }

  try {
    // Create call in HubSpot using the Objects API for Calls
    // Typically hubspot.crm.objects.calls.basicApi.create() or generic objects.basicApi.create('CALL', ...)
    // Using 'as any' for similar reasons as with note creation.
    // deno-lint-ignore no-explicit-any
    const response = await hubspot.crm.objects.basicApi.create('CALL', callPayload as any)
    return response
  } catch (error: unknown) {
    // Changed error to error: unknown
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    // @ts-ignore TODO: Fix this type error
    console.error('Error creating HubSpot call engagement:', message, error.response?.body)
    throw new Error(`Failed to create call engagement: ${message}`)
  }
}

// Properties to fetch for deals (align with sync and cache)
const DEAL_PROPERTIES = [
  'dealname',
  'amount',
  'pipeline',
  'dealstage',
  'closedate',
  'hs_priority',
  'dealtype',
  'hubspot_owner_id',
  'hs_object_id', // Ensure HubSpot Object ID is fetched
  'createdate',
  'lastmodifieddate',
]

export async function getHubSpotDealById(
  connectionId: string,
  dealId: string,
  forceFetch = false, // Option to bypass cache
) {
  // Attempt to read from cache first, unless forceFetch is true
  if (!forceFetch) {
    const { data: cachedDeal, error: cacheError } = await supabase
      .from('cached_crm_deals')
      .select('*')
      .eq('crm_connection_id', connectionId)
      .eq('crm_entity_id', dealId)
      .maybeSingle()

    if (cacheError) {
      console.warn(`Cache read error for deal ${dealId}:`, cacheError.message)
    } else if (cachedDeal) {
      return cachedDeal as unknown // Cast to unknown
    }
  }

  const hubspot = await getHubSpotClient(connectionId)
  try {
    const deal = await hubspot.crm.deals.basicApi.getById(dealId, DEAL_PROPERTIES)

    const dealToCache = {
      crm_connection_id: connectionId,
      crm_entity_id: deal.id,
      name: deal.properties.dealname,
      amount: deal.properties.amount ? parseFloat(deal.properties.amount) : undefined,
      pipeline: deal.properties.pipeline,
      stage: deal.properties.dealstage,
      close_date: deal.properties.closedate,
      priority: deal.properties.hs_priority,
      type: deal.properties.dealtype,
      hubspot_owner_id: deal.properties.hubspot_owner_id ?? undefined,
      properties: deal.properties,
      last_synced_at: new Date().toISOString(),
      created_at: deal.properties.createdate || new Date(deal.createdAt).toISOString(),
      updated_at: deal.properties.lastmodifieddate || new Date(deal.updatedAt).toISOString(),
    }

    const { error: upsertError } = await supabase
      .from('cached_crm_deals')
      .upsert(dealToCache, { onConflict: 'crm_connection_id, crm_entity_id' })

    if (upsertError) {
      console.error('Error caching HubSpot deal:', upsertError.message)
    }
    return deal
  } catch (error: unknown) {
    // Changed error to error: unknown
    // @ts-ignore TODO: Fix this type error
    if (error.response && error.response.status === 404) {
      await supabase
        .from('cached_crm_deals')
        .delete()
        .eq('crm_connection_id', connectionId)
        .eq('crm_entity_id', dealId)
      return null
    }
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error(`Error fetching deal ${dealId} from HubSpot:`, message)
    throw new Error(`Failed to fetch deal ${dealId}: ${message}`)
  }
}

export async function searchHubSpotDeals(
  connectionId: string,
  searchQuery: string,
  limit = 10,
  after?: string,
) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    const searchRequest = {
      query: searchQuery,
      limit,
      after,
      properties: DEAL_PROPERTIES,
      filterGroups: [],
    }
    // Assuming hubspot.crm.deals.searchApi.doSearch exists
    const response = await hubspot.crm.deals.searchApi.doSearch(searchRequest)
    return response.results
  } catch (error: unknown) {
    // Changed error to error: unknown
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error searching deals in HubSpot:', message)
    throw new Error(`Failed to search deals: ${message}`)
  }
}

// Deal operations
export async function createHubSpotDeal(
  connectionId: string,
  dealData: DealData,
): Promise<SimplePublicObject> {
  const hubspot = await getHubSpotClient(connectionId)

  // Create deal in HubSpot
  const response = await hubspot.crm.deals.basicApi.create({
    properties: {
      dealname: dealData.name,
      amount: dealData.amount?.toString() ?? '',
      pipeline: dealData.pipeline ?? '',
      dealstage: dealData.stage ?? '',
      closedate: dealData.close_date ?? '',
      hs_priority: dealData.priority ?? '',
      dealtype: dealData.type ?? '',
      hubspot_owner_id: dealData.hubspot_owner_id ?? undefined,
      ...dealData.properties,
    },
  })

  // Create cache entry
  await supabase.from('cached_crm_deals').insert({
    crm_connection_id: connectionId,
    crm_entity_id: response.id,
    name: dealData.name,
    amount: dealData.amount,
    pipeline: dealData.pipeline ?? '',
    stage: dealData.stage ?? '',
    close_date: dealData.close_date ?? '',
    priority: dealData.priority ?? '',
    type: dealData.type ?? '',
    hubspot_owner_id: dealData.hubspot_owner_id ?? undefined,
    properties: dealData.properties,
    last_synced_at: new Date().toISOString(),
  })

  return response
}

export async function updateHubSpotDeal(
  connectionId: string,
  dealId: string,
  dealData: DealData,
): Promise<SimplePublicObject> {
  const hubspot = await getHubSpotClient(connectionId)

  // Update deal in HubSpot
  const response = await hubspot.crm.deals.basicApi.update(dealId, {
    properties: {
      dealname: dealData.name,
      amount: dealData.amount?.toString() ?? '',
      pipeline: dealData.pipeline ?? '',
      dealstage: dealData.stage ?? '',
      closedate: dealData.close_date ?? '',
      hs_priority: dealData.priority ?? '',
      dealtype: dealData.type ?? '',
      hubspot_owner_id: dealData.hubspot_owner_id ?? undefined,
      ...dealData.properties,
    },
  })

  // Update cache
  await supabase
    .from('cached_crm_deals')
    .update({
      name: dealData.name,
      amount: dealData.amount,
      pipeline: dealData.pipeline ?? '',
      stage: dealData.stage ?? '',
      close_date: dealData.close_date ?? '',
      type: dealData.type ?? '',
      priority: dealData.priority ?? '',
      hubspot_owner_id: dealData.hubspot_owner_id ?? undefined,
      properties: dealData.properties,
      last_synced_at: new Date().toISOString(),
    })
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', dealId)

  return response
}

export async function deleteHubSpotDeal(connectionId: string, dealId: string) {
  const hubspot = await getHubSpotClient(connectionId)

  // Delete deal in HubSpot
  await hubspot.crm.deals.basicApi.archive(dealId)

  // Delete from cache
  await supabase
    .from('cached_crm_deals')
    .delete()
    .eq('crm_connection_id', connectionId)
    .eq('crm_entity_id', dealId)

  return { success: true }
}

// Pipeline operations
export async function getHubSpotDealPipelines(connectionId: string) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    const response = await hubspot.crm.pipelines.pipelinesApi.getAll('deals')
    return response.results
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching deal pipelines from HubSpot:', message)
    throw new Error(`Failed to fetch deal pipelines: ${message}`)
  }
}

export async function getHubSpotDealStages(connectionId: string, pipelineId?: string) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    if (pipelineId) {
      // Get stages for a specific pipeline
      const response = await hubspot.crm.pipelines.pipelineStagesApi.getAll('deals', pipelineId)
      return response.results
    } else {
      // Get all pipelines with their stages
      const pipelinesResponse = await hubspot.crm.pipelines.pipelinesApi.getAll('deals')
      const allStages = []

      for (const pipeline of pipelinesResponse.results) {
        const stagesResponse = await hubspot.crm.pipelines.pipelineStagesApi.getAll(
          'deals',
          pipeline.id,
        )
        allStages.push({
          pipelineId: pipeline.id,
          pipelineName: pipeline.label,
          stages: stagesResponse.results,
        })
      }

      return allStages
    }
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching deal stages from HubSpot:', message)
    throw new Error(`Failed to fetch deal stages: ${message}`)
  }
}

// Owner operations
export async function getHubSpotOwners(connectionId: string) {
  const hubspot = await getHubSpotClient(connectionId)
  try {
    const [active, archived] = await Promise.all([
      hubspot.crm.owners.ownersApi.getPage(undefined, undefined, 100, false),
      hubspot.crm.owners.ownersApi.getPage(undefined, undefined, 100, true),
    ])
    const response = [...(active?.results ?? []), ...(archived?.results ?? [])]
    return response
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching owners from HubSpot:', message)
    throw new Error(`Failed to fetch owners: ${message}`)
  }
}

/**
 * Creates an association between two HubSpot entities
 * @param connectionId The CRM connection ID
 * @param fromObjectType The type of the source object ('company', 'contact', or 'deal')
 * @param fromObjectId The ID of the source object
 * @param toObjectType The type of the target object ('company', 'contact', or 'deal')
 * @param toObjectId The ID of the target object
 * @param associationTypeId Optional: specific association type ID (defaults to standard type based on objects)
 * @returns The created association
 */
export async function createHubSpotAssociation(
  connectionId: string,
  fromObjectType: 'company' | 'contact' | 'deal',
  fromObjectId: string,
  toObjectType: 'company' | 'contact' | 'deal',
  toObjectId: string,
  associationTypeId?: number,
): Promise<any> {
  const hubspot = await getHubSpotClient(connectionId)

  // Determine the appropriate association type if not provided
  if (!associationTypeId) {
    if (fromObjectType === 'company' && toObjectType === 'contact') {
      associationTypeId = AssociationTypes.companyToContact
    } else if (fromObjectType === 'contact' && toObjectType === 'company') {
      associationTypeId = AssociationTypes.contactToCompany
    } else if (fromObjectType === 'company' && toObjectType === 'deal') {
      associationTypeId = AssociationTypes.companyToDeal
    } else if (fromObjectType === 'deal' && toObjectType === 'company') {
      associationTypeId = AssociationTypes.dealToCompany
    } else if (fromObjectType === 'contact' && toObjectType === 'deal') {
      associationTypeId = AssociationTypes.contactToDeal
    } else if (fromObjectType === 'deal' && toObjectType === 'contact') {
      associationTypeId = AssociationTypes.dealToContact
    } else {
      throw new Error(`Unsupported association between ${fromObjectType} and ${toObjectType}`)
    }
  }

  try {
    // Create the association
    const response = await hubspot.crm.associations.v4.basicApi.create(
      fromObjectType,
      fromObjectId,
      toObjectType,
      toObjectId,
      [
        {
          associationCategory: 'HUBSPOT_DEFINED',
          associationTypeId: associationTypeId,
        },
      ],
    )

    return response
  } catch (error) {
    console.error(
      `Error creating association between ${fromObjectType} ${fromObjectId} and ${toObjectType} ${toObjectId}:`,
      error,
    )
    throw error
  }
}

/**
 * Associates a contact with a company
 * @param connectionId The CRM connection ID
 * @param contactId The contact ID
 * @param companyId The company ID
 * @returns The created association
 */
export async function associateContactWithCompany(
  connectionId: string,
  contactId: string,
  companyId: string,
): Promise<any> {
  return createHubSpotAssociation(
    connectionId,
    'contact',
    contactId,
    'company',
    companyId,
    AssociationTypes.contactToCompany,
  )
}

/**
 * Associates a contact with a deal
 * @param connectionId The CRM connection ID
 * @param contactId The contact ID
 * @param dealId The deal ID
 * @returns The created association
 */
export async function associateContactWithDeal(
  connectionId: string,
  contactId: string,
  dealId: string,
): Promise<any> {
  return createHubSpotAssociation(
    connectionId,
    'contact',
    contactId,
    'deal',
    dealId,
    AssociationTypes.contactToDeal,
  )
}

/**
 * Associates a company with a deal
 * @param connectionId The CRM connection ID
 * @param companyId The company ID
 * @param dealId The deal ID
 * @returns The created association
 */
export async function associateCompanyWithDeal(
  connectionId: string,
  companyId: string,
  dealId: string,
): Promise<any> {
  return createHubSpotAssociation(
    connectionId,
    'company',
    companyId,
    'deal',
    dealId,
    AssociationTypes.companyToDeal,
  )
}

// Note data interface for updates
export interface HubSpotNoteUpdateData {
  note_body?: string
  timestamp?: string
}

// Get Note by ID
export async function getHubSpotNoteById(
  connectionId: string,
  noteId: string,
  forceFetch: boolean = false,
) {
  const hubspot = await getHubSpotClient(connectionId)

  try {
    const response = await hubspot.crm.objects.basicApi.getById('NOTE', noteId, [
      'hs_note_body',
      'hs_timestamp',
      'hubspot_owner_id',
    ])
    return response
  } catch (error) {
    console.error(`Error getting note ${noteId}:`, error)
    throw error
  }
}

// Update Note
export async function updateHubSpotNote(
  connectionId: string,
  noteId: string,
  updateData: HubSpotNoteUpdateData,
) {
  const hubspot = await getHubSpotClient(connectionId)

  const properties: Record<string, string> = {}

  if (updateData.note_body) {
    properties.hs_note_body = updateData.note_body
  }

  if (updateData.timestamp) {
    properties.hs_timestamp = updateData.timestamp
  }

  try {
    const response = await hubspot.crm.objects.basicApi.update('NOTE', noteId, {
      properties,
    })
    return response
  } catch (error) {
    console.error(`Error updating note ${noteId}:`, error)
    throw error
  }
}

// Delete Note
export async function deleteHubSpotNote(connectionId: string, noteId: string) {
  const hubspot = await getHubSpotClient(connectionId)

  try {
    await hubspot.crm.objects.basicApi.archive('NOTE', noteId)
    return { success: true, noteId }
  } catch (error) {
    console.error(`Error deleting note ${noteId}:`, error)
    throw error
  }
}

// Search Notes
export async function searchHubSpotNotes(
  connectionId: string,
  query: string,
  limit: number = 100,
  after?: string,
) {
  const hubspot = await getHubSpotClient(connectionId)

  try {
    const searchRequest = {
      filterGroups: [
        {
          filters: [
            {
              propertyName: 'hs_note_body',
              operator: 'CONTAINS_TOKEN',
              value: query,
            },
          ],
        },
      ],
      properties: ['hs_note_body', 'hs_timestamp', 'hubspot_owner_id'],
      limit,
      after,
    }

    const response = await hubspot.crm.objects.searchApi.doSearch('NOTE', searchRequest)
    return response
  } catch (error) {
    console.error('Error searching notes:', error)
    throw error
  }
}

// Associate Note with Record
export async function associateNoteWithRecord(
  connectionId: string,
  noteId: string,
  toObjectType: 'contact' | 'company' | 'deal',
  toObjectId: string,
  associationTypeId?: number,
) {
  const hubspot = await getHubSpotClient(connectionId)

  // Use default association type IDs if not provided
  let defaultAssociationTypeId: number
  if (!associationTypeId) {
    switch (toObjectType) {
      case 'contact':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_CONTACT
        break
      case 'company':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_COMPANY
        break
      case 'deal':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_DEAL
        break
      default:
        throw new Error(`Unsupported object type: ${toObjectType}`)
    }
  } else {
    defaultAssociationTypeId = associationTypeId
  }

  try {
    const response = await hubspot.crm.associations.v4.basicApi.create(
      'note',
      noteId,
      toObjectType,
      toObjectId,
      [
        {
          associationCategory: 'HUBSPOT_DEFINED',
          associationTypeId: defaultAssociationTypeId,
        },
      ],
    )
    return response
  } catch (error) {
    console.error(`Error associating note ${noteId} with ${toObjectType} ${toObjectId}:`, error)
    throw error
  }
}

// Remove Note Association
export async function removeNoteAssociation(
  connectionId: string,
  noteId: string,
  toObjectType: 'contact' | 'company' | 'deal',
  toObjectId: string,
  associationTypeId?: number,
) {
  const hubspot = await getHubSpotClient(connectionId)

  // Use default association type IDs if not provided
  let defaultAssociationTypeId: number
  if (!associationTypeId) {
    switch (toObjectType) {
      case 'contact':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_CONTACT
        break
      case 'company':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_COMPANY
        break
      case 'deal':
        defaultAssociationTypeId = ASSOCIATION_TYPE_IDS.NOTE_TO_DEAL
        break
      default:
        throw new Error(`Unsupported object type: ${toObjectType}`)
    }
  } else {
    defaultAssociationTypeId = associationTypeId
  }

  try {
    await hubspot.crm.associations.v4.basicApi.archive(
      'note',
      noteId,
      toObjectType,
      toObjectId,
      defaultAssociationTypeId,
    )
    return { success: true, noteId, toObjectType, toObjectId }
  } catch (error) {
    console.error(
      `Error removing association between note ${noteId} and ${toObjectType} ${toObjectId}:`,
      error,
    )
    throw error
  }
}

// Define association type IDs (these are standard HubSpot IDs)
// You can find these via API: GET /crm/v3/associations/{fromObjectType}/{toObjectType}/types
const ASSOCIATION_TYPE_IDS = {
  NOTE_TO_CONTACT: 202, // Note to contact
  NOTE_TO_COMPANY: 190, // Note to company
  NOTE_TO_DEAL: 214, // Note to deal
  CALL_TO_CONTACT: 202,
  CALL_TO_COMPANY: 218,
  CALL_TO_DEAL: 210,
}
