// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { serve } from 'https://deno.land/std@0.224.0/http/server.ts'
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { ElevenLabsClient } from 'npm:elevenlabs@^1.56.1'

console.log('Sync ElevenLabs Conversations function started')

// --- Environment Variables ---
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')! // Use service role for all operations
const elevenlabsApiKey = Deno.env.get('ELEVENLABS_API_KEY')!

// --- Clients ---
// Instantiate the main client
const elevenlabsClient = new ElevenLabsClient({
  apiKey: elevenlabsApiKey,
})
const supabaseAdminClient = createClient(supabaseUrl, supabaseServiceRoleKey)

// --- CORS Headers ---
// Although triggered by pg_cron, include CORS for potential manual triggers/testing
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust as needed
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// --- Helper Function: Upsert Conversation ---
async function upsertConversation(
  client: SupabaseClient,
  // deno-lint-ignore no-explicit-any
  conversation: any,
) {
  const { error } = await client.from('conversations').upsert(
    {
      conversation_id: conversation.conversation_id,
      agent_id: conversation.agent_id,
      status: conversation.status,
      transcript: conversation.transcript,
      analysis: conversation.analysis,
      metadata: conversation.metadata,
      conversation_initiation_client_data: conversation.conversation_initiation_client_data,
      data: null,
      created_at: conversation.creation_timestamp_ms
        ? new Date(conversation.creation_timestamp_ms).toISOString()
        : new Date().toISOString(),
      last_synced: new Date().toISOString(),
    },
    {
      // Use just conversation_id as conflict target if created_at is just for partitioning
      // and conversation_id is the true unique ID from ElevenLabs.
      // If a conversation could genuinely be re-created with the same ID but different timestamp
      // that needs upserting, then include created_at. Assuming conversation_id is unique.
      onConflict: 'conversation_id',
    },
  )

  if (error) {
    console.error(`Error upserting conversation ${conversation.conversation_id}:`, error)
    return false
  }
  return true
}

// --- Main Handler ---
serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Basic security check: Ensure the request originates from pg_cron or an authorized source
  // This is a basic check; enhance as needed (e.g., check IP, use a secret header)
  // Note: Supabase Edge Functions don't easily expose caller IP. pg_net requests might appear internal.
  // A simple check might be to ensure no user auth token is present, assuming cron doesn't send one.
  const authHeader = req.headers.get('Authorization')
  if (authHeader && authHeader !== `Bearer ${supabaseServiceRoleKey}`) {
    // Allow service key for potential direct calls
    console.warn('Unauthorized attempt to trigger sync function.')
    // Allow service role key bearer token for potential direct invocation/testing
    // but reject others. pg_cron won't send a user token.
    // Refine this check based on how you expect pg_cron to authenticate, if at all.
    // The pg_cron job in the migration uses the service role key.
    // return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401, headers: corsHeaders })
  }

  console.log('Starting conversation sync process...')
  let processedCount = 0
  let errorCount = 0

  try {
    // --- 1. Fetch Synced Agent IDs --- (Removed timestamp logic)
    const { data: agents, error: agentError } = await supabaseAdminClient
      .from('agents')
      .select('agent_id')

    if (agentError) {
      console.error('Error fetching agent IDs:', agentError)
      throw new Error('Failed to fetch agent list.')
    }

    if (!agents || agents.length === 0) {
      console.log('No agents found in the database. Sync finished.')
      return new Response(JSON.stringify({ message: 'No agents configured for sync.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    const agentIds = agents.map(a => a.agent_id)
    console.log(`Found ${agentIds.length} agents to sync conversations for.`)

    // --- 3. Iterate Through Agents and Fetch/Process Conversations ---
    for (const agentId of agentIds) {
      console.log(`Fetching conversations for agent: ${agentId}`)
      let nextCursor: string | undefined = undefined
      let hasMore = true

      while (hasMore) {
        try {
          // Fetch a page of conversations for the agent using the conversationalAi namespace
          // Note: API doesn't seem to support created_after filtering directly in getConversations.
          // We fetch pages and filter locally based on lastSyncTimestampMs.
          // Remove type annotation as import failed
          const conversationsPage = await elevenlabsClient.conversationalAi.getConversations({
            agent_id: agentId,
            cursor: nextCursor,
            page_size: 100, // Adjust page size as needed
          })

          // Remove type annotation as import failed
          const conversations = conversationsPage.conversations || []
          console.log(
            `  Fetched ${conversations.length} conversations (page). Cursor: ${conversationsPage.next_cursor}`,
          )

          if (conversations.length === 0) {
            hasMore = false
            break // No more conversations on this page for this agent
          }

          // Process all conversations on the page, relying on upsert
          for (const convSummary of conversations) {
            try {
              // Fetch full details for each conversation summary
              const conversationDetails = await elevenlabsClient.conversationalAi.getConversation(
                convSummary.conversation_id,
              )

              // Upsert the conversation details
              if (await upsertConversation(supabaseAdminClient, conversationDetails)) {
                processedCount++
              } else {
                errorCount++
              }

              // deno-lint-ignore no-explicit-any
            } catch (detailError: any) {
              console.error(
                `Error fetching/upserting details for conv ${convSummary.conversation_id}:`,
                detailError,
              )
              errorCount++
              // Continue processing other summaries on this page even if one fails
            }
          } // End for(convSummary)

          // Continue pagination based only on the presence of a next cursor
          nextCursor = conversationsPage.next_cursor
          if (!nextCursor) {
            hasMore = false
            console.log(`  Stopping pagination for agent ${agentId} as there is no next cursor.`)
          }
          // deno-lint-ignore no-explicit-any
        } catch (pageError: any) {
          // Catch errors fetching the page
          console.error(`Error fetching conversations page for agent ${agentId}:`, pageError)
          // Add more specific error handling based on potential SDK errors if needed
          errorCount++ // Count error for this agent's page fetch
          hasMore = false // Stop trying for this agent on error
        }
      } // End while(hasMore)
    } // End for(agentId)

    // --- 4. Return/Log Stats ---
    console.log(`Sync finished. Processed: ${processedCount}, Errors: ${errorCount}`)
    return new Response(
      JSON.stringify({ success: true, processed: processedCount, errors: errorCount }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Unhandled error in sync function:', error)
    const errorMessage =
      error instanceof Error ? error.message : 'Internal Server Error during sync'
    return new Response(JSON.stringify({ error: 'Sync failed', details: errorMessage }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
