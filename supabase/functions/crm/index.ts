import { Hono } from 'jsr:@hono/hono'
import { corsHeaders } from '../_shared/cors.ts'
import { verifyAuth } from '../_shared/utils.ts'
import {
  createHubSpotContact,
  updateHubSpotContact,
  deleteHubSpotContact,
  createHubSpotCompany,
  updateHubSpotCompany,
  deleteHubSpotCompany,
  createHubSpotDeal,
  updateHubSpotDeal,
  deleteHubSpotDeal,
  ContactData,
  CompanyData,
  DealData,
  syncHubSpotData,
  getCompanyAssociatedContacts,
  getCompanyAssociatedDeals,
  getContactAssociatedCompanies,
  getContactAssociatedDeals,
  getDealAssociatedContacts,
  getDealAssociatedCompanies,
  getHubSpotContactById as _getHubSpotContactById,
  searchHubSpotContacts as _searchHubSpotContacts,
  getHubSpotCompanyById,
  searchHubSpotCompanies,
  getHubSpotDealById,
  searchHubSpotDeals,
  createHubSpotNoteEngagement, // Added for notes
  createHubSpotCallEngagement, // Added for calls
  createHubSpotAssociation, // Added for associations
  associateContactWithCompany, // Added for contact-company associations
  associateContactWithDeal, // Added for contact-deal associations
  associateCompanyWithDeal, // Added for company-deal associations
  getHubSpotDealPipelines, // Added for pipelines
  getHubSpotDealStages, // Added for deal stages
  getHubSpotOwners, // Added for owners
  getHubSpotNoteById, // Added for notes
  updateHubSpotNote, // Added for notes
  deleteHubSpotNote, // Added for notes
  searchHubSpotNotes, // Added for notes
  associateNoteWithRecord, // Added for note associations
  removeNoteAssociation, // Added for note associations
} from '../_shared/hubspot.ts'

const functionName = 'crm'
const app = new Hono().basePath(`/${functionName}`)

// Add new sync endpoint
app.post('/:connectionId/sync', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')

    // Call the sync function
    const response = await syncHubSpotData(connectionId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.error('Sync error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create Contact
app.post('/:connectionId/contacts', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactData = await c.req.json<ContactData>()

    const response = await createHubSpotContact(connectionId, {
      ...contactData,
      properties: {
        ...contactData.properties,
      },
    })

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Update Contact
app.patch('/:connectionId/contacts/:contactId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')
    const contactData = await c.req.json<ContactData>()

    const response = await updateHubSpotContact(connectionId, contactId, {
      ...contactData,
      properties: {
        ...contactData.properties,
      },
    })

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Delete Contact
app.delete('/:connectionId/contacts/:contactId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')

    const response = await deleteHubSpotContact(connectionId, contactId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create Company
app.post('/:connectionId/companies', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyData = await c.req.json<CompanyData>()

    const response = await createHubSpotCompany(connectionId, companyData)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Update Company
app.patch('/:connectionId/companies/:companyId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')
    const companyData = await c.req.json<CompanyData>()

    const response = await updateHubSpotCompany(connectionId, companyId, companyData)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Delete Company
app.delete('/:connectionId/companies/:companyId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')

    const response = await deleteHubSpotCompany(connectionId, companyId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Company by ID
app.get('/:connectionId/companies/:companyId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')
    const { force_fetch } = c.req.query()

    const company = await getHubSpotCompanyById(connectionId, companyId, force_fetch === 'true')

    if (!company) {
      return new Response(JSON.stringify({ error: 'Company not found' }), {
        status: 404,
        headers: corsHeaders,
      })
    }
    return new Response(JSON.stringify(company), { status: 200, headers: corsHeaders })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error in GET company by ID:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Search Companies
app.post('/:connectionId/companies/search', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { query, limit, after } = await c.req.json<{
      query: string
      limit?: number
      after?: string
    }>()

    if (!query) {
      return new Response(JSON.stringify({ error: 'Search query is required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const results = await searchHubSpotCompanies(connectionId, query, limit, after)
    return new Response(JSON.stringify(results), { status: 200, headers: corsHeaders })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error in search companies:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/:connectionId/companies/:companyId/contacts', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')
    const response = await getCompanyAssociatedContacts(connectionId, companyId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/:connectionId/companies/:companyId/deals', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')
    const response = await getCompanyAssociatedDeals(connectionId, companyId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/:connectionId/contacts/:contactId/companies', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')
    const response = await getContactAssociatedCompanies(connectionId, contactId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

app.get('/:connectionId/contacts/:contactId/deals', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')
    const response = await getContactAssociatedDeals(connectionId, contactId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Add Deal endpoints
// Create Deal
app.post('/:connectionId/deals', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealData = await c.req.json<DealData>()

    const response = await createHubSpotDeal(connectionId, dealData)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', JSON.stringify(error))
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Update Deal
app.patch('/:connectionId/deals/:dealId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealId = c.req.param('dealId')
    const dealData = await c.req.json<DealData>()

    const response = await updateHubSpotDeal(connectionId, dealId, dealData)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Delete Deal
app.delete('/:connectionId/deals/:dealId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealId = c.req.param('dealId')

    const response = await deleteHubSpotDeal(connectionId, dealId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Deal Pipelines
app.get('/:connectionId/deals/pipelines', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const pipelines = await getHubSpotDealPipelines(connectionId)

    return new Response(JSON.stringify(pipelines), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching deal pipelines:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Deal Stages
app.get('/:connectionId/deals/stages', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { pipeline_id } = c.req.query()

    const stages = await getHubSpotDealStages(connectionId, pipeline_id)

    return new Response(JSON.stringify(stages), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching deal stages:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Deal by ID
app.get('/:connectionId/deals/:dealId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealId = c.req.param('dealId')
    const { force_fetch } = c.req.query()

    const deal = await getHubSpotDealById(connectionId, dealId, force_fetch === 'true')

    if (!deal) {
      return new Response(JSON.stringify({ error: 'Deal not found' }), {
        status: 404,
        headers: corsHeaders,
      })
    }
    return new Response(JSON.stringify(deal), { status: 200, headers: corsHeaders })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error in GET deal by ID:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Search Deals
app.post('/:connectionId/deals/search', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { query, limit, after } = await c.req.json<{
      query: string
      limit?: number
      after?: string
    }>()

    if (!query) {
      return new Response(JSON.stringify({ error: 'Search query is required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const results = await searchHubSpotDeals(connectionId, query, limit, after)
    return new Response(JSON.stringify(results), { status: 200, headers: corsHeaders })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error in search deals:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Deal Associated Contacts
app.get('/:connectionId/deals/:dealId/contacts', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealId = c.req.param('dealId')
    const response = await getDealAssociatedContacts(connectionId, dealId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Deal Associated Companies
app.get('/:connectionId/deals/:dealId/companies', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const dealId = c.req.param('dealId')
    const response = await getDealAssociatedCompanies(connectionId, dealId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.log('🚀 ~ error:', error)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get HubSpot Owners
app.get('/:connectionId/hubspot/owners', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const owners = await getHubSpotOwners(connectionId)
    return new Response(JSON.stringify(owners), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error fetching HubSpot owners:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create Note Engagement
app.post('/:connectionId/engagements/note', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { note_body, timestamp, associations } = await c.req.json<{
      note_body: string
      timestamp?: string
      associations?: Array<{ toObjectType: 'contact' | 'company' | 'deal'; toObjectId: string }>
    }>()

    if (!note_body) {
      return new Response(JSON.stringify({ error: 'note_body is required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const response = await createHubSpotNoteEngagement(
      connectionId,
      note_body,
      timestamp,
      associations,
    )

    return new Response(JSON.stringify(response), {
      status: 201, // 201 Created for new resource
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error creating note engagement:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create Call Engagement
app.post('/:connectionId/engagements/call', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const callDataInput = await c.req.json<{
      body?: string
      direction?: 'INBOUND' | 'OUTBOUND'
      durationMs?: number
      status?: string
      title?: string
      timestamp?: string
      associations?: Array<{ toObjectType: 'contact' | 'company' | 'deal'; toObjectId: string }>
    }>()

    // Destructure to separate associations from other call properties
    const { associations, ...restOfCallData } = callDataInput

    const response = await createHubSpotCallEngagement(
      connectionId,
      restOfCallData, // This is the object with body, direction, etc.
      associations,
    )

    return new Response(JSON.stringify(response), {
      status: 201, // 201 Created for new resource
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error creating call engagement:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Create Association
app.post('/:connectionId/associations', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { fromObjectType, fromObjectId, toObjectType, toObjectId, associationTypeId } =
      await c.req.json<{
        fromObjectType: 'company' | 'contact' | 'deal'
        fromObjectId: string
        toObjectType: 'company' | 'contact' | 'deal'
        toObjectId: string
        associationTypeId?: number
      }>()

    const response = await createHubSpotAssociation(
      connectionId,
      fromObjectType,
      fromObjectId,
      toObjectType,
      toObjectId,
      associationTypeId,
    )

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Associate Contact with Company
app.post('/:connectionId/contacts/:contactId/associate-company/:companyId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')
    const companyId = c.req.param('companyId')

    const response = await associateContactWithCompany(connectionId, contactId, companyId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Associate Contact with Deal
app.post('/:connectionId/contacts/:contactId/associate-deal/:dealId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const contactId = c.req.param('contactId')
    const dealId = c.req.param('dealId')

    const response = await associateContactWithDeal(connectionId, contactId, dealId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Associate Company with Deal
app.post('/:connectionId/companies/:companyId/associate-deal/:dealId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const companyId = c.req.param('companyId')
    const dealId = c.req.param('dealId')

    const response = await associateCompanyWithDeal(connectionId, companyId, dealId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    console.log('🚀 ~ error:', error)
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Get Note by ID
app.get('/:connectionId/notes/:noteId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const noteId = c.req.param('noteId')
    const { force_fetch } = c.req.query()

    const note = await getHubSpotNoteById(connectionId, noteId, force_fetch === 'true')

    if (!note) {
      return new Response(JSON.stringify({ error: 'Note not found' }), {
        status: 404,
        headers: corsHeaders,
      })
    }

    return new Response(JSON.stringify(note), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error getting note by ID:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Update Note
app.patch('/:connectionId/notes/:noteId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const noteId = c.req.param('noteId')
    const { note_body, timestamp } = await c.req.json<{
      note_body?: string
      timestamp?: string
    }>()

    const response = await updateHubSpotNote(connectionId, noteId, { note_body, timestamp })

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error updating note:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Delete Note
app.delete('/:connectionId/notes/:noteId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const noteId = c.req.param('noteId')

    const response = await deleteHubSpotNote(connectionId, noteId)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error deleting note:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Search Notes
app.post('/:connectionId/notes/search', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const { query, limit, after } = await c.req.json<{
      query: string
      limit?: number
      after?: string
    }>()

    if (!query) {
      return new Response(JSON.stringify({ error: 'Search query is required' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const results = await searchHubSpotNotes(connectionId, query, limit, after)
    return new Response(JSON.stringify(results), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error searching notes:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Associate Note with Record
app.put('/:connectionId/notes/:noteId/associations/:toObjectType/:toObjectId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const noteId = c.req.param('noteId')
    const toObjectType = c.req.param('toObjectType') as 'contact' | 'company' | 'deal'
    const toObjectId = c.req.param('toObjectId')
    const { associationTypeId } = await c.req.json<{ associationTypeId?: number }>()

    if (!['contact', 'company', 'deal'].includes(toObjectType)) {
      return new Response(JSON.stringify({ error: 'Invalid object type' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const response = await associateNoteWithRecord(
      connectionId,
      noteId,
      toObjectType,
      toObjectId,
      associationTypeId,
    )

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error associating note:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Remove Note Association
app.delete('/:connectionId/notes/:noteId/associations/:toObjectType/:toObjectId', async c => {
  try {
    const user = await verifyAuth(c)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    const connectionId = c.req.param('connectionId')
    const noteId = c.req.param('noteId')
    const toObjectType = c.req.param('toObjectType') as 'contact' | 'company' | 'deal'
    const toObjectId = c.req.param('toObjectId')
    const { associationTypeId } = await c.req.json<{ associationTypeId?: number }>()

    if (!['contact', 'company', 'deal'].includes(toObjectType)) {
      return new Response(JSON.stringify({ error: 'Invalid object type' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    const response = await removeNoteAssociation(
      connectionId,
      noteId,
      toObjectType,
      toObjectId,
      associationTypeId,
    )

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Error removing note association:', message)
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: corsHeaders,
    })
  }
})

// Add CORS headers middleware for all routes
app.use('*', async (c, next) => {
  // Handle preflight requests
  if (c.req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    })
  }

  await next()

  // Add CORS headers to the response
  Object.entries(corsHeaders).forEach(([key, value]) => {
    c.res.headers.set(key, value)
  })
})

Deno.serve(app.fetch)
