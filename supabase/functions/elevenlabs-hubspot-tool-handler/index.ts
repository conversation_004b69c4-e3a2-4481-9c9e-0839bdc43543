import { corsHeaders } from '../_shared/cors.ts'
// Potentially verifyAuth if needed, or a specific API key for ElevenLabs
// import { verifyAuth } from '../_shared/utils.ts'

interface ToolCallRequest {
  tool_name: string
  parameters: Record<string, unknown> // Parameters extracted by ElevenLabs LLM
  // We'll need organization_id and crm_connection_id to route to the correct HubSpot instance
  organization_id?: string
  crm_connection_id?: string
}

interface ToolCallResponse {
  success: boolean
  data?: unknown
  error?: {
    message: string
    details?: unknown
  }
}

// Main Deno serve function
Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 204 })
  }

  let toolCallRequest: ToolCallRequest
  try {
    toolCallRequest = await req.json()
  } catch (e: unknown) {
    // Changed e to e: unknown
    const message = e instanceof Error ? e.message : 'Invalid JSON request body'
    return new Response(
      JSON.stringify({ success: false, error: { message } }), // Use the extracted message
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  }

  const {
    tool_name,
    parameters,
    organization_id: _organization_id,
    crm_connection_id,
  } = toolCallRequest
  // Crucial: Get connectionId for CRM operations.
  // This might come directly, or you might need to look it up via organization_id
  // For now, assume crm_connection_id is passed or can be derived.
  // If your Supabase functions called by crm.service.ts (e.g., in crm/index.ts) already handle
  // auth and getting connection_id based on the user/org making the request, then
  // this handler might not need crm_connection_id explicitly if it invokes those functions correctly.
  // However, the crm/index.ts routes are /:connectionId/... so connectionId is key.
  // The ElevenLabs tool call parameters will need to ensure crm_connection_id is included.

  if (!crm_connection_id) {
    return new Response(
      JSON.stringify({ success: false, error: { message: 'crm_connection_id is required' } }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  }

  const supabaseUrlBase = Deno.env.get('SUPABASE_URL') // Or construct as needed
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') // For calling other Supabase functions securely

  let responseData: ToolCallResponse = {
    success: false,
    error: { message: 'Internal server error: Response data not set.' },
  }

  try {
    let endpoint = ''
    let method = 'POST' // Default, change as needed
    let body: Record<string, unknown> | null = parameters // By default, pass all params

    switch (tool_name) {
      // CONTACTS
      case 'hubspot_create_contact': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/contacts`
        // body already contains contact properties from parameters
        break
      }
      case 'hubspot_get_contact_by_id': {
        method = 'GET'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/contacts/${parameters.contactId}`
        if (parameters.force_fetch) endpoint += '?force_fetch=true'
        body = null // GET request
        break
      }
      case 'hubspot_search_contacts': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/contacts/search`
        body = { query: parameters.query, limit: parameters.limit, after: parameters.after }
        break
      }
      case 'hubspot_update_contact': {
        method = 'PATCH'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/contacts/${parameters.contactId}`
        const { contactId: _updateContactId, ...updateContactData } = parameters
        body = updateContactData
        break
      }
      case 'hubspot_delete_contact': {
        method = 'DELETE'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/contacts/${parameters.contactId}`
        body = null
        break
      }

      // COMPANIES
      case 'hubspot_create_company': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/companies`
        break
      }
      case 'hubspot_get_company_by_id': {
        method = 'GET'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/companies/${parameters.companyId}`
        if (parameters.force_fetch) endpoint += '?force_fetch=true'
        body = null
        break
      }
      case 'hubspot_search_companies': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/companies/search`
        body = { query: parameters.query, limit: parameters.limit, after: parameters.after }
        break
      }
      case 'hubspot_update_company': {
        method = 'PATCH'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/companies/${parameters.companyId}`
        const { companyId: _updateCompanyId, ...updateCompanyData } = parameters
        body = updateCompanyData
        break
      }
      case 'hubspot_delete_company': {
        method = 'DELETE'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/companies/${parameters.companyId}`
        body = null
        break
      }

      // DEALS
      case 'hubspot_create_deal': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/deals`
        break
      }
      case 'hubspot_get_deal_by_id': {
        method = 'GET'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/deals/${parameters.dealId}`
        if (parameters.force_fetch) endpoint += '?force_fetch=true'
        body = null
        break
      }
      case 'hubspot_search_deals': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/deals/search`
        body = { query: parameters.query, limit: parameters.limit, after: parameters.after }
        break
      }
      case 'hubspot_update_deal': {
        method = 'PATCH'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/deals/${parameters.dealId}`
        const { dealId: _updateDealId, ...updateDealData } = parameters // remove dealId from body
        body = updateDealData
        break
      }
      case 'hubspot_delete_deal': {
        method = 'DELETE'
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/deals/${parameters.dealId}`
        body = null
        break
      }

      // ENGAGEMENTS
      case 'hubspot_log_note': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/engagements/note`
        body = {
          note_body: parameters.note_body,
          timestamp: parameters.timestamp,
          associations: parameters.associations, // e.g. [{toObjectType: 'contact', toObjectId: '123'}]
        }
        break
      }
      case 'hubspot_log_call': {
        endpoint = `${supabaseUrlBase}/functions/v1/crm/${crm_connection_id}/engagements/call`
        body = {
          body: parameters.body,
          direction: parameters.direction,
          durationMs: parameters.durationMs,
          status: parameters.status,
          title: parameters.title,
          timestamp: parameters.timestamp,
          associations: parameters.associations,
        }
        break
      }

      default: {
        responseData = { success: false, error: { message: `Unknown tool_name: ${tool_name}` } }
        // No early return here, let the main response handling do its job.
        break // Exit switch
      }
    }

    // Only attempt to fetch if a valid endpoint was determined (i.e., not the default case)
    if (endpoint) {
      const fetchOptions: RequestInit = {
        method: method,
        headers: {
          Authorization: `Bearer ${serviceRoleKey ?? ''}`, // Securely call other Supabase functions
          'Content-Type': 'application/json',
          'X-Internal-Request': 'true', // Add this header
          apikey: serviceRoleKey ?? '', // Supabase functions often also check apikey header
        },
      }

      if (body) {
        fetchOptions.body = JSON.stringify(body)
      }

      const internalResponse = await fetch(endpoint, fetchOptions)
      const internalResponseData = await internalResponse.json()

      if (!internalResponse.ok) {
        // Forward the error from the crm function
        responseData = {
          success: false,
          error: {
            message: `Error calling internal CRM function ${tool_name}`,
            details: internalResponseData,
          },
        }
      } else {
        responseData = { success: true, data: internalResponseData }
      }
    }
    // The 'else if (!responseData)' block is removed as responseData is now initialized.
    // If the 'default' case in the switch was hit, responseData would have been set there.
    // If 'endpoint' was empty for any other reason and 'default' wasn't hit (which shouldn't happen with current logic),
    // the initialized 'responseData' would be used.
  } catch (e: unknown) {
    // Changed e to e: unknown
    const message = e instanceof Error ? e.message : 'An unknown error occurred'
    console.error(`Error processing tool ${tool_name}:`, e)
    responseData = {
      success: false,
      error: { message: `Server error processing ${tool_name}: ${message}` }, // Use the extracted message
    }
    return new Response(JSON.stringify(responseData), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }

  return new Response(JSON.stringify(responseData), {
    status: responseData.success
      ? 200
      : (responseData.error?.details as { status?: number })?.status || 500, // try to use status from downstream error
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  })
})
