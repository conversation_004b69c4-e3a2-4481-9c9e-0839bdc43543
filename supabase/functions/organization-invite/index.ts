// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { serve } from 'https://deno.land/std@0.224.0/http/server.ts'
import { createClient } from '@supabase/supabase-js'
import { Resend } from 'resend'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY')
const APP_URL = Deno.env.get('APP_URL') ?? 'http://localhost:3000'
const RESEND_EMAIL = Deno.env.get('RESEND_EMAIL')

if (!RESEND_API_KEY) {
  throw new Error('Missing RESEND_API_KEY environment variable')
}

const resend = new Resend(RESEND_API_KEY)
const supabase = createClient(SUPABASE_URL!, SUPABASE_ANON_KEY!)
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json',
}

const handler = async (req: Request): Promise<Response> => {
  try {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
      return new Response('ok', { headers: corsHeaders })
    }

    // Get request data
    const { organizationId, email, role } = await req.json()
    if (!organizationId || !email || !role) {
      return new Response(JSON.stringify({ error: 'Missing required parameters' }), {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Get organization details
    const { data: organization } = await supabase
      .from('organizations')
      .select('name')
      .eq('id', organizationId)
      .single()

    if (!organization) {
      return new Response(JSON.stringify({ error: 'Organization not found' }), {
        status: 404,
        headers: corsHeaders,
      })
    }

    // Get the authenticated user
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const {
      data: { user },
    } = await supabase.auth.getUser(token)

    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: corsHeaders,
      })
    }

    // Create invite
    const { data: invite, error: inviteError } = await supabase
      .from('organization_invites')
      .insert({
        organization_id: organizationId,
        email,
        role,
        invited_by: user.id,
      })
      .select()
      .single()

    if (inviteError) {
      console.error('Invite error:', inviteError)
      return new Response(JSON.stringify({ error: 'Failed to create invite' }), {
        status: 500,
        headers: corsHeaders,
      })
    }

    // Generate invite URL
    const inviteUrl = `${APP_URL}/invite/${invite.id}`

    // Send invitation email
    const { data: emailData, error: emailError } = await resend.emails.send({
      from: `Foundation <${RESEND_EMAIL}>`,
      to: email,
      subject: `Invitation to join ${organization.name}`,
      html: `
        <h2>You've been invited to join ${organization.name}</h2>
        <p>You've been invited to join the organization with the role of ${role}.</p>
        <a href="${inviteUrl}" style="
          display: inline-block;
          padding: 10px 20px;
          background-color: #0070f3;
          color: white;
          text-decoration: none;
          border-radius: 5px;
          margin: 20px 0;
        ">Accept Invitation</a>
      `,
    })

    if (emailError) {
      console.error('Email error:', emailError)
      return new Response(JSON.stringify({ error: 'Failed to send invitation email' }), {
        status: 500,
        headers: corsHeaders,
      })
    }

    return new Response(
      JSON.stringify({
        message: 'Invitation sent successfully',
        data: { invite, email: emailData },
      }),
      { status: 200, headers: corsHeaders },
    )
  } catch (error) {
    console.error('Handler error:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: corsHeaders,
    })
  }
}
serve(handler)
