{"version": "4", "specifiers": {"jsr:@supabase/functions-js@*": "2.4.4", "npm:@types/node@*": "22.12.0", "npm:openai@^4.52.5": "4.95.0"}, "jsr": {"@supabase/functions-js@2.4.4": {"integrity": "38456509a6e22fb116b118464cbb36357256f9048d3580632b63af91f63769f7", "dependencies": ["npm:openai"]}}, "npm": {"@types/node-fetch@2.6.12": {"integrity": "sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==", "dependencies": ["@types/node@22.12.0", "form-data"]}, "@types/node@18.19.86": {"integrity": "sha512-fifKayi175wLyKyc5qUfyENhQ1dCNI1UNjp653d8kuYcPQN5JhX3dGuP/XmvPTg/xRBn1VTLpbmi+H/Mr7tLfQ==", "dependencies": ["undici-types@5.26.5"]}, "@types/node@22.12.0": {"integrity": "sha512-Fll2FZ1riMjNmlmJOdAyY5pUbkftXslB5DgEzlIuNaiWhXd00FhWxVC/r4yV/4wBb9JfImTu+jiSvXTkJ7F/gA==", "dependencies": ["undici-types@6.20.0"]}, "abort-controller@3.0.0": {"integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dependencies": ["event-target-shim"]}, "agentkeepalive@4.6.0": {"integrity": "sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==", "dependencies": ["humanize-ms"]}, "asynckit@0.4.0": {"integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "call-bind-apply-helpers@1.0.2": {"integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": ["es-errors", "function-bind"]}, "combined-stream@1.0.8": {"integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": ["delayed-stream"]}, "delayed-stream@1.0.0": {"integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "dunder-proto@1.0.1": {"integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": ["call-bind-apply-helpers", "es-errors", "gopd"]}, "es-define-property@1.0.1": {"integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="}, "es-errors@1.3.0": {"integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="}, "es-object-atoms@1.1.1": {"integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": ["es-errors"]}, "es-set-tostringtag@2.1.0": {"integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "dependencies": ["es-errors", "get-intrinsic", "has-tostringtag", "hasown"]}, "event-target-shim@5.0.1": {"integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ=="}, "form-data-encoder@1.7.2": {"integrity": "sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A=="}, "form-data@4.0.2": {"integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "dependencies": ["asynckit", "combined-stream", "es-set-tostringtag", "mime-types"]}, "formdata-node@4.4.1": {"integrity": "sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==", "dependencies": ["node-domexception", "web-streams-polyfill"]}, "function-bind@1.1.2": {"integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "get-intrinsic@1.3.0": {"integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": ["call-bind-apply-helpers", "es-define-property", "es-errors", "es-object-atoms", "function-bind", "get-proto", "gopd", "has-symbols", "hasown", "math-intrinsics"]}, "get-proto@1.0.1": {"integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": ["dunder-proto", "es-object-atoms"]}, "gopd@1.2.0": {"integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}, "has-symbols@1.1.0": {"integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="}, "has-tostringtag@1.0.2": {"integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "dependencies": ["has-symbols"]}, "hasown@2.0.2": {"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": ["function-bind"]}, "humanize-ms@1.2.1": {"integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==", "dependencies": ["ms"]}, "math-intrinsics@1.1.0": {"integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="}, "mime-db@1.52.0": {"integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types@2.1.35": {"integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": ["mime-db"]}, "ms@2.1.3": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node-domexception@1.0.0": {"integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ=="}, "node-fetch@2.7.0": {"integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dependencies": ["whatwg-url"]}, "openai@4.95.0": {"integrity": "sha512-tWHLTA+/HHyWlP8qg0mQLDSpI2NQLhk6zHLJL8yb59qn2pEI8rbEiAGSDPViLvi3BRDoQZIX5scaJ3xYGr2nhw==", "dependencies": ["@types/node@18.19.86", "@types/node-fetch", "abort-controller", "agentkeepalive", "form-data-encoder", "formdata-node", "node-fetch"]}, "tr46@0.0.3": {"integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "undici-types@5.26.5": {"integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="}, "undici-types@6.20.0": {"integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg=="}, "web-streams-polyfill@4.0.0-beta.3": {"integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug=="}, "webidl-conversions@3.0.1": {"integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "whatwg-url@5.0.0": {"integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": ["tr46", "webidl-conversions"]}}, "redirects": {"https://esm.sh/@selderee/plugin-htmlparser2@^0.11.0?target=denonext": "https://esm.sh/@selderee/plugin-htmlparser2@0.11.0?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@types/react-dom@~19.1.2/server.d.ts": "https://esm.sh/@types/react-dom@19.1.2/server.d.ts", "https://esm.sh/@types/react@~19.1.0/index.d.ts": "https://esm.sh/@types/react@19.1.2/index.d.ts", "https://esm.sh/@types/react@~19.1.0/jsx-runtime.d.ts": "https://esm.sh/@types/react@19.1.2/jsx-runtime.d.ts", "https://esm.sh/@types/react@~19.1.2/index.d.ts": "https://esm.sh/@types/react@19.1.2/index.d.ts", "https://esm.sh/@types/ws@~8.5.14/index.d.mts": "https://esm.sh/@types/ws@8.5.14/index.d.mts", "https://esm.sh/bufferutil@^4.0.1?target=denonext": "https://esm.sh/bufferutil@4.0.9?target=denonext", "https://esm.sh/deepmerge@^4.3.1?target=denonext": "https://esm.sh/deepmerge@4.3.1?target=denonext", "https://esm.sh/dom-serializer@^2.0.0?target=denonext": "https://esm.sh/dom-serializer@2.0.0?target=denonext", "https://esm.sh/domelementtype@^2.3.0?target=denonext": "https://esm.sh/domelementtype@2.3.0?target=denonext", "https://esm.sh/domhandler@^5.0.3?target=denonext": "https://esm.sh/domhandler@5.0.3?target=denonext", "https://esm.sh/domutils@^3.0.1?target=denonext": "https://esm.sh/domutils@3.2.2?target=denonext", "https://esm.sh/entities@^4.2.0?target=denonext": "https://esm.sh/entities@4.5.0?target=denonext", "https://esm.sh/entities@^4.4.0/lib/decode?target=denonext": "https://esm.sh/entities@4.5.0/lib/decode?target=denonext", "https://esm.sh/htmlparser2@^8.0.2?target=denonext": "https://esm.sh/htmlparser2@8.0.2?target=denonext", "https://esm.sh/leac@^0.6.0?target=denonext": "https://esm.sh/leac@0.6.0?target=denonext", "https://esm.sh/node-gyp-build@^4.3.0?target=denonext": "https://esm.sh/node-gyp-build@4.8.4?target=denonext", "https://esm.sh/parseley@^0.12.0?target=denonext": "https://esm.sh/parseley@0.12.1?target=denonext", "https://esm.sh/peberminta@^0.9.0?target=denonext": "https://esm.sh/peberminta@0.9.0?target=denonext", "https://esm.sh/react-dom@^19.2.0-canary-ea05b750-20250408/server?target=denonext": "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/server?target=denonext", "https://esm.sh/react@^19.2.0-canary-ea05b750-20250408/jsx-runtime?target=denonext": "https://esm.sh/react@19.2.0-canary-ea05b750-20250408/jsx-runtime?target=denonext", "https://esm.sh/react@^19.2.0-canary-ea05b750-20250408?target=denonext": "https://esm.sh/react@19.2.0-canary-ea05b750-20250408?target=denonext", "https://esm.sh/selderee@^0.11.0?target=denonext": "https://esm.sh/selderee@0.11.0?target=denonext", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/utf-8-validate@%3E=5.0.2?target=denonext": "https://esm.sh/utf-8-validate@6.0.5?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext", "https://esm.sh/ws@^8.18.0?target=denonext": "https://esm.sh/ws@8.18.1?target=denonext"}, "remote": {"https://esm.sh/@react-email/render@1.0.6/denonext/render.mjs": "0fb8c975e744c4140364bae520dc183a24387212f33d18f74d6fb5380d5e06fb", "https://esm.sh/@selderee/plugin-htmlparser2@0.11.0/denonext/plugin-htmlparser2.mjs": "bf215c5e29a046da720be2ba856316197ccd53291f42030e947f3df49d8a93e4", "https://esm.sh/@selderee/plugin-htmlparser2@0.11.0?target=denonext": "1b642b94e690ea7e82b72aab470d89548cc2e32fd6a1603afec94f67c800b7db", "https://esm.sh/@supabase/auth-js@2.69.1/denonext/auth-js.mjs": "fb31c3925437753f5a8a90fc57ea24dc5b68b2b295e696123b1b6a635b7b3ada", "https://esm.sh/@supabase/functions-js@2.4.4/denonext/functions-js.mjs": "7adeb257410ef3c4a8a1eb9b4ff416c0075d1c32860ca04913c8a9dace1de6a6", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.19.4/denonext/postgrest-js.mjs": "2073b5552ba10c7a8302bffffae771e3aede1daf833382355dae239fb0ab2576", "https://esm.sh/@supabase/realtime-js@2.11.2/denonext/realtime-js.mjs": "c33ac375b6be89c893f9df844d2525a4ace015a35aa6ba236270d00c6605c7ba", "https://esm.sh/@supabase/storage-js@2.7.1/denonext/storage-js.mjs": "73ac8cdc95cfcd794fe603dbd7ce06d539ab51538ae6467eabe0f9cc26c993aa", "https://esm.sh/@supabase/supabase-js@2.49.4": "d52c4d06946766d328fdd0ac2e007f52bb6d2ef7ce6103ad9f0f57d92b73e978", "https://esm.sh/@supabase/supabase-js@2.49.4/denonext/supabase-js.mjs": "8c664dda021a5abc7c0b1f49d89d5886a7f9c63c9d365eb3764e1e27440bd781", "https://esm.sh/bufferutil@4.0.9/denonext/bufferutil.mjs": "13dca4d5bb2c68cbe119f880fa3bd785b9a81a8e02e0834dae604b4b85295cd8", "https://esm.sh/bufferutil@4.0.9?target=denonext": "e32574569ab438facfcc3f412c659b0719bbf05477136ca176938c9a3ac45125", "https://esm.sh/deepmerge@4.3.1/denonext/deepmerge.mjs": "1f448aaa83a52514faecb2edc02eb766c9eb10b935b9ac4c1c2a877a0bb56b80", "https://esm.sh/deepmerge@4.3.1?target=denonext": "a8faef33b536441a2c63f8c967d160848eb1f898a8e83a35a9f0623ecd9063d3", "https://esm.sh/dom-serializer@2.0.0/denonext/dom-serializer.mjs": "545028b1d2c25bae5cbfe6930a28a2e4f7f05e1a0d09bbd0f3f5f9a33df8e3bd", "https://esm.sh/dom-serializer@2.0.0?target=denonext": "1626b2b8326556ea2816b5f9bf7522bc9581d545fd9ad117c066ab7a5ff1fb89", "https://esm.sh/domelementtype@2.3.0/denonext/domelementtype.mjs": "4f3b57348729cd517560139eb1969ca2fe9cc58c5188abe56e7336d5cb557cc0", "https://esm.sh/domelementtype@2.3.0?target=denonext": "2beb2a1e3d18892a9b00ef9528811b93f613a77d2b6fb25376ec0f109ac48a4f", "https://esm.sh/domhandler@5.0.3/denonext/domhandler.mjs": "3fb258a3d79bc9066a568bb6b09ce946d1fcfa2636a24ae80a4db220956e0873", "https://esm.sh/domhandler@5.0.3?target=denonext": "298fde249b7bff9e80667cfe643e7d4b390871b77b0928d086ce4c0b8fc570e2", "https://esm.sh/domutils@3.2.2/denonext/domutils.mjs": "f0b4e80e73810ed6f3d8c4e1822feef89208f32c88b6024a84328d02f5f77c40", "https://esm.sh/domutils@3.2.2?target=denonext": "7e487176c61dfd1dfdbcfd1195e7329a64f53421511561b69c570a6cff0a6167", "https://esm.sh/entities@4.5.0/denonext/entities.mjs": "4a9306e4021ae1079e83b5db26e1678c536fa69c8f2839802bc3cc43282cef08", "https://esm.sh/entities@4.5.0/denonext/lib/decode.mjs": "ef22e25f6bca668e40c4f7d4ecaebe2172a833a18372d55b54f997d0d8702dcd", "https://esm.sh/entities@4.5.0/denonext/lib/escape.mjs": "116aef78e5ff05efa6f79851b8b59da025ab88f5c25d2262f73df98f4d57c3fa", "https://esm.sh/entities@4.5.0/lib/decode?target=denonext": "488bc8401a0c85a76527d61a41352c5371904aeda57a136eb10ccfadcd2f7c8c", "https://esm.sh/entities@4.5.0?target=denonext": "f6bc559c07f40e94b3ef50f0b24e2666a2258db3b6697bf4da8fd2fc014ef7a1", "https://esm.sh/html-to-text@9.0.5/denonext/html-to-text.mjs": "6d781c4b144d75deab4b13f72d449cf9a562e924568a57217b28bb459506d775", "https://esm.sh/htmlparser2@8.0.2/denonext/htmlparser2.mjs": "c0be0f190e625b82e88378875016f820a38d586e9c885d37e3dd2073a4f0fdfb", "https://esm.sh/htmlparser2@8.0.2/denonext/lib/esm/Parser.mjs": "d58fb2f87f8fead8e7ac03c544690908b4db21ab0513415b0cfe9311ab31aaa3", "https://esm.sh/htmlparser2@8.0.2/denonext/lib/esm/Tokenizer.mjs": "09109e601c7acd75b99c2a34e53e339a46301d9937e1495eaca8f9019a7b3040", "https://esm.sh/htmlparser2@8.0.2?target=denonext": "fd3edaa58a00e79f11b3510cc3930c9f5345486cdcc52c0afe24bbb36aece028", "https://esm.sh/leac@0.6.0/denonext/leac.mjs": "e8c75e599987aa753dd83b68150eae015e87066834f78748e08b52a101d1730f", "https://esm.sh/leac@0.6.0?target=denonext": "49d9cd25ff3a0658f687f430fb60ae79811a6ba3db8f6d2d243274d789ff6013", "https://esm.sh/node-gyp-build@4.8.4/denonext/node-gyp-build.mjs": "9a86f2d044fc77bd60aaa3d697c2ba1b818da5fb1b9aaeedec59a40b8e908803", "https://esm.sh/node-gyp-build@4.8.4?target=denonext": "261a6cedf1fdbf159798141ba1e2311ac1510682c5c8b55dacc8cf5fdee4aa06", "https://esm.sh/parseley@0.12.1/denonext/parseley.mjs": "adb641dd0790c5f80caccae1d12029aa5434498368eea27696e9e3d5dcd0b835", "https://esm.sh/parseley@0.12.1?target=denonext": "8169d6a6a0345226dc65fed2b195413c2bc863348afa61ce31f5bf4730299a1a", "https://esm.sh/peberminta@0.9.0/denonext/lib/util.mjs": "ab48ab5eead902341c9f2209db7881d58f9ad2eeac75d62b02bd8fc3c2b96df5", "https://esm.sh/peberminta@0.9.0/denonext/peberminta.mjs": "b9e30d34531689bfa87bdd584c19732de2b40a7fb9c87a8dd083b24ccce9199f", "https://esm.sh/peberminta@0.9.0?target=denonext": "021e477cef449a6078dec84f778954407b9a913315dc72e5c42eee18db590843", "https://esm.sh/prettier@3.5.3/denonext/doc.mjs": "348842240cd67b74474fb2ea86c5ef391a611413cc2cfc46d9400c5b4da52a6a", "https://esm.sh/prettier@3.5.3/denonext/plugins/acorn.mjs": "7207618afc87f4c55c73369c0dd929d0ad5a4a7c976a7b282c36d60cdec94ef5", "https://esm.sh/prettier@3.5.3/denonext/plugins/angular.mjs": "8e639df1b31b85601cd083f1b0be56ab76f25e7ac26d6109d984d5be214b456b", "https://esm.sh/prettier@3.5.3/denonext/plugins/babel.mjs": "140c19132cde7b35b9e19af588c22233486f137e32d4d3237682184c043ce2b7", "https://esm.sh/prettier@3.5.3/denonext/plugins/estree.mjs": "9548887fb3c2a1c864bdb44d9660dc95e8b11a03a85b51ba9fed8a8eedfa6768", "https://esm.sh/prettier@3.5.3/denonext/plugins/flow.mjs": "002a24d3b6451056c48bbc2cc2091ddba3d6d42257e326da7649133af85ac477", "https://esm.sh/prettier@3.5.3/denonext/plugins/glimmer.mjs": "380e3bebc7b8d18c400c04e515586f4581e8f3eca24d7cf89ef44330bba7991a", "https://esm.sh/prettier@3.5.3/denonext/plugins/graphql.mjs": "6882e686309f81e817a158ce746b81ce73bb0804e69e3d0aea529ab58ecb4489", "https://esm.sh/prettier@3.5.3/denonext/plugins/html.mjs": "2a127ef535ae3cfe9dc898b20f72ee6b2ba4fc1c6142dfc04f120408b556ba79", "https://esm.sh/prettier@3.5.3/denonext/plugins/markdown.mjs": "a56151f9a5f13c47d352ce1b9d50dbee7a39af90d734e7d67814b356ba9f2b64", "https://esm.sh/prettier@3.5.3/denonext/plugins/meriyah.mjs": "c29c5732d4bcce653d9ae3d93b1d83df2ecb6609e1f57ab8b31e76074e5cc990", "https://esm.sh/prettier@3.5.3/denonext/plugins/postcss.mjs": "0ef7b72ddffbf979544ba46b75e58daa1646d8611fea6f6bd02696cf272eef74", "https://esm.sh/prettier@3.5.3/denonext/plugins/typescript.mjs": "9c70d792773acd8e5cd20294e3ea09500f93f7b32c4cdc611efe88ee6aab1927", "https://esm.sh/prettier@3.5.3/denonext/plugins/yaml.mjs": "fdc98af53cfbc9ff58926be0a386ec34a21b4c8a9a42d8d444a10aa86a7bd9f9", "https://esm.sh/prettier@3.5.3/denonext/prettier.mjs": "ccbadd036440d2086e269c6d32399328f85f23c788cc9dec4ef262efaf9b46d1", "https://esm.sh/prettier@3.5.3/denonext/standalone.mjs": "8550e87e6cd225dd9d4b9ce7319b7fd87cabe7a3e5de06dcaed1de5e9e6b5d0c", "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/denonext/cjs/react-dom-server-legacy.browser.production.mjs": "262d6822b88b38a2126912790f42c35ec800eadd8bede9f02b8a00edf312f5a2", "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/denonext/cjs/react-dom-server.browser.production.mjs": "350c7a0fa1655b24d6d60f6f019a9367c332786ac5f6b490a048f4c5c646abab", "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/denonext/react-dom.mjs": "e4817a347e0e4857eab9e1bd8f6cce059928150308984af1e744b75df351e3b6", "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/denonext/server.mjs": "6641fe4a2ff1aae676348cab2104987b0c97a7bae08ee66b3e240e6c4e5adc73", "https://esm.sh/react-dom@19.2.0-canary-ea05b750-20250408/server?target=denonext": "6990eee004db7f1ac0e99d6cd6e7494144e40898a34da3f0069dee8144d1193c", "https://esm.sh/react@19.2.0-canary-ea05b750-20250408/denonext/jsx-runtime.mjs": "72412381278d0c2d4ef9637b3bcfb3099d39e584af0795b3bbd1352ef873c687", "https://esm.sh/react@19.2.0-canary-ea05b750-20250408/denonext/react.mjs": "9206aa5ab82aeb94420f8d21e40cdc5583b05175259aa93d2881627cdec86e0b", "https://esm.sh/react@19.2.0-canary-ea05b750-20250408/jsx-runtime?target=denonext": "aae3e8f81e0b4bfe64b5f2b7c2328b3ba16b7a04ad49f84eb6233e6f2a2ca53b", "https://esm.sh/react@19.2.0-canary-ea05b750-20250408?target=denonext": "ff9397421b8133769535c4812b1992235d2876eb545cbb197b598b300aa6d05b", "https://esm.sh/resend@4.3.0": "c2e0995c26a36c065678e6ad2001943923dcfdf578f8614b7641fa3ba2f6c0fe", "https://esm.sh/resend@4.3.0/denonext/resend.mjs": "ccce3d620f8526d00222acb0b9e99c56ac19d9c049c534c28381da0bad306103", "https://esm.sh/selderee@0.11.0/denonext/selderee.mjs": "7cd55f556e21df07f094dc04a9a3b19f56f4b0781c92aebfb5fbda7536fc167d", "https://esm.sh/selderee@0.11.0?target=denonext": "30cf39b226588a56ac350c58aa59db26137ad7a6c609f02689718bbfd389a618", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/utf-8-validate@6.0.5/denonext/utf-8-validate.mjs": "66b8ea532a0c745068f5b96ddb1bae332c3036703243541d2e89e66331974d98", "https://esm.sh/utf-8-validate@6.0.5?target=denonext": "071bc33ba1a58297e23a34d69dd589fd06df04b0f373b382ff5da544a623f271", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d", "https://esm.sh/ws@8.18.1/denonext/ws.mjs": "732cae76ba0acb311a561003d2f7ef569293cb9159d67dd800ab346b84f80432", "https://esm.sh/ws@8.18.1?target=denonext": "e99b670fc49b38e15a7576ddcd5bb01e123fe9b3a017db7f97898127811b4e27"}}