// deno-lint-ignore-file no-explicit-any
// Setup type definitions for built-in Supabase Runtime APIs
import 'jsr:@supabase/functions-js/edge-runtime.d.ts'
import { Hono, Context } from 'jsr:@hono/hono'
import { corsHeaders } from '../_shared/cors.ts' // Import shared CORS headers
import {
  createElevenLabsAgent,
  updateElevenLabsAgent,
  deleteElevenLabsAgent,
  parseElevenLabsError,
  getAgentSignedUrl,
  getAllVoices,
  createKnowledgeBaseText,
  createKnowledgeBaseUrl,
  createKnowledgeBaseFile,
  deleteKnowledgeBase,
  knowledgeBaseRagIndexStatus,
} from './lib/elevenlabs_api.ts'
import {
  getServiceSupabaseClient,
  insertAgentRecord,
  updateAgentRecord,
  deleteAgentRecord,
  createKnowledgeBase,
  deleteKnowledgeBaseRecord,
  getKnowledgeBaseRecord,
  upsertAgentKnowledgeBase,
} from './lib/supabase_db.ts'
// Import actual payload mapping functions
import { mapToCreatePayload, mapToUpdatePayload } from './lib/payload_mapper.ts'
// Import Auth functions and Error type
import {
  authenticateUser,
  authorizeOrganizationAdmin,
  getUserSupabaseClient, // Needed for authorization check
} from './lib/auth.ts'
import { HttpError } from './lib/errors.ts'
import { parseAndValidateRequest } from './lib/validation.ts' // Import the validation function
// Assuming types are defined in types.ts - adjust if necessary
import { AuthenticatedUser } from './lib/types.ts'

// Set the base path for the function name
const functionName = 'manage-elevenlabs-agent'
const app = new Hono().basePath(`/${functionName}`)

// --- Main POST Handler ---
// The route is now relative to the basePath, so '/' is correct
app.post('/', async (c: Context) => {
  console.log('Entering POST handler...') // Add log here
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const _user = await authenticateUser(c.req.raw) // Prefixed user with _
    const authHeader = c.req.raw.headers.get('Authorization')!

    // --- 2. Parse and Validate Request Body ---
    // This function now handles JSON parsing and validation, throwing HttpError on failure.
    const {
      action,
      agent_id: existingAgentId,
      config,
      organization_id,
    } = await parseAndValidateRequest(c.req.raw)

    // --- 3. Authorize User for Organization ---
    // Note: Step numbers adjusted after removing validation block
    const userSupabaseClient = getUserSupabaseClient(authHeader)
    await authorizeOrganizationAdmin(userSupabaseClient, _user.id, organization_id) // Used _user

    // --- 4. Get Service Role Client ---
    const serviceSupabaseClient = getServiceSupabaseClient()

    // --- 5. Perform Action ---
    let agentId: string | undefined = existingAgentId
    let elevenlabsResponse: any
    // config is already typed correctly via ValidatedPayload from parseAndValidateRequest
    const agentConfig = config // Type assertion might not be needed if ValidatedPayload is precise

    // --- CREATE ---
    if (action === 'create') {
      console.log(`Processing 'create' action for org ${organization_id}...`)
      // Type guard: Validation ensures config exists for 'create'
      if (!agentConfig) {
        throw new HttpError(
          500,
          'Internal error: config missing for create action despite validation.',
        )
      }
      try {
        const elPayload = mapToCreatePayload(agentConfig)
        elevenlabsResponse = await createElevenLabsAgent(elPayload)
        agentId = elevenlabsResponse.agent_id // Assign the newly created agent ID
        await insertAgentRecord(
          serviceSupabaseClient,
          agentId!,
          agentConfig,
          organization_id,
          _user as AuthenticatedUser, // Used _user
        )
      } catch (error: any) {
        console.error('Error during agent creation process:', error)
        const { status, message } = parseElevenLabsError(error)
        const finalMessage =
          message.startsWith('Unknown error') && error.message ? error.message : message
        const errorSource = message.startsWith('Unknown error') ? 'Supabase DB' : 'ElevenLabs API'
        // Revert to standard Response for error handling
        return new Response(
          JSON.stringify({
            error: `Failed to create agent (${errorSource})`,
            details: finalMessage,
          }),
          {
            status: status || 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
          },
        )
      }
      // --- UPDATE ---
    } else if (action === 'update') {
      console.log(`Processing 'update' action for agent ${agentId} in org ${organization_id}...`)
      if (!agentId) {
        // Should be caught by validation, but double-check
        return c.json({ error: 'agent_id is required for update action' }, 400)
      }
      // Type guard: Validation ensures config exists for 'update'
      if (!agentConfig) {
        throw new HttpError(
          500,
          'Internal error: config missing for update action despite validation.',
        )
      }
      try {
        const elPayload = mapToUpdatePayload(agentConfig)
        elevenlabsResponse = await updateElevenLabsAgent(agentId, elPayload)
        await updateAgentRecord(serviceSupabaseClient, agentId, agentConfig, organization_id)
        if (agentConfig.knowledge_bases) {
          await upsertAgentKnowledgeBase(
            serviceSupabaseClient,
            agentId,
            agentConfig.knowledge_bases?.map((kb: any) => kb.id) ?? [],
          )
        }
      } catch (error: any) {
        console.error(`Error during agent update process for ${agentId}:`, error)
        const { status, message } = parseElevenLabsError(error)
        const finalMessage =
          message.startsWith('Unknown error') && error.message ? error.message : message
        const errorSource = message.startsWith('Unknown error') ? 'Supabase DB' : 'ElevenLabs API'
        // Revert to standard Response for error handling
        return new Response(
          JSON.stringify({
            error: `Failed to update agent (${errorSource})`,
            details: finalMessage,
          }),
          {
            status: status || 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
          },
        )
      }
      // --- DELETE ---
    } else if (action === 'delete') {
      console.log(`Processing 'delete' action for agent ${agentId} in org ${organization_id}...`)
      if (!agentId) {
        // Should be caught by validation, but double-check
        return c.json({ error: 'agent_id is required for delete action' }, 400)
      }
      try {
        // Delete from ElevenLabs first
        elevenlabsResponse = await deleteElevenLabsAgent(agentId)

        // Delete from Supabase, even if ElevenLabs returned 404
        if (elevenlabsResponse?.status !== 404) {
          await deleteAgentRecord(serviceSupabaseClient, agentId, organization_id)
        } else {
          console.warn(
            `Agent ${agentId} not found in ElevenLabs (404), attempting Supabase delete anyway.`,
          )
          try {
            await deleteAgentRecord(serviceSupabaseClient, agentId, organization_id)
          } catch (dbError: any) {
            // Handle specific error during Supabase delete *after* a 404 from ElevenLabs
            console.error(
              `Error deleting agent ${agentId} from Supabase (after 404 from ElevenLabs):`,
              dbError,
            )
            let dbErrorMessage = 'Unknown database error'
            if (dbError instanceof Error) {
              dbErrorMessage = dbError.message
            } else {
              dbErrorMessage = String(dbError) // Fallback to string conversion
            }
            // Revert to standard Response for error handling
            return new Response(
              JSON.stringify({
                error: 'Agent not found in ElevenLabs, and failed to delete locally',
                details: dbErrorMessage,
              }),
              { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }, // Merge CORS headers
            )
          }
        }
      } catch (error: any) {
        // Handle non-404 errors from ElevenLabs or errors from Supabase delete (when EL delete succeeded)
        console.error(`Error during agent deletion process for ${agentId}:`, error)
        const { status, message } = parseElevenLabsError(error)
        const finalMessage =
          message.startsWith('Unknown error') && error.message ? error.message : message
        const errorSource = message.startsWith('Unknown error') ? 'Supabase DB' : 'ElevenLabs API'

        // Check if the error originated from Supabase DB *after* a successful ElevenLabs delete
        if (
          errorSource === 'Supabase DB' &&
          (!elevenlabsResponse || elevenlabsResponse.status !== 404)
        ) {
          // Revert to standard Response for error handling
          return new Response(
            JSON.stringify({
              error: 'Failed to delete agent locally after deleting from ElevenLabs',
              details: finalMessage,
            }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }, // Merge CORS headers
          )
        } else {
          // Otherwise, assume error is from ElevenLabs (non-404)
          // Revert to standard Response for error handling
          return new Response(
            JSON.stringify({
              error: `Failed to delete agent (${errorSource})`,
              details: finalMessage,
            }),
            {
              status: status || 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
            },
          )
        }
      }
      // --- INVALID ACTION ---
      // This case should now be caught by parseAndValidateRequest, but kept as a fallback.
    } else {
      // This path should theoretically not be reached if validation is exhaustive
      console.error(`Reached unexpected 'else' block for action: ${action}`)
      return c.json({ error: 'Invalid action specified' }, 400)
    }

    // --- 6. Return Success Response ---
    const finalAgentId = action === 'create' ? agentId : existingAgentId // Use newly created ID if applicable
    // Use standard Response for consistency, explicitly setting Content-Type
    return new Response(
      JSON.stringify({
        success: true,
        agent_id: finalAgentId,
        elevenlabs_response: elevenlabsResponse,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
      },
    )
  } catch (error: any) {
    // Single catch block for all errors within the main try
    // Check if it's a specific HttpError from auth/authz first
    if (error instanceof HttpError) {
      console.warn(`Caught HttpError: ${error.status} - ${error.message}`, error.cause || '')
      // Ensure details are included if available in the cause
      const details =
        error.cause && error.cause instanceof Error ? error.cause.message : String(error.cause)
      // Revert to standard Response for error handling
      return new Response(
        JSON.stringify({ error: error.message, details: details || undefined }),
        { status: error.status, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }, // Merge CORS headers
      )
    }

    // Handle any other unhandled errors (e.g., JSON parsing, unexpected issues in action logic)
    console.error('Unhandled error in main handler:', error)
    const generalErrorMessage =
      error instanceof Error ? error.message : 'An unexpected internal server error occurred'
    // Revert to standard Response for error handling
    return new Response(
      JSON.stringify({ error: 'Internal Server Error', details: generalErrorMessage }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }, // Merge CORS headers
    )
  }
})

app.get('/agents/:id/signed-url', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const _user = await authenticateUser(c.req.raw) // Prefixed user with _
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const agentId = c.req.param('id')
    if (!agentId) {
      return c.json({ error: 'Agent ID is required' }, 400)
    }
    // --- 2. Fetch Signed URL ---
    const signedUrlResponse = await getAgentSignedUrl(agentId)
    return new Response(JSON.stringify(signedUrlResponse), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching signed URL:', error)
    return c.json({ error: 'Failed to fetch signed URL' }, 500)
  }
})

app.get('/voices', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const _user = await authenticateUser(c.req.raw) // Prefixed user with _
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    // --- 2. Fetch Voices ---
    const voicesResponse = await getAllVoices()
    return new Response(JSON.stringify(voicesResponse), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voices:', error)
    return c.json({ error: 'Failed to fetch voices' }, 500)
  }
})

app.post('/knowledge-base/text', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const user = await authenticateUser(c.req.raw) // Kept user as it's used
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const body = await c.req.raw.json()
    const { organization_id, name, content } = body
    if (!organization_id || !name || !content) {
      return c.json({ error: 'Organization ID, Name and Content are required' }, 400)
    }

    const knowledgeBase = await createKnowledgeBaseText({ name, text: content })
    const serviceSupabaseClient = getServiceSupabaseClient()
    const data = await createKnowledgeBase(serviceSupabaseClient, {
      eleven_labs_id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: 'text',
      content: content,
      prompt_injectable: knowledgeBase.prompt_injectable,
      organization_id,
      user_id: user.id,
      metadata: knowledgeBase.metadata,
    })

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voice:', error)
    return c.json({ error: 'Failed to fetch voice' }, 500)
  }
})

app.post('/knowledge-base/url', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const user = await authenticateUser(c.req.raw) // Kept user as it's used
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const body = await c.req.raw.json()
    const { organization_id, url, name } = body // Added name here
    if (!organization_id || !url || !name) {
      // Added name check
      return c.json({ error: 'Organization ID, URL, and Name are required' }, 400)
    }

    const knowledgeBase = await createKnowledgeBaseUrl({ url, name }) // Pass name to API
    const serviceSupabaseClient = getServiceSupabaseClient()
    const data = await createKnowledgeBase(serviceSupabaseClient, {
      eleven_labs_id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: 'url',
      content: knowledgeBase.extracted_inner_html,
      prompt_injectable: knowledgeBase.prompt_injectable,
      organization_id,
      user_id: user.id,
      url,
      metadata: knowledgeBase.metadata,
    })

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voice:', error)
    return c.json({ error: 'Failed to fetch voice' }, 500)
  }
})

app.post(':id/knowledge-base/file', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const user = await authenticateUser(c.req.raw) // Kept user as it's used
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const organization_id = c.req.param('id')
    const body = await c.req.parseBody()
    const file = body['file'] as File // Type assertion
    const name = body['name'] as string // Type assertion and retrieval
    if (!organization_id || !file || !name) {
      // Added name check
      return c.json({ error: 'Organization ID, File, and Name are required' }, 400)
    }

    const knowledgeBase = await createKnowledgeBaseFile({ file, name }) // Pass name to API
    const serviceSupabaseClient = getServiceSupabaseClient()
    const data = await createKnowledgeBase(serviceSupabaseClient, {
      eleven_labs_id: knowledgeBase.id,
      name: knowledgeBase.name,
      type: 'file',
      content: knowledgeBase.extracted_inner_html,
      prompt_injectable: knowledgeBase.prompt_injectable,
      organization_id,
      user_id: user.id,
      metadata: knowledgeBase.metadata,
    })

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voice:', error)
    return c.json({ error: 'Failed to fetch voice' }, 500)
  }
})

app.delete('/knowledge-base/:id', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const _user = await authenticateUser(c.req.raw) // Prefixed user with _
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const id = c.req.param('id')
    const serviceSupabaseClient = getServiceSupabaseClient()
    const record = await getKnowledgeBaseRecord(serviceSupabaseClient, id)

    if (!record) {
      return c.json({ error: `Knowledge base with ID ${id} not found.` }, 404)
    }

    await deleteKnowledgeBase(record.eleven_labs_id)
    await deleteKnowledgeBaseRecord(serviceSupabaseClient, id)

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voice:', error)
    return c.json({ error: 'Failed to fetch voice' }, 500)
  }
})

app.get('/knowledge-base/:id/rag-index', async (c: Context) => {
  try {
    // --- 1. Authenticate User ---
    // Use c.req.raw for functions expecting the native Request object
    const _user = await authenticateUser(c.req.raw) // Prefixed user with _
    const _authHeader = c.req.raw.headers.get('Authorization')! // Prefixed authHeader with _
    const id = c.req.param('id')
    const serviceSupabaseClient = getServiceSupabaseClient()
    const record = await getKnowledgeBaseRecord(serviceSupabaseClient, id)

    if (!record) {
      return c.json({ error: `Knowledge base with ID ${id} not found.` }, 404)
    }

    const response = await knowledgeBaseRagIndexStatus(record.eleven_labs_id)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }, // Merge CORS headers
    })
  } catch (error: any) {
    console.error('Error fetching voice:', error)
    return c.json({ error: 'Failed to fetch voice' }, 500)
  }
})

// --- OPTIONS Handler ---
// Added for CORS preflight requests, using shared headers
app.options('*', _ => {
  return new Response(null, {
    status: 204, // No Content
    headers: corsHeaders,
  })
})

// --- CORS Middleware Removed ---
// CORS headers are now applied directly in each response within the main handler.
// The OPTIONS handler below handles preflight requests.

// Remove the separate OPTIONS handler as it's now handled in the middleware
// app.options('*', (_) => { ... }) // This comment is now inaccurate, the OPTIONS handler is kept.

// --- Start Server ---
Deno.serve(app.fetch)
