// deno-lint-ignore-file no-explicit-any
import { ElevenLabsClient } from 'npm:elevenlabs@^1.58.0'
import { ElevenLabsPayload } from './types.ts'

// --- Environment Variables ---
const elevenlabsApiKey = Deno.env.get('ELEVENLABS_API_KEY')
if (!elevenlabsApiKey) {
  throw new Error('Missing environment variable: ELEVENLABS_API_KEY')
}

// --- ElevenLabs Client ---
const elevenlabsClient = new ElevenLabsClient({
  apiKey: elevenlabsApiKey,
})

/**
 * Creates an agent in ElevenLabs.
 * @param payload The mapped payload for the createAgent API call.
 * @returns The response from the ElevenLabs API.
 * @throws Throws an error if the API call fails.
 */
export async function createElevenLabsAgent(payload: ElevenLabsPayload): Promise<any> {
  console.log('Sending create payload to ElevenLabs:', JSON.stringify(payload, null, 2))
  try {
    // Cast to 'any' to bypass strict SDK type checking for sparse payload
    const response = await elevenlabsClient.conversationalAi.createAgent(payload as any)
    console.log(`ElevenLabs agent created successfully: ${response.agent_id}`)
    return response
  } catch (error) {
    console.error('Error creating agent in ElevenLabs:', error)
    // Re-throw the error to be handled by the main function
    throw error
  }
}

/**
 * Updates an agent in ElevenLabs.
 * @param agentId The ID of the agent to update.
 * @param payload The mapped payload for the updateAgent API call.
 * @returns The response from the ElevenLabs API.
 * @throws Throws an error if the API call fails.
 */
export async function updateElevenLabsAgent(
  agentId: string,
  payload: ElevenLabsPayload,
): Promise<any> {
  // Skip API call if the payload is empty (no fields to update)
  if (Object.keys(payload).length === 0) {
    console.log(
      `No configuration fields provided to update for agent ${agentId}. Skipping ElevenLabs API call.`,
    )
    return { skipped: true, reason: 'No update fields provided' }
  }

  console.log(
    `Sending update payload to ElevenLabs for agent ${agentId}:`,
    JSON.stringify(payload, null, 2),
  )
  try {
    // Cast to 'any' for sparse update payload
    const response = await elevenlabsClient.conversationalAi.updateAgent(agentId, payload as any)
    console.log(`ElevenLabs agent updated successfully: ${agentId}`)
    return response
  } catch (error) {
    console.error(`Error updating agent ${agentId} in ElevenLabs:`, error)
    // Re-throw the error
    throw error
  }
}

/**
 * Deletes an agent from ElevenLabs.
 * @param agentId The ID of the agent to delete.
 * @returns The response from the ElevenLabs API.
 * @throws Throws an error if the API call fails (unless it's a 404 Not Found).
 */
export async function deleteElevenLabsAgent(agentId: string): Promise<any> {
  console.log(`Attempting to delete agent ${agentId} from ElevenLabs...`)
  try {
    const response = await elevenlabsClient.conversationalAi.deleteAgent(agentId)
    console.log(`Successfully deleted agent ${agentId} from ElevenLabs.`)
    return response
  } catch (error: any) {
    console.error(`Error deleting agent ${agentId} from ElevenLabs:`, error)
    // Check if the error is a 404 Not Found
    let status = 500
    if (error && typeof error === 'object') {
      if ('status' in error) status = error.status || 500
      if ('statusCode' in error) status = error.statusCode || status
    }

    if (status === 404) {
      console.warn(
        `Agent ${agentId} not found in ElevenLabs (status 404). This might be okay if already deleted.`,
      )
      // Return a specific object indicating the 404, allowing the caller to decide how to proceed (e.g., still delete from DB)
      return { status: 404, message: 'Agent not found in ElevenLabs' }
    } else {
      // For other errors, re-throw
      throw error
    }
  }
}

/**
 * Parses ElevenLabs API errors to extract status and message.
 * @param error The error object caught from the API call.
 * @returns An object containing the status code and error message.
 */
export function parseElevenLabsError(error: any): { status: number; message: string } {
  let status = 500
  let errorMessage = 'Unknown error during ElevenLabs API call'

  if (error && typeof error === 'object') {
    errorMessage = error.message || JSON.stringify(error)
    if ('status' in error) status = error.status || 500
    if ('statusCode' in error) status = error.statusCode || status

    // Log the actual error body if available
    if (error?.body) {
      console.error('ElevenLabs Error Body:', JSON.stringify(error.body, null, 2))
      // Append details if possible, avoid duplicating the main message if it's already in the body
      const bodyString = JSON.stringify(error.body)
      if (!errorMessage.includes(bodyString)) {
        errorMessage += ` | Details: ${bodyString}`
      }
    }
  } else if (error instanceof Error) {
    errorMessage = error.message
  }

  return { status, message: errorMessage }
}

export async function getAgentSignedUrl(agentId: string): Promise<any> {
  try {
    const response = await elevenlabsClient.conversationalAi.getSignedUrl({
      agent_id: agentId,
    })
    return response
  } catch (error: any) {
    console.error(`Error fetching signed URL for agent ${agentId}:`, error)
    throw error
  }
}

export async function getAllVoices(): Promise<any> {
  try {
    const response = await elevenlabsClient.voices.getAll()
    return response
  } catch (error: any) {
    console.error(`Error fetching voices:`, error)
    throw error
  }
}

export async function createKnowledgeBaseText(payload: any): Promise<any> {
  try {
    const response =
      await elevenlabsClient.conversationalAi.createKnowledgeBaseTextDocument(payload)
    const detail = await elevenlabsClient.conversationalAi.getKnowledgeBaseDocumentById(response.id)
    return detail
  } catch (error: any) {
    console.error(`Error creating knowledge base text:`, error)
    throw error
  }
}

export async function createKnowledgeBaseUrl(payload: { url: string; name: string }): Promise<any> {
  try {
    const response = await elevenlabsClient.conversationalAi.createKnowledgeBaseUrlDocument(payload)
    const detail = await elevenlabsClient.conversationalAi.getKnowledgeBaseDocumentById(response.id)
    return detail
  } catch (error: any) {
    console.error(`Error creating knowledge base file:`, error)
    throw error
  }
}

export async function createKnowledgeBaseFile(payload: {
  file: File | Blob
  name: string
}): Promise<any> {
  try {
    const response =
      await elevenlabsClient.conversationalAi.createKnowledgeBaseFileDocument(payload)
    const detail = await elevenlabsClient.conversationalAi.getKnowledgeBaseDocumentById(response.id)
    return detail
  } catch (error: any) {
    console.error(`Error creating knowledge base file:`, error)
    throw error
  }
}

export async function deleteKnowledgeBase(id: string): Promise<any> {
  try {
    const response = await elevenlabsClient.conversationalAi.deleteKnowledgeBaseDocument(id)
    return response
  } catch (error: any) {
    console.error(`Error deleting knowledge base:`, error)
    throw error
  }
}

export async function knowledgeBaseRagIndexStatus(id: string): Promise<any> {
  try {
    // @ts-ignore // SDK might be outdated, this method exists in API
    const response = await elevenlabsClient.conversationalAi.ragIndexStatus(id, {
      model: 'e5_mistral_7b_instruct',
    })
    return response
  } catch (error: any) {
    console.error(`Error getting knowledge base rag index status:`, error)
    throw error
  }
}
