// deno-lint-ignore-file no-explicit-any
import { AgentConfig, ElevenLabsPayload } from './types.ts'

// Retrieve the tool handler U<PERSON> from environment variables
// This MUST be set in your Supabase function's environment settings.
const ELEVENLABS_TOOL_HANDLER_URL = Deno.env.get('ELEVENLABS_TOOL_HANDLER_URL')

const hubspotToolDescriptions: Record<string, string> = {
  hubspot_create_contact:
    'Creates a new contact in HubSpot. Requires at least an email. Other useful fields: first_name, last_name, phone, job_title, and custom properties.',
  hubspot_get_contact_by_id:
    'Retrieves a specific contact from HubSpot by its ID. Use force_fetch=true to bypass cache.',
  hubspot_search_contacts:
    'Searches for contacts in HubSpot based on a query string. Supports limit and pagination (after).',
  hubspot_update_contact:
    'Updates an existing contact in HubSpot using its ID and the new data. Include only fields to be changed.',
  hubspot_create_company:
    'Creates a new company in HubSpot. Requires at least a name. Other fields: domain, industry, phone, etc.',
  hubspot_get_company_by_id:
    'Retrieves a specific company from HubSpot by its ID. Use force_fetch=true to bypass cache.',
  hubspot_search_companies:
    'Searches for companies in HubSpot based on a query string. Supports limit and pagination (after).',
  hubspot_update_company:
    'Updates an existing company in HubSpot using its ID and the new data. Include only fields to be changed.',
  hubspot_create_deal:
    'Creates a new deal in HubSpot. Requires at least a name. Other fields: amount, pipeline, stage, close_date, etc.',
  hubspot_get_deal_by_id:
    'Retrieves a specific deal from HubSpot by its ID. Use force_fetch=true to bypass cache.',
  hubspot_search_deals:
    'Searches for deals in HubSpot based on a query string. Supports limit and pagination (after).',
  hubspot_update_deal:
    'Updates an existing deal in HubSpot using its ID and the new data. Include only fields to be changed.',
  hubspot_log_note:
    'Logs a note in HubSpot and can associate it with contacts, companies, or deals. Requires note_body and an associations array.',
  hubspot_log_call:
    'Logs a call in HubSpot and can associate it with contacts, companies, or deals. Requires various call details and an associations array.',
}

const systemToolDescriptions: Record<string, string> = {
  end_call: 'Ends the current call gracefully.',
  transfer_to_agent:
    'Transfers the user to a specialized agent based on their request. (Requires params for transfers in prompt config)',
  transfer_to_number:
    'Transfers the user to a human operator or phone number. (Requires params for transfers in prompt config)',
  language_detection: 'Detects the language of the user and can switch agent language presets.',
}

// Defines the JSON schema for the 'parameters' object for each HubSpot tool.
// These are the parameters the LLM is expected to generate and send to the webhook.
// IMPORTANT: Every property at every level MUST have a 'description'.
// For 'additionalProperties': use 'true' to allow extra fields, or OMIT the key entirely to forbid them (most strict).
const hubspotToolParametersSchema: Record<string, any> = {
  hubspot_create_contact: {
    type: 'object',
    description:
      'Parameters generated by the LLM for creating a new HubSpot contact. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      email: { type: 'string', format: 'email', description: "The contact's email address." },
      firstname: { type: 'string', description: "The contact's first name." },
      lastname: { type: 'string', description: "The contact's last name." },
      phone: { type: 'string', description: "The contact's phone number." },
      jobtitle: { type: 'string', description: "The contact's job title." },
    },
    required: ['crm_connection_id', 'organization_id', 'email'],
  },
  hubspot_get_contact_by_id: {
    type: 'object',
    description:
      'Parameters for retrieving a HubSpot contact by ID. Must include crm_connection_id and organization_id.',
    properties: {
      contactId: { type: 'string', description: 'The ID of the contact to retrieve.' },
      force_fetch: {
        type: 'boolean',
        description: 'Optional. If true, bypasses cache. Defaults to false.',
        default: false,
      },
    },
    required: ['contactId'],
  },
  hubspot_search_contacts: {
    type: 'object',
    description:
      'Parameters for searching HubSpot contacts. Must include crm_connection_id and organization_id.',
    properties: {
      query: { type: 'string', description: 'The search query for contacts.' },
      limit: {
        type: 'integer',
        minimum: 1,
        default: 10,
        description: 'Optional. Maximum number of contacts to return. Defaults to 10.',
      },
      after: {
        type: 'string',
        description: 'Optional. Pagination cursor for the next set of contacts.',
      },
    },
    required: ['query'],
  },
  hubspot_update_contact: {
    type: 'object',
    description:
      'Parameters for updating an existing HubSpot contact. Must include crm_connection_id and organization_id.',
    properties: {
      contactId: { type: 'string', description: 'The ID of the contact to update.' },
      email: {
        type: 'string',
        format: 'email',
        description: "(Optional) The contact's new email address.",
      },
      firstname: { type: 'string', description: "(Optional) The contact's new first name." },
      // Define other specific, updatable HubSpot contact properties here if needed by LLM.
    },
    required: ['contactId'],
  },
  hubspot_create_company: {
    type: 'object',
    description:
      'Parameters for creating a new HubSpot company. Must include crm_connection_id and organization_id.',
    properties: {
      name: { type: 'string', description: "The company's name." },
      domain: { type: 'string', description: "(Optional) The company's website domain." },
      industry: { type: 'string', description: "(Optional) The company's industry." },
    },
    required: ['name'],
  },
  hubspot_get_company_by_id: {
    type: 'object',
    description:
      'Parameters for retrieving a HubSpot company by ID. Must include crm_connection_id and organization_id.',
    properties: {
      companyId: { type: 'string', description: 'The ID of the company to retrieve.' },
      force_fetch: {
        type: 'boolean',
        description: 'Optional. If true, bypasses cache. Defaults to false.',
        default: false,
      },
    },
    required: ['companyId'],
  },
  hubspot_search_companies: {
    type: 'object',
    description:
      'Parameters for searching HubSpot companies. Must include crm_connection_id and organization_id.',
    properties: {
      query: { type: 'string', description: 'The search query for companies.' },
      limit: {
        type: 'integer',
        default: 10,
        description: 'Optional. Maximum number of companies to return.',
      },
      after: { type: 'string', description: 'Optional. Pagination cursor.' },
    },
    required: ['query'],
  },
  hubspot_update_company: {
    type: 'object',
    description:
      'Parameters for updating a HubSpot company. Must include crm_connection_id and organization_id.',
    properties: {
      companyId: { type: 'string', description: 'ID of the company to update.' },
      name: { type: 'string', description: "(Optional) The company's new name." },
      // Define other specific, updatable HubSpot company properties here if needed by LLM.
    },
    required: ['companyId'],
  },
  hubspot_create_deal: {
    type: 'object',
    description:
      'Parameters for creating a new HubSpot deal. Must include crm_connection_id and organization_id.',
    properties: {
      dealname: { type: 'string', description: 'Name of the deal.' },
      amount: { type: 'number', description: '(Optional) The deal amount.' },
      pipeline: {
        type: 'string',
        description: '(Optional) The ID of the pipeline the deal belongs to.',
      },
      dealstage: { type: 'string', description: '(Optional) The ID of the deal stage.' },
      closedate: {
        type: 'string',
        format: 'date-time',
        description: "(Optional) The deal's close date in ISO 8601 format.",
      },
    },
    required: ['dealname'],
  },
  hubspot_get_deal_by_id: {
    type: 'object',
    description:
      'Parameters for retrieving a HubSpot deal by ID. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      dealId: { type: 'string', description: 'The ID of the deal to retrieve.' },
      force_fetch: {
        type: 'boolean',
        description: 'Optional. If true, bypasses cache. Defaults to false.',
        default: false,
      },
    },
    required: ['crm_connection_id', 'organization_id', 'dealId'],
  },
  hubspot_search_deals: {
    type: 'object',
    description:
      'Parameters for searching HubSpot deals. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      query: { type: 'string', description: 'The search query for deals.' },
      limit: {
        type: 'integer',
        default: 10,
        description: 'Optional. Maximum number of deals to return.',
      },
      after: { type: 'string', description: 'Optional. Pagination cursor.' },
    },
    required: ['crm_connection_id', 'organization_id', 'query'],
  },
  hubspot_update_deal: {
    type: 'object',
    description:
      'Parameters for updating a HubSpot deal. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      dealId: { type: 'string', description: 'ID of the deal to update.' },
      dealname: { type: 'string', description: "(Optional) The deal's new name." },
      // Define other specific, updatable HubSpot deal properties here if needed by LLM.
    },
    required: ['crm_connection_id', 'organization_id', 'dealId'],
  },
  hubspot_log_note: {
    type: 'object',
    description:
      'Parameters for logging a note in HubSpot. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      note_body: { type: 'string', description: 'Content of the note.' },
      timestamp: {
        type: 'string',
        format: 'date-time',
        description: '(Optional) ISO 8601 UTC timestamp. Defaults to now.',
      },
      associations: {
        type: 'array',
        description: '(Optional) Array of objects to associate the note with.',
        items: {
          type: 'object',
          description: 'Defines a single association to a HubSpot object.',
          properties: {
            toObjectType: {
              type: 'string',
              enum: ['contact', 'company', 'deal'],
              description: 'Type of HubSpot object (contact, company, or deal).',
            },
            toObjectId: { type: 'string', description: 'ID of the HubSpot object.' },
          },
          required: ['toObjectType', 'toObjectId'],
        },
      },
    },
    required: ['crm_connection_id', 'organization_id', 'note_body'],
  },
  hubspot_log_call: {
    type: 'object',
    description:
      'Parameters for logging a call in HubSpot. Must include crm_connection_id and organization_id.',
    properties: {
      crm_connection_id: {
        type: 'string',
        description:
          'HubSpot Connection ID (must be obtained from LLM instructions and included here).',
      },
      organization_id: {
        type: 'string',
        description: 'Organization ID (must be obtained from LLM instructions and included here).',
      },
      body: { type: 'string', description: '(Optional) Notes or transcription of the call.' },
      direction: {
        type: 'string',
        enum: ['INBOUND', 'OUTBOUND'],
        description: '(Optional) Direction of the call.',
      },
      durationMs: {
        type: 'integer',
        description: '(Optional) Duration of the call in milliseconds.',
      },
      status: { type: 'string', description: "(Optional) Status of the call (e.g., 'COMPLETED')." },
      title: { type: 'string', description: '(Optional) Title of the call.' },
      timestamp: {
        type: 'string',
        format: 'date-time',
        description: '(Optional) ISO 8601 UTC timestamp. Defaults to now.',
      },
      associations: {
        type: 'array',
        description: '(Optional) Array of objects to associate the call with.',
        items: {
          type: 'object',
          description: 'Defines a single association to a HubSpot object.',
          properties: {
            toObjectType: {
              type: 'string',
              enum: ['contact', 'company', 'deal'],
              description: 'Type of HubSpot object.',
            },
            toObjectId: { type: 'string', description: 'ID of the HubSpot object.' },
          },
          required: ['toObjectType', 'toObjectId'],
        },
      },
    },
    required: ['crm_connection_id', 'organization_id'], // Call body itself is optional.
  },
}

/**
 * Maps the internal AgentConfig to the payload structure required for
 * creating an agent via the ElevenLabs API.
 */
export function mapToCreatePayload(config: AgentConfig): ElevenLabsPayload {
  const createPayload: ElevenLabsPayload = {}

  if (config.agent_name !== undefined) {
    createPayload.name = config.agent_name
  }

  const convConfig: { [key: string]: any } = {}
  const turnConfig: { [key: string]: any } = {}
  const conversationConfig: { [key: string]: any } = {}
  const agentConfig: { [key: string]: any } = {}
  let promptConfig: { [key: string]: any } | undefined
  const ttsConfig: { [key: string]: any } = {}

  if (config.turn_timeout !== undefined) turnConfig.turn_timeout = config.turn_timeout
  if (config.max_duration_seconds !== undefined) {
    conversationConfig.max_duration_seconds = config.max_duration_seconds
  }
  if (config.first_message_prompt !== undefined)
    agentConfig.first_message = config.first_message_prompt
  if (config.system_prompt !== undefined) {
    promptConfig = { ...(promptConfig || {}), prompt: config.system_prompt }
  }
  if (config.language !== undefined) agentConfig.language = config.language
  if (config.llm !== undefined) {
    promptConfig = { ...(promptConfig || {}), llm: config.llm }
  }
  if (config.temperature !== undefined) {
    promptConfig = { ...(promptConfig || {}), temperature: config.temperature }
  }
  if (config.max_tokens !== undefined) {
    promptConfig = { ...(promptConfig || {}), max_tokens: config.max_tokens }
  }
  if (config.rag_enabled !== undefined) {
    promptConfig = {
      ...(promptConfig || {}),
      rag: {
        enabled: config.rag_enabled,
        embedding_model: 'e5_mistral_7b_instruct',
        max_vector_distance: 0.6,
        max_documents_length: 50000,
      },
    }
  }
  if (config.knowledge_bases !== undefined && config.knowledge_bases.length > 0) {
    promptConfig = {
      ...(promptConfig || {}),
      knowledge_base: config.knowledge_bases.map((kb: any) => ({
        id: kb.eleven_labs_id,
        name: kb.name,
        type: kb.type,
        usage_mode: kb.usage_mode,
      })),
    }
  }

  if (config.tools && Array.isArray(config.tools) && config.tools.length > 0) {
    if (!ELEVENLABS_TOOL_HANDLER_URL && config.tools.some(tool => tool.startsWith('hubspot_'))) {
      console.warn(
        'ELEVENLABS_TOOL_HANDLER_URL environment variable is not set, HubSpot tools will be skipped.',
      )
    }

    const processedTools = config.tools
      .map((toolName: string) => {
        if (toolName.startsWith('hubspot_')) {
          if (!ELEVENLABS_TOOL_HANDLER_URL) return null
          const toolParamsSchema = hubspotToolParametersSchema[toolName]
          if (!toolParamsSchema) {
            console.warn(
              `Missing parameter schema for HubSpot tool: ${toolName}. Skipping tool webhook definition.`,
            )
            return null // Or a default minimal schema if that's preferable for a partial setup
          }
          return {
            type: 'webhook',
            name: toolName,
            description:
              hubspotToolDescriptions[toolName] ||
              `Performs a HubSpot ${toolName.split('_').slice(1).join(' ')} action.`,
            api_schema: {
              url: ELEVENLABS_TOOL_HANDLER_URL,
              method: 'POST',
              request_body_schema: {
                type: 'object',
                required: ['tool_name', 'parameters'],
                description: `Defines the exact structure of the JSON payload sent to the webhook for the ${toolName} tool.`,
                properties: {
                  tool_name: {
                    type: 'string',
                    enum: [toolName],
                    description: 'The specific name of the HubSpot tool. Must be: ' + toolName,
                  },
                  parameters: toolParamsSchema, // Embed the detailed schema for this tool's parameters
                },
              },
            },
          }
        } else if (systemToolDescriptions[toolName]) {
          return {
            type: 'system',
            name: toolName,
            description: systemToolDescriptions[toolName] || `System tool: ${toolName}`,
          }
        }
        console.warn(`Unknown tool or missing description: ${toolName}. Skipping tool definition.`)
        return null
      })
      .filter(tool => tool !== null)

    if (processedTools.length > 0) {
      promptConfig = { ...(promptConfig || {}), tools: processedTools }
    }
  }

  if (promptConfig) agentConfig.prompt = promptConfig

  if (config.voice_id !== undefined) ttsConfig.voice_id = config.voice_id
  if (config.agent_output_audio_format !== undefined) {
    ttsConfig.audio_format = config.agent_output_audio_format
  }
  if (config.model_id !== undefined) ttsConfig.model_id = config.model_id
  if (config.optimize_streaming_latency !== undefined) {
    ttsConfig.optimize_streaming_latency = config.optimize_streaming_latency
  }
  if (config.similarity_boost !== undefined) ttsConfig.similarity_boost = config.similarity_boost
  if (config.speed !== undefined) ttsConfig.speed = config.speed
  if (config.stability !== undefined) ttsConfig.stability = config.stability

  if (Object.keys(ttsConfig).length > 0) convConfig.tts = ttsConfig
  if (Object.keys(turnConfig).length > 0) convConfig.turn = turnConfig
  if (Object.keys(conversationConfig).length > 0) convConfig.conversation = conversationConfig
  if (Object.keys(agentConfig).length > 0) convConfig.agent = agentConfig

  if (Object.keys(convConfig).length > 0) {
    createPayload.conversation_config = convConfig
  }

  const platformSettings: { [key: string]: any } = {
    overrides: {
      conversation_config_override: {
        agent: { prompt: { prompt: true }, first_message: true, language: true },
        tts: { voice_id: true },
      },
      custom_llm_extra_body: false,
      enable_conversation_initiation_client_data_from_webhook: false,
    },
  }
  if (config.data_collection_points !== undefined) {
    platformSettings.data_collection = config.data_collection_points
  }
  if (config.evaluation_criteria !== undefined) {
    platformSettings.evaluation = config.evaluation_criteria
  }
  if (config.data_collection_points || config.evaluation_criteria) {
    createPayload.platform_settings = platformSettings
  }

  if (Object.keys(createPayload).length === 0 && config.agent_name) {
    createPayload.name = config.agent_name
  } else if (Object.keys(createPayload).length === 0) {
    throw new Error('Cannot create agent: No valid configuration fields provided.')
  }

  return createPayload
}

/**
 * Maps the internal AgentConfig to the payload structure required for
 * updating an agent via the ElevenLabs API.
 */
export function mapToUpdatePayload(config: AgentConfig): ElevenLabsPayload {
  const updatePayload: ElevenLabsPayload = {}

  if (config.agent_name !== undefined) updatePayload.name = config.agent_name

  const convConfig: { [key: string]: any } = {}
  const turnConfig: { [key: string]: any } = {}
  const conversationConfig: { [key: string]: any } = {}
  const agentConfig: { [key: string]: any } = {}
  let promptConfig: { [key: string]: any } | undefined
  const ttsConfig: { [key: string]: any } = {}

  if (config.turn_timeout !== undefined) turnConfig.turn_timeout = config.turn_timeout
  if (config.max_duration_seconds !== undefined) {
    conversationConfig.max_duration_seconds = config.max_duration_seconds
  }
  if (config.first_message_prompt !== undefined)
    agentConfig.first_message = config.first_message_prompt
  if (config.system_prompt !== undefined) {
    promptConfig = { ...(promptConfig || {}), prompt: config.system_prompt }
  }
  if (config.language !== undefined) agentConfig.language = config.language
  if (config.llm !== undefined) {
    promptConfig = { ...(promptConfig || {}), llm: config.llm }
  }
  if (config.temperature !== undefined) {
    promptConfig = { ...(promptConfig || {}), temperature: config.temperature }
  }
  if (config.max_tokens !== undefined) {
    promptConfig = { ...(promptConfig || {}), max_tokens: config.max_tokens }
  }
  if (config.rag_enabled !== undefined) {
    promptConfig = {
      ...(promptConfig || {}),
      rag: {
        enabled: config.rag_enabled,
        embedding_model: 'e5_mistral_7b_instruct',
        max_vector_distance: 0.6,
        max_documents_length: 50000,
      },
    }
  }
  if (config.knowledge_bases !== undefined && config.knowledge_bases.length > 0) {
    promptConfig = {
      ...(promptConfig || {}),
      knowledge_base: config.knowledge_bases.map((kb: any) => ({
        id: kb.eleven_labs_id,
        name: kb.name,
        type: kb.type,
        usage_mode: kb.usage_mode,
      })),
    }
  }

  if (config.tools && Array.isArray(config.tools) && config.tools.length > 0) {
    if (!ELEVENLABS_TOOL_HANDLER_URL && config.tools.some(tool => tool.startsWith('hubspot_'))) {
      console.warn(
        'ELEVENLABS_TOOL_HANDLER_URL environment variable is not set, HubSpot tools will be skipped.',
      )
    }
    const processedTools = config.tools
      .map((toolName: string) => {
        if (toolName.startsWith('hubspot_')) {
          if (!ELEVENLABS_TOOL_HANDLER_URL) return null
          const toolParamsSchema = hubspotToolParametersSchema[toolName]
          if (!toolParamsSchema) {
            console.warn(
              `Missing parameter schema for HubSpot tool: ${toolName}. Skipping tool webhook definition.`,
            )
            return null
          }
          return {
            type: 'webhook',
            name: toolName,
            description:
              hubspotToolDescriptions[toolName] ||
              `Performs a HubSpot ${toolName.split('_').slice(1).join(' ')} action.`,
            api_schema: {
              url: ELEVENLABS_TOOL_HANDLER_URL,
              method: 'POST',
              request_body_schema: {
                type: 'object',
                required: ['tool_name', 'parameters'],
                description: `Defines the exact structure of the JSON payload sent to the webhook for the ${toolName} tool.`,
                properties: {
                  tool_name: {
                    type: 'string',
                    enum: [toolName],
                    description: 'The specific name of the HubSpot tool. Must be: ' + toolName,
                  },
                  parameters: toolParamsSchema, // Embed the detailed schema for this tool's parameters
                },
              },
            },
          }
        } else if (systemToolDescriptions[toolName]) {
          return {
            type: 'system',
            name: toolName,
            description: systemToolDescriptions[toolName] || `System tool: ${toolName}`,
          }
        }
        console.warn(`Unknown tool or missing description: ${toolName}. Skipping tool definition.`)
        return null
      })
      .filter(tool => tool !== null)

    if (processedTools.length > 0) {
      promptConfig = { ...(promptConfig || {}), tools: processedTools }
    }
  }

  if (promptConfig) agentConfig.prompt = promptConfig

  if (config.voice_id !== undefined) ttsConfig.voice_id = config.voice_id
  if (config.agent_output_audio_format !== undefined) {
    ttsConfig.audio_format = config.agent_output_audio_format
  }
  if (config.model_id !== undefined) ttsConfig.model_id = config.model_id
  if (config.optimize_streaming_latency !== undefined) {
    ttsConfig.optimize_streaming_latency = config.optimize_streaming_latency
  }
  if (config.similarity_boost !== undefined) ttsConfig.similarity_boost = config.similarity_boost
  if (config.speed !== undefined) ttsConfig.speed = config.speed
  if (config.stability !== undefined) ttsConfig.stability = config.stability

  if (Object.keys(ttsConfig).length > 0) convConfig.tts = ttsConfig
  if (Object.keys(turnConfig).length > 0) convConfig.turn = turnConfig
  if (Object.keys(conversationConfig).length > 0) convConfig.conversation = conversationConfig
  if (Object.keys(agentConfig).length > 0) convConfig.agent = agentConfig

  if (Object.keys(convConfig).length > 0) {
    updatePayload.conversation_config = convConfig
  }

  const platformSettings: { [key: string]: any } = {
    overrides: {
      conversation_config_override: {
        agent: { prompt: { prompt: true }, first_message: true, language: true },
        tts: { voice_id: true },
      },
      custom_llm_extra_body: false,
      enable_conversation_initiation_client_data_from_webhook: false,
    },
  }
  if (config.data_collection_points !== undefined) {
    platformSettings.data_collection = config.data_collection_points
  }
  if (config.evaluation_criteria !== undefined) {
    platformSettings.evaluation = config.evaluation_criteria
  }
  if (config.data_collection_points || config.evaluation_criteria) {
    updatePayload.platform_settings = platformSettings
  }

  return updatePayload
}
