// supabase/functions/manage-elevenlabs-agent/supabase_db.ts
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { AgentConfig, SupabaseAgentRecord, AuthenticatedUser } from './types.ts'

// --- Interfaces for Knowledge Base ---
export interface KnowledgeBasePayload {
  eleven_labs_id: string
  organization_id: string
  user_id: string
  name: string
  type: 'text' | 'url' | 'file'
  content?: string
  prompt_injectable?: boolean
  metadata?: Record<string, string | number | boolean | null> // Allow boolean and null for metadata
  url?: string
  // id and created_at are usually auto-generated by DB
}

export interface KnowledgeBaseRecord extends KnowledgeBasePayload {
  id: string // Assuming 'id' is the primary key and is a string (e.g., UUID)
  created_at: string // Assuming 'created_at' is an ISO string timestamp
  // Add any other fields that are part of the 'knowledge_bases' table
}

// --- Environment Variables ---
const supabaseUrl = Deno.env.get('SUPABASE_URL')
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

if (!supabaseUrl) throw new Error('Missing environment variable: SUPABASE_URL')
if (!supabaseServiceRoleKey)
  throw new Error('Missing environment variable: SUPABASE_SERVICE_ROLE_KEY')

// --- Service Role Client ---
// Use a function to create the client to ensure it's fresh if needed,
// though for a single function invocation, a top-level client is usually fine.
function getServiceSupabaseClient(): SupabaseClient {
  return createClient(supabaseUrl!, supabaseServiceRoleKey!)
}

/**
 * Inserts a new agent record into the Supabase 'agents' table.
 * @param serviceClient The Supabase service role client.
 * @param agentId The ID generated by ElevenLabs.
 * @param config The original agent configuration.
 * @param organizationId The ID of the organization.
 * @param user The authenticated user who initiated the creation.
 * @throws Throws an error if the database insert fails.
 */
export async function insertAgentRecord(
  serviceClient: SupabaseClient,
  agentId: string,
  config: AgentConfig,
  organizationId: string,
  user: AuthenticatedUser,
): Promise<void> {
  const record: SupabaseAgentRecord = {
    agent_id: agentId,
    agent_name: config.agent_name,
    first_message_prompt: config.first_message_prompt,
    system_prompt: config.system_prompt,
    evaluation_criteria: config.evaluation_criteria,
    data_collection_points: config.data_collection_points,
    turn_timeout: config.turn_timeout,
    max_duration_seconds: config.max_duration_seconds,
    tools: config.tools,
    voice_id: config.voice_id,
    language: config.language,
    llm: config.llm,
    temperature: config.temperature,
    max_tokens: config.max_tokens,
    rag_enabled: config.rag_enabled,
    knowledge_bases: config.knowledge_bases,
    agent_output_audio_format: config.agent_output_audio_format,
    model_id: config.model_id,
    optimize_streaming_latency: config.optimize_streaming_latency,
    similarity_boost: config.similarity_boost,
    speed: config.speed,
    stability: config.stability,
    organization_id: organizationId,
    user_id: user.id,
  }

  console.log(`Inserting agent ${agentId} into Supabase...`)
  const { error } = await serviceClient.from('agents').insert(record)

  if (error) {
    console.error(`Error inserting agent ${agentId} into Supabase:`, error)
    throw new Error(`Failed to save agent locally after creation: ${error.message}`)
  }
  console.log(`Agent ${agentId} saved to Supabase successfully.`)
}

/**
 * Updates an existing agent record in the Supabase 'agents' table.
 * @param serviceClient The Supabase service role client.
 * @param agentId The ID of the agent to update.
 * @param config The configuration containing updates.
 * @param organizationId The ID of the organization (for verification).
 * @throws Throws an error if the database update fails.
 */
export async function updateAgentRecord(
  serviceClient: SupabaseClient,
  agentId: string,
  config: AgentConfig,
  organizationId: string,
): Promise<void> {
  const supabaseUpdateData: Partial<SupabaseAgentRecord> & { last_updated: string } = {
    last_updated: new Date().toISOString(),
  }

  // Map only the fields present in the config
  if (config.agent_name !== undefined) supabaseUpdateData.agent_name = config.agent_name
  if (config.first_message_prompt !== undefined)
    supabaseUpdateData.first_message_prompt = config.first_message_prompt
  if (config.system_prompt !== undefined) supabaseUpdateData.system_prompt = config.system_prompt
  if (config.evaluation_criteria !== undefined)
    supabaseUpdateData.evaluation_criteria = config.evaluation_criteria
  if (config.data_collection_points !== undefined)
    supabaseUpdateData.data_collection_points = config.data_collection_points
  if (config.turn_timeout !== undefined) supabaseUpdateData.turn_timeout = config.turn_timeout
  if (config.max_duration_seconds !== undefined)
    supabaseUpdateData.max_duration_seconds = config.max_duration_seconds
  if (config.tools !== undefined) supabaseUpdateData.tools = config.tools
  if (config.voice_id !== undefined) supabaseUpdateData.voice_id = config.voice_id
  if (config.language !== undefined) supabaseUpdateData.language = config.language
  if (config.llm !== undefined) supabaseUpdateData.llm = config.llm
  if (config.temperature !== undefined) supabaseUpdateData.temperature = config.temperature
  if (config.max_tokens !== undefined) supabaseUpdateData.max_tokens = config.max_tokens
  if (config.rag_enabled !== undefined) supabaseUpdateData.rag_enabled = config.rag_enabled
  if (config.knowledge_bases !== undefined)
    supabaseUpdateData.knowledge_bases = config.knowledge_bases
  if (config.agent_output_audio_format !== undefined)
    supabaseUpdateData.agent_output_audio_format = config.agent_output_audio_format
  if (config.model_id !== undefined) supabaseUpdateData.model_id = config.model_id
  if (config.optimize_streaming_latency !== undefined)
    supabaseUpdateData.optimize_streaming_latency = config.optimize_streaming_latency
  if (config.similarity_boost !== undefined)
    supabaseUpdateData.similarity_boost = config.similarity_boost
  if (config.speed !== undefined) supabaseUpdateData.speed = config.speed
  if (config.stability !== undefined) supabaseUpdateData.stability = config.stability

  // Only perform update if there are fields other than last_updated
  if (Object.keys(supabaseUpdateData).length > 1) {
    console.log(`Updating agent ${agentId} in Supabase...`)
    const { error } = await serviceClient
      .from('agents')
      .update(supabaseUpdateData)
      .eq('agent_id', agentId)
      .eq('organization_id', organizationId) // Ensure update is scoped

    if (error) {
      console.error(`Error updating agent ${agentId} in Supabase:`, error)
      throw new Error(`Failed to update agent locally: ${error.message}`)
    }
    console.log(`Agent ${agentId} updated in Supabase successfully.`)
  } else {
    console.log(`No fields to update in Supabase for agent ${agentId}.`)
  }
}

/**
 * Deletes an agent record from the Supabase 'agents' table.
 * @param serviceClient The Supabase service role client.
 * @param agentId The ID of the agent to delete.
 * @param organizationId The ID of the organization (for verification).
 * @throws Throws an error if the database delete fails.
 */
export async function deleteAgentRecord(
  serviceClient: SupabaseClient,
  agentId: string,
  organizationId: string,
): Promise<void> {
  console.log(`Attempting to delete agent ${agentId} from Supabase...`)
  const { error } = await serviceClient
    .from('agents')
    .delete()
    .eq('agent_id', agentId)
    .eq('organization_id', organizationId) // Ensure deletion is scoped

  if (error) {
    console.error(`Error deleting agent ${agentId} from Supabase:`, error)
    throw new Error(`Failed to delete agent locally: ${error.message}`)
  }
  console.log(`Successfully deleted agent ${agentId} from Supabase.`)
}

export async function createKnowledgeBase(
  serviceClient: SupabaseClient,
  payload: KnowledgeBasePayload,
): Promise<KnowledgeBaseRecord[]> {
  // Assuming insert().select() returns an array of records
  try {
    const { data, error } = await serviceClient.from('knowledge_bases').insert(payload).select()
    if (error) {
      throw error
    }
    return data
  } catch (error) {
    console.error('Error creating knowledge base in Supabase:', error) // Changed log message
    throw error
  }
}

export async function deleteKnowledgeBaseRecord(
  serviceClient: SupabaseClient,
  id: string,
): Promise<void> {
  try {
    const { error } = await serviceClient.from('knowledge_bases').delete().eq('id', id)
    if (error) {
      throw error
    }
  } catch (error) {
    console.error('Error deleting knowledge base in ElevenLabs:', error)
    throw error
  }
}

export const getKnowledgeBaseRecord = async (
  serviceClient: SupabaseClient,
  id: string,
): Promise<KnowledgeBaseRecord | null> => {
  // Can be null if not found
  try {
    const { data, error } = await serviceClient
      .from('knowledge_bases')
      .select('*')
      .eq('id', id)
      .single()
    if (error) {
      throw error
    }
    return data
  } catch (error) {
    console.error('Error fetching knowledge base record:', error)
    throw error
  }
}

export const upsertAgentKnowledgeBase = async (
  serviceClient: SupabaseClient,
  agentId: string,
  knowledgeBaseIds: string[],
): Promise<void> => {
  try {
    await serviceClient.from('agent_knowledge_bases').delete().eq('agent_id', agentId)
    if (!knowledgeBaseIds.length) return
    await serviceClient.from('agent_knowledge_bases').upsert(
      knowledgeBaseIds.map(id => ({
        agent_id: agentId,
        knowledge_base_id: id,
      })),
    )
  } catch (error) {
    throw error
  }
}

// Export the function to get the client if needed elsewhere, or keep it internal
export { getServiceSupabaseClient }
