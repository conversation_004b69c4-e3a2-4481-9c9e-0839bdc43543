// deno-lint-ignore-file no-explicit-any
import { ValidatedPayload } from './types.ts'
import { HttpError } from './errors.ts'

/**
 * Parses the request body and validates the payload based on the action.
 * @param req The incoming request object.
 * @returns The validated payload.
 * @throws {HttpError} If parsing fails or validation rules are not met (400).
 */
export async function parseAndValidateRequest(req: Request): Promise<ValidatedPayload> {
  let payload: any
  try {
    payload = await req.json()
  } catch (e) {
    console.error('Failed to parse request body:', e)
    throw new HttpError(400, 'Invalid JSON payload')
  }

  // Basic structure validation
  const { action, agent_id, config, organization_id } = payload

  if (!action || !['create', 'update', 'delete'].includes(action)) {
    throw new HttpError(
      400,
      'Missing or invalid action specified (must be create, update, or delete)',
    )
  }
  if (!organization_id) {
    throw new HttpError(400, 'Missing required field: organization_id')
  }

  // Action-specific validation
  switch (action) {
    case 'create':
      if (!config) {
        throw new HttpError(400, 'Missing required field: config for create action')
      }
      // Optional: Add more specific validation for config fields if needed for create
      // e.g., if (!config.agent_name) throw new HttpError(400, 'Missing agent_name in config for create');
      break
    case 'update':
      if (!agent_id) {
        throw new HttpError(400, 'Missing required field: agent_id for update action')
      }
      if (!config) {
        throw new HttpError(400, 'Missing required field: config for update action')
      }
      // Ensure config is not an empty object if provided for update,
      // although the mapper/API handler might also check this.
      if (typeof config !== 'object' || Object.keys(config).length === 0) {
        throw new HttpError(400, 'Config object cannot be empty for update action')
      }
      break
    case 'delete':
      if (!agent_id) {
        throw new HttpError(400, 'Missing required field: agent_id for delete action')
      }
      // Config should not be present for delete
      if (config) {
        console.warn("Received 'config' field during delete action, it will be ignored.")
        // Optionally throw an error if config is strictly forbidden for delete
        // throw new HttpError(400, 'Config field should not be provided for delete action');
      }
      break
  }

  console.log(
    `Request validated for action: ${action}, org: ${organization_id}, agent: ${agent_id || 'N/A'}`,
  )

  // Return the validated payload, casting config if necessary
  // The types ensure config exists for create/update, agent_id for update/delete
  return {
    action,
    organization_id,
    agent_id: agent_id, // Will be undefined for create, which is fine
    config: config, // Will be undefined for delete, which is fine
  } as ValidatedPayload // Cast needed because TS can't fully track conditional presence here
}
