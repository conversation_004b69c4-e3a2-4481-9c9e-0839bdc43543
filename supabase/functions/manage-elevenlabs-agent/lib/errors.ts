// supabase/functions/manage-elevenlabs-agent/errors.ts

/**
 * Custom error class for HTTP errors, containing status code and message.
 */
export class HttpError extends Error {
  status: number
  originalError?: Error | null // Optional original error for logging

  constructor(status: number, message: string, originalError: Error | null = null) {
    super(message)
    this.name = 'HttpError'
    this.status = status
    this.originalError = originalError

    // Maintains proper stack trace in V8
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, HttpError)
    }
  }
}
