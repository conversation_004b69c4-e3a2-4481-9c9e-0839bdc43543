// deno-lint-ignore-file no-explicit-any

/**
 * Represents the configuration object received in the request payload
 * for creating or updating an agent. Fields are optional as they
 * might not be present for updates or specific configurations.
 */
export interface AgentConfig {
  agent_name?: string
  first_message_prompt?: string
  system_prompt?: string
  evaluation_criteria?: any // Define more strictly if possible
  data_collection_points?: any // Define more strictly if possible
  turn_timeout?: number
  max_duration_seconds?: number
  tools?: any[] // Define tool structure if known
  voice_id?: string
  language?: string
  llm?: string // Or a more specific LLM configuration type
  temperature?: number
  max_tokens?: number
  rag_enabled?: boolean
  knowledge_bases?: any[] // Define knowledge base structure if known
  agent_output_audio_format?: string
  model_id?: string
  optimize_streaming_latency?: number // Or boolean, depending on API
  similarity_boost?: number
  speed?: number
  stability?: number
}

/**
 * Represents the parsed and validated request payload.
 */
export interface ValidatedPayload {
  action: 'create' | 'update' | 'delete'
  organization_id: string
  agent_id?: string // Required for update/delete
  config?: AgentConfig // Required for create/update
}

/**
 * Represents the structure expected by Supabase for the 'agents' table.
 * Includes fields potentially not sent to ElevenLabs (like tools, user_id).
 */
export interface SupabaseAgentRecord {
  agent_id: string
  agent_name?: string
  first_message_prompt?: string
  system_prompt?: string
  evaluation_criteria?: any
  data_collection_points?: any
  turn_timeout?: number
  max_duration_seconds?: number
  tools?: any[]
  voice_id?: string
  language?: string
  llm?: string
  temperature?: number
  max_tokens?: number
  rag_enabled?: boolean
  knowledge_bases?: any[]
  agent_output_audio_format?: string
  model_id?: string
  optimize_streaming_latency?: number
  similarity_boost?: number
  speed?: number
  stability?: number
  organization_id: string
  user_id: string // ID of the user creating the agent
  last_updated?: string // Added for updates
}

/**
 * Represents the structure for ElevenLabs API payloads (simplified).
 * Using 'any' for flexibility due to sparse nature and SDK types.
 */
export type ElevenLabsPayload = { [key: string]: any }

/**
 * Represents the authenticated user object from Supabase.
 */
export interface AuthenticatedUser {
  id: string
  // Add other relevant user properties if needed
}
