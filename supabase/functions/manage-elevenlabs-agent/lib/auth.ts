// supabase/functions/manage-elevenlabs-agent/auth.ts
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { AuthenticatedUser } from './types.ts'
import { HttpError } from './errors.ts'
// --- Environment Variables ---
const supabaseUrl = Deno.env.get('SUPABASE_URL')
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')

if (!supabaseUrl) throw new Error('Missing environment variable: SUPABASE_URL')
if (!supabaseAnonKey) throw new Error('Missing environment variable: SUPABASE_ANON_KEY')

/**
 * Creates a Supabase client instance based on the user's Authorization header.
 * @param authHeader The Authorization header value (e.g., "Bearer <token>").
 * @returns A Supabase client instance authenticated as the user.
 */
function getUserSupabaseClient(authHeader: string): SupabaseClient {
  return createClient(supabaseUrl!, supabaseAnonKey!, {
    global: { headers: { Authorization: authHeader } },
  })
}

/**
 * Authenticates the user based on the Authorization header.
 * @param req The incoming request object.
 * @returns The authenticated user object.
 * @throws {HttpError} If authentication fails (401).
 */
export async function authenticateUser(req: Request): Promise<AuthenticatedUser> {
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    console.error('Authentication failed: Missing Authorization header.')
    throw new HttpError(401, 'Missing Authorization header.')
  }

  const userSupabaseClient = getUserSupabaseClient(authHeader)
  const {
    data: { user },
    error: userError,
  } = await userSupabaseClient.auth.getUser()

  if (userError || !user) {
    console.error('User not authenticated:', userError?.message)
    throw new HttpError(401, 'User not authenticated', userError)
  }

  console.log(`User authenticated: ${user.id}`)
  // Cast to AuthenticatedUser, assuming 'id' is the primary property needed.
  // Add other properties if required by downstream logic.
  return user as AuthenticatedUser
}

/**
 * Authorizes the user for a specific organization, checking for 'owner' or 'admin' role.
 * @param userSupabaseClient The Supabase client authenticated as the user.
 * @param userId The ID of the authenticated user.
 * @param organizationId The ID of the organization to check permissions for.
 * @throws {HttpError} If the database query fails (500) or the user is not authorized (403).
 */
export async function authorizeOrganizationAdmin(
  userSupabaseClient: SupabaseClient,
  userId: string,
  organizationId: string,
): Promise<void> {
  console.log(`Authorizing user ${userId} for admin/owner role in org ${organizationId}...`)
  const { data: memberData, error: memberError } = await userSupabaseClient
    .from('organization_members')
    .select('role')
    .eq('user_id', userId)
    .eq('organization_id', organizationId)
    .in('role', ['owner', 'admin'])
    .maybeSingle() // Use maybeSingle to handle null case gracefully

  if (memberError) {
    console.error(
      `Error checking organization membership for user ${userId} in org ${organizationId}:`,
      memberError,
    )
    throw new HttpError(500, 'Failed to verify user permissions', memberError)
  }

  if (!memberData) {
    console.warn(`User ${userId} not authorized (not admin/owner) for org ${organizationId}.`)
    throw new HttpError(403, 'User not authorized to manage agents in this organization.')
  }

  console.log(`User ${userId} authorized for org ${organizationId} with role ${memberData.role}.`)
}

// Re-export getUserSupabaseClient if needed by the main handler
export { getUserSupabaseClient }
