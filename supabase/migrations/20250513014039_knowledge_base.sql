create type "public"."knowledge_base_type" as enum ('file', 'url', 'text');

create table "public"."agent_knowledge_bases" (
    "id" uuid not null default gen_random_uuid(),
    "agent_id" text not null,
    "knowledge_base_id" uuid not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."agent_knowledge_bases" enable row level security;

create table "public"."knowledge_bases" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "content" text,
    "prompt_injectable" boolean not null default false,
    "type" knowledge_base_type not null,
    "metadata" jsonb default '{}'::jsonb,
    "organization_id" uuid not null,
    "user_id" uuid,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone default now(),
    "deleted_at" timestamp with time zone
);


alter table "public"."knowledge_bases" enable row level security;

CREATE UNIQUE INDEX agent_knowledge_bases_agent_id_knowledge_base_id_key ON public.agent_knowledge_bases USING btree (agent_id, knowledge_base_id);

CREATE UNIQUE INDEX agent_knowledge_bases_pkey ON public.agent_knowledge_bases USING btree (id);

CREATE INDEX idx_agent_knowledge_bases_agent_id ON public.agent_knowledge_bases USING btree (agent_id);

CREATE INDEX idx_agent_knowledge_bases_knowledge_base_id ON public.agent_knowledge_bases USING btree (knowledge_base_id);

CREATE INDEX idx_knowledge_bases_organization_id ON public.knowledge_bases USING btree (organization_id);

CREATE INDEX idx_knowledge_bases_type ON public.knowledge_bases USING btree (type);

CREATE INDEX idx_knowledge_bases_user_id ON public.knowledge_bases USING btree (user_id);

CREATE UNIQUE INDEX knowledge_bases_pkey ON public.knowledge_bases USING btree (id);

alter table "public"."agent_knowledge_bases" add constraint "agent_knowledge_bases_pkey" PRIMARY KEY using index "agent_knowledge_bases_pkey";

alter table "public"."knowledge_bases" add constraint "knowledge_bases_pkey" PRIMARY KEY using index "knowledge_bases_pkey";

alter table "public"."agent_knowledge_bases" add constraint "agent_knowledge_bases_agent_id_fkey" FOREIGN KEY (agent_id) REFERENCES agents(agent_id) ON DELETE CASCADE not valid;

alter table "public"."agent_knowledge_bases" validate constraint "agent_knowledge_bases_agent_id_fkey";

alter table "public"."agent_knowledge_bases" add constraint "agent_knowledge_bases_agent_id_knowledge_base_id_key" UNIQUE using index "agent_knowledge_bases_agent_id_knowledge_base_id_key";

alter table "public"."agent_knowledge_bases" add constraint "agent_knowledge_bases_knowledge_base_id_fkey" FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE not valid;

alter table "public"."agent_knowledge_bases" validate constraint "agent_knowledge_bases_knowledge_base_id_fkey";

alter table "public"."knowledge_bases" add constraint "knowledge_bases_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."knowledge_bases" validate constraint "knowledge_bases_organization_id_fkey";

alter table "public"."knowledge_bases" add constraint "knowledge_bases_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL not valid;

alter table "public"."knowledge_bases" validate constraint "knowledge_bases_user_id_fkey";

grant delete on table "public"."agent_knowledge_bases" to "anon";

grant insert on table "public"."agent_knowledge_bases" to "anon";

grant references on table "public"."agent_knowledge_bases" to "anon";

grant select on table "public"."agent_knowledge_bases" to "anon";

grant trigger on table "public"."agent_knowledge_bases" to "anon";

grant truncate on table "public"."agent_knowledge_bases" to "anon";

grant update on table "public"."agent_knowledge_bases" to "anon";

grant delete on table "public"."agent_knowledge_bases" to "authenticated";

grant insert on table "public"."agent_knowledge_bases" to "authenticated";

grant references on table "public"."agent_knowledge_bases" to "authenticated";

grant select on table "public"."agent_knowledge_bases" to "authenticated";

grant trigger on table "public"."agent_knowledge_bases" to "authenticated";

grant truncate on table "public"."agent_knowledge_bases" to "authenticated";

grant update on table "public"."agent_knowledge_bases" to "authenticated";

grant delete on table "public"."agent_knowledge_bases" to "service_role";

grant insert on table "public"."agent_knowledge_bases" to "service_role";

grant references on table "public"."agent_knowledge_bases" to "service_role";

grant select on table "public"."agent_knowledge_bases" to "service_role";

grant trigger on table "public"."agent_knowledge_bases" to "service_role";

grant truncate on table "public"."agent_knowledge_bases" to "service_role";

grant update on table "public"."agent_knowledge_bases" to "service_role";

grant delete on table "public"."knowledge_bases" to "anon";

grant insert on table "public"."knowledge_bases" to "anon";

grant references on table "public"."knowledge_bases" to "anon";

grant select on table "public"."knowledge_bases" to "anon";

grant trigger on table "public"."knowledge_bases" to "anon";

grant truncate on table "public"."knowledge_bases" to "anon";

grant update on table "public"."knowledge_bases" to "anon";

grant delete on table "public"."knowledge_bases" to "authenticated";

grant insert on table "public"."knowledge_bases" to "authenticated";

grant references on table "public"."knowledge_bases" to "authenticated";

grant select on table "public"."knowledge_bases" to "authenticated";

grant trigger on table "public"."knowledge_bases" to "authenticated";

grant truncate on table "public"."knowledge_bases" to "authenticated";

grant update on table "public"."knowledge_bases" to "authenticated";

grant delete on table "public"."knowledge_bases" to "service_role";

grant insert on table "public"."knowledge_bases" to "service_role";

grant references on table "public"."knowledge_bases" to "service_role";

grant select on table "public"."knowledge_bases" to "service_role";

grant trigger on table "public"."knowledge_bases" to "service_role";

grant truncate on table "public"."knowledge_bases" to "service_role";

grant update on table "public"."knowledge_bases" to "service_role";

create policy "Users can manage agent knowledge bases in their organizations"
on "public"."agent_knowledge_bases"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM agents
  WHERE ((agents.agent_id = agent_knowledge_bases.agent_id) AND (agents.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid())))))));


create policy "Users can view agent knowledge bases in their organizations"
on "public"."agent_knowledge_bases"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM agents
  WHERE ((agents.agent_id = agent_knowledge_bases.agent_id) AND (agents.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid())))))));


create policy "Users can delete knowledge bases in their organizations"
on "public"."knowledge_bases"
as permissive
for delete
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));


create policy "Users can insert knowledge bases in their organizations"
on "public"."knowledge_bases"
as permissive
for insert
to public
with check ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));


create policy "Users can update knowledge bases in their organizations"
on "public"."knowledge_bases"
as permissive
for update
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));


create policy "Users can view knowledge bases in their organizations"
on "public"."knowledge_bases"
as permissive
for select
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));


CREATE TRIGGER set_knowledge_bases_updated_at BEFORE UPDATE ON public.knowledge_bases FOR EACH ROW EXECUTE FUNCTION set_updated_at();


