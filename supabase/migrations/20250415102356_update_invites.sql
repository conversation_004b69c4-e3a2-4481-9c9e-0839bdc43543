create type "public"."organization_invite_status" as enum ('pending', 'accepted', 'declined', 'expired');

alter table "public"."organization_members" alter column "status" drop default;

alter type "public"."organization_status" rename to "organization_status__old_version_to_be_dropped";

create type "public"."organization_status" as enum ('active', 'inactive');

create table "public"."organization_invites" (
    "id" uuid not null default gen_random_uuid(),
    "organization_id" uuid not null,
    "email" text not null,
    "role" organization_role not null default 'member'::organization_role,
    "status" organization_invite_status not null default 'pending'::organization_invite_status,
    "invited_by" uuid not null,
    "expires_at" timestamp with time zone not null default (now() + '7 days'::interval),
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone
);


alter table "public"."organization_members" alter column status type "public"."organization_status" using status::text::"public"."organization_status";

alter table "public"."organization_members" alter column "status" set default 'active'::organization_status;

drop type "public"."organization_status__old_version_to_be_dropped";

CREATE INDEX idx_org_invites_email ON public.organization_invites USING btree (email);

CREATE INDEX idx_org_invites_org_id ON public.organization_invites USING btree (organization_id);

CREATE INDEX idx_org_invites_status ON public.organization_invites USING btree (status);

CREATE UNIQUE INDEX organization_invites_pkey ON public.organization_invites USING btree (id);

CREATE UNIQUE INDEX organization_invites_unique_pending ON public.organization_invites USING btree (organization_id, email) WHERE (status = 'pending'::organization_invite_status);

alter table "public"."organization_invites" add constraint "organization_invites_pkey" PRIMARY KEY using index "organization_invites_pkey";

alter table "public"."organization_invites" add constraint "organization_invites_invited_by_fkey" FOREIGN KEY (invited_by) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."organization_invites" validate constraint "organization_invites_invited_by_fkey";

alter table "public"."organization_invites" add constraint "organization_invites_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."organization_invites" validate constraint "organization_invites_organization_id_fkey";

grant delete on table "public"."organization_invites" to "anon";

grant insert on table "public"."organization_invites" to "anon";

grant references on table "public"."organization_invites" to "anon";

grant select on table "public"."organization_invites" to "anon";

grant trigger on table "public"."organization_invites" to "anon";

grant truncate on table "public"."organization_invites" to "anon";

grant update on table "public"."organization_invites" to "anon";

grant delete on table "public"."organization_invites" to "authenticated";

grant insert on table "public"."organization_invites" to "authenticated";

grant references on table "public"."organization_invites" to "authenticated";

grant select on table "public"."organization_invites" to "authenticated";

grant trigger on table "public"."organization_invites" to "authenticated";

grant truncate on table "public"."organization_invites" to "authenticated";

grant update on table "public"."organization_invites" to "authenticated";

grant delete on table "public"."organization_invites" to "service_role";

grant insert on table "public"."organization_invites" to "service_role";

grant references on table "public"."organization_invites" to "service_role";

grant select on table "public"."organization_invites" to "service_role";

grant trigger on table "public"."organization_invites" to "service_role";

grant truncate on table "public"."organization_invites" to "service_role";

grant update on table "public"."organization_invites" to "service_role";


