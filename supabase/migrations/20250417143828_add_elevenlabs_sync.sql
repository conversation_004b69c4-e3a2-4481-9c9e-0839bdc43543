-- Enable required extensions
create extension if not exists pg_cron with schema extensions;
create extension if not exists pg_net with schema extensions;

-- 1. Create agents table
CREATE TABLE public.agents (
    agent_id TEXT PRIMARY KEY, -- ID from ElevenLabs
    agent_name TEXT NOT NULL,
    first_message_prompt TEXT,
    system_prompt TEXT,
    evaluation_criteria JSONB,
    data_collection_points JSONB,
    turn_timeout INTEGER,
    max_duration_seconds INTEGER,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    last_updated TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for agents table
CREATE INDEX idx_agents_organization_id ON public.agents(organization_id);
CREATE INDEX idx_agents_user_id ON public.agents(user_id);

-- Enable RLS for agents table
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agents table
CREATE POLICY "Users can view agents in their organizations"
  ON public.agents FOR SELECT
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert agents in their organizations"
  ON public.agents FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Users can update agents in their organizations"
  ON public.agents FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id
      FROM public.organization_members
      WHERE user_id = auth.uid()
      AND role IN ('owner', 'admin')
    )
  );

-- 2. Create conversations table (partitioned)
CREATE TABLE public.conversations (
    conversation_id TEXT NOT NULL,
    agent_id TEXT NOT NULL, -- References public.agents(agent_id)
    status TEXT,
    transcript JSONB,
    analysis JSONB,
    metadata JSONB,
    conversation_initiation_client_data JSONB,
    data JSONB,
    created_at TIMESTAMPTZ NOT NULL,
    last_synced TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT conversations_pkey PRIMARY KEY (conversation_id, created_at)
) PARTITION BY RANGE (created_at);

-- Add indexes for conversations table
CREATE INDEX idx_conversations_agent_id ON public.conversations(agent_id);
CREATE INDEX idx_conversations_status ON public.conversations(status);
CREATE INDEX idx_conversations_created_at_idx ON public.conversations(created_at);

-- Enable RLS for conversations table
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversations table
CREATE POLICY "Users can view conversations in their organizations"
  ON public.conversations FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
        )
    )
  );

CREATE POLICY "Users can insert conversations in their organizations"
  ON public.conversations FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );

CREATE POLICY "Users can update conversations in their organizations"
  ON public.conversations FOR UPDATE
  USING (
    EXISTS (
      SELECT 1
      FROM public.agents
      WHERE public.agents.agent_id = public.conversations.agent_id
        AND public.agents.organization_id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
          AND role IN ('owner', 'admin')
        )
    )
  );

-- 3. Create initial partition for conversations (adjust month/year as needed)
-- Assuming current date is April 2025, create partition for May 2025
CREATE TABLE public.conversations_y2025m05 PARTITION OF public.conversations
    FOR VALUES FROM ('2025-05-01 00:00:00+00') TO ('2025-06-01 00:00:00+00');

-- 4. Function to create monthly partitions automatically
CREATE OR REPLACE FUNCTION public.create_monthly_conversation_partition(p_date date)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    partition_name TEXT;
    start_of_month TIMESTAMPTZ;
    end_of_month TIMESTAMPTZ;
BEGIN
    start_of_month := date_trunc('month', p_date)::timestamptz;
    end_of_month := (start_of_month + interval '1 month')::timestamptz;
    partition_name := 'conversations_y' || to_char(start_of_month, 'YYYY') || 'm' || to_char(start_of_month, 'MM');

    -- Check if partition already exists
    IF NOT EXISTS (
        SELECT 1
        FROM   pg_catalog.pg_class c
        JOIN   pg_catalog.pg_namespace n ON n.oid = c.relnamespace
        WHERE  n.nspname = 'public'
        AND    c.relname = partition_name
        AND    c.relkind = 'r' -- 'r' for relation (table)
    ) THEN
        -- Create the partition
        EXECUTE format(
            'CREATE TABLE public.%I PARTITION OF public.conversations FOR VALUES FROM (%L) TO (%L);',
            partition_name,
            start_of_month,
            end_of_month
        );
        RAISE NOTICE 'Created partition %', partition_name;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
    END IF;
END;
$$;

-- 5. Schedule monthly partition creation job with pg_cron
-- Runs at 00:00 on the 1st day of every month
-- Creates partition for the *next* month
SELECT cron.schedule(
    'create-monthly-conversation-partition',
    '0 0 1 * *', -- At 00:00 on day-of-month 1
    $$ SELECT public.create_monthly_conversation_partition(CURRENT_DATE + interval '1 month'); $$
);
