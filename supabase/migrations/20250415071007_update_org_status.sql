alter table "public"."organization_members" alter column "status" drop default;

alter type "public"."organization_status" rename to "organization_status__old_version_to_be_dropped";

create type "public"."organization_status" as enum ('active', 'inactive', 'pending', 'declined');

alter table "public"."organization_members" alter column status type "public"."organization_status" using status::text::"public"."organization_status";

alter table "public"."organization_members" alter column "status" set default 'active'::organization_status;

drop type "public"."organization_status__old_version_to_be_dropped";


