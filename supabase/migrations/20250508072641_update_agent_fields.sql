alter table "public"."agents" add column "agent_output_audio_format" text;

alter table "public"."agents" add column "language" text default 'en'::text;

alter table "public"."agents" add column "llm" text default 'gemini-2.0-flash-001'::text;

alter table "public"."agents" add column "max_tokens" integer default '-1'::integer;

alter table "public"."agents" add column "model_id" text default 'eleven_turbo_v2'::text;

alter table "public"."agents" add column "optimize_streaming_latency" integer default 3;

alter table "public"."agents" add column "similarity_boost" double precision default 0.8;

alter table "public"."agents" add column "speed" double precision default 1;

alter table "public"."agents" add column "stability" double precision default 0.5;

alter table "public"."agents" add column "temperature" double precision default 0;

alter table "public"."agents" add column "voice_id" text;


