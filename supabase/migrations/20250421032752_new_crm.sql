create type "public"."crm_change_type" as enum ('create', 'update', 'delete');

create type "public"."crm_entity_type" as enum ('contact', 'deal', 'company', 'call_log');

create type "public"."crm_type" as enum ('hubspot', 'salesforce', 'paragon');

create type "public"."observation_status" as enum ('pending', 'processing', 'completed', 'failed');

create sequence "public"."observation_processing_status_id_seq";

create sequence "public"."observations_id_seq";

create table "public"."cached_crm_contacts" (
    "id" uuid not null default extensions.gen_random_uuid(),
    "crm_connection_id" uuid not null,
    "crm_entity_id" text not null,
    "first_name" text,
    "last_name" text,
    "email" text,
    "phone" text,
    "properties" jsonb,
    "last_synced_at" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."cached_crm_contacts" enable row level security;

create table "public"."crm_connections" (
    "id" uuid not null default extensions.gen_random_uuid(),
    "organization_id" uuid not null,
    "user_id" uuid,
    "crm_type" crm_type not null,
    "credentials" jsonb not null,
    "crm_instance_identifier" text,
    "schema_definition" jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."crm_connections" enable row level security;

create table "public"."observation_processing_status" (
    "id" bigint not null default nextval('observation_processing_status_id_seq'::regclass),
    "observation_id" bigint not null,
    "worker_id" text,
    "status" observation_status not null default 'pending'::observation_status,
    "started_at" timestamp with time zone,
    "finished_at" timestamp with time zone,
    "error_message" text,
    "retry_count" integer default 0,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."observation_processing_status" enable row level security;

create table "public"."observations" (
    "id" bigint not null default nextval('observations_id_seq'::regclass),
    "crm_connection_id" uuid not null,
    "timestamp" timestamp with time zone default now(),
    "source" text not null,
    "entity_type" crm_entity_type not null,
    "entity_id" text not null,
    "change_type" crm_change_type not null,
    "state_snapshot" jsonb not null
);


alter table "public"."observations" enable row level security;

alter sequence "public"."observation_processing_status_id_seq" owned by "public"."observation_processing_status"."id";

alter sequence "public"."observations_id_seq" owned by "public"."observations"."id";

CREATE UNIQUE INDEX cached_crm_contacts_crm_connection_id_crm_entity_id_key ON public.cached_crm_contacts USING btree (crm_connection_id, crm_entity_id);

CREATE UNIQUE INDEX cached_crm_contacts_pkey ON public.cached_crm_contacts USING btree (id);

CREATE UNIQUE INDEX crm_connections_pkey ON public.crm_connections USING btree (id);

CREATE INDEX idx_cached_crm_contacts_crm_connection ON public.cached_crm_contacts USING btree (crm_connection_id);

CREATE INDEX idx_cached_crm_contacts_email ON public.cached_crm_contacts USING btree (email);

CREATE INDEX idx_cached_crm_contacts_last_synced ON public.cached_crm_contacts USING btree (last_synced_at DESC);

CREATE INDEX idx_cached_crm_contacts_phone ON public.cached_crm_contacts USING btree (phone);

CREATE INDEX idx_observation_processing_status_observation_id ON public.observation_processing_status USING btree (observation_id);

CREATE INDEX idx_observation_processing_status_pending ON public.observation_processing_status USING btree (status) WHERE (status = 'pending'::observation_status);

CREATE INDEX idx_observations_crm_connection_entity ON public.observations USING btree (crm_connection_id, entity_type, entity_id);

CREATE INDEX idx_observations_timestamp ON public.observations USING btree ("timestamp" DESC);

CREATE UNIQUE INDEX observation_processing_status_observation_id_key ON public.observation_processing_status USING btree (observation_id);

CREATE UNIQUE INDEX observation_processing_status_pkey ON public.observation_processing_status USING btree (id);

CREATE UNIQUE INDEX observations_pkey ON public.observations USING btree (id);

CREATE UNIQUE INDEX unique_observation ON public.observations USING btree (crm_connection_id, entity_type, entity_id, "timestamp");

alter table "public"."cached_crm_contacts" add constraint "cached_crm_contacts_pkey" PRIMARY KEY using index "cached_crm_contacts_pkey";

alter table "public"."crm_connections" add constraint "crm_connections_pkey" PRIMARY KEY using index "crm_connections_pkey";

alter table "public"."observation_processing_status" add constraint "observation_processing_status_pkey" PRIMARY KEY using index "observation_processing_status_pkey";

alter table "public"."observations" add constraint "observations_pkey" PRIMARY KEY using index "observations_pkey";

alter table "public"."cached_crm_contacts" add constraint "cached_crm_contacts_crm_connection_id_crm_entity_id_key" UNIQUE using index "cached_crm_contacts_crm_connection_id_crm_entity_id_key";

alter table "public"."cached_crm_contacts" add constraint "cached_crm_contacts_crm_connection_id_fkey" FOREIGN KEY (crm_connection_id) REFERENCES crm_connections(id) not valid;

alter table "public"."cached_crm_contacts" validate constraint "cached_crm_contacts_crm_connection_id_fkey";

alter table "public"."crm_connections" add constraint "crm_connections_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."crm_connections" validate constraint "crm_connections_organization_id_fkey";

alter table "public"."crm_connections" add constraint "crm_connections_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL not valid;

alter table "public"."crm_connections" validate constraint "crm_connections_user_id_fkey";

alter table "public"."observation_processing_status" add constraint "observation_processing_status_observation_id_fkey" FOREIGN KEY (observation_id) REFERENCES observations(id) ON DELETE CASCADE not valid;

alter table "public"."observation_processing_status" validate constraint "observation_processing_status_observation_id_fkey";

alter table "public"."observation_processing_status" add constraint "observation_processing_status_observation_id_key" UNIQUE using index "observation_processing_status_observation_id_key";

alter table "public"."observations" add constraint "observations_crm_connection_id_fkey" FOREIGN KEY (crm_connection_id) REFERENCES crm_connections(id) not valid;

alter table "public"."observations" validate constraint "observations_crm_connection_id_fkey";

alter table "public"."observations" add constraint "unique_observation" UNIQUE using index "unique_observation";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.set_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$function$
;

grant delete on table "public"."cached_crm_contacts" to "anon";

grant insert on table "public"."cached_crm_contacts" to "anon";

grant references on table "public"."cached_crm_contacts" to "anon";

grant select on table "public"."cached_crm_contacts" to "anon";

grant trigger on table "public"."cached_crm_contacts" to "anon";

grant truncate on table "public"."cached_crm_contacts" to "anon";

grant update on table "public"."cached_crm_contacts" to "anon";

grant delete on table "public"."cached_crm_contacts" to "authenticated";

grant insert on table "public"."cached_crm_contacts" to "authenticated";

grant references on table "public"."cached_crm_contacts" to "authenticated";

grant select on table "public"."cached_crm_contacts" to "authenticated";

grant trigger on table "public"."cached_crm_contacts" to "authenticated";

grant truncate on table "public"."cached_crm_contacts" to "authenticated";

grant update on table "public"."cached_crm_contacts" to "authenticated";

grant delete on table "public"."cached_crm_contacts" to "service_role";

grant insert on table "public"."cached_crm_contacts" to "service_role";

grant references on table "public"."cached_crm_contacts" to "service_role";

grant select on table "public"."cached_crm_contacts" to "service_role";

grant trigger on table "public"."cached_crm_contacts" to "service_role";

grant truncate on table "public"."cached_crm_contacts" to "service_role";

grant update on table "public"."cached_crm_contacts" to "service_role";

grant delete on table "public"."crm_connections" to "anon";

grant insert on table "public"."crm_connections" to "anon";

grant references on table "public"."crm_connections" to "anon";

grant select on table "public"."crm_connections" to "anon";

grant trigger on table "public"."crm_connections" to "anon";

grant truncate on table "public"."crm_connections" to "anon";

grant update on table "public"."crm_connections" to "anon";

grant delete on table "public"."crm_connections" to "authenticated";

grant insert on table "public"."crm_connections" to "authenticated";

grant references on table "public"."crm_connections" to "authenticated";

grant select on table "public"."crm_connections" to "authenticated";

grant trigger on table "public"."crm_connections" to "authenticated";

grant truncate on table "public"."crm_connections" to "authenticated";

grant update on table "public"."crm_connections" to "authenticated";

grant delete on table "public"."crm_connections" to "service_role";

grant insert on table "public"."crm_connections" to "service_role";

grant references on table "public"."crm_connections" to "service_role";

grant select on table "public"."crm_connections" to "service_role";

grant trigger on table "public"."crm_connections" to "service_role";

grant truncate on table "public"."crm_connections" to "service_role";

grant update on table "public"."crm_connections" to "service_role";

grant delete on table "public"."observation_processing_status" to "anon";

grant insert on table "public"."observation_processing_status" to "anon";

grant references on table "public"."observation_processing_status" to "anon";

grant select on table "public"."observation_processing_status" to "anon";

grant trigger on table "public"."observation_processing_status" to "anon";

grant truncate on table "public"."observation_processing_status" to "anon";

grant update on table "public"."observation_processing_status" to "anon";

grant delete on table "public"."observation_processing_status" to "authenticated";

grant insert on table "public"."observation_processing_status" to "authenticated";

grant references on table "public"."observation_processing_status" to "authenticated";

grant select on table "public"."observation_processing_status" to "authenticated";

grant trigger on table "public"."observation_processing_status" to "authenticated";

grant truncate on table "public"."observation_processing_status" to "authenticated";

grant update on table "public"."observation_processing_status" to "authenticated";

grant delete on table "public"."observation_processing_status" to "service_role";

grant insert on table "public"."observation_processing_status" to "service_role";

grant references on table "public"."observation_processing_status" to "service_role";

grant select on table "public"."observation_processing_status" to "service_role";

grant trigger on table "public"."observation_processing_status" to "service_role";

grant truncate on table "public"."observation_processing_status" to "service_role";

grant update on table "public"."observation_processing_status" to "service_role";

grant delete on table "public"."observations" to "anon";

grant insert on table "public"."observations" to "anon";

grant references on table "public"."observations" to "anon";

grant select on table "public"."observations" to "anon";

grant trigger on table "public"."observations" to "anon";

grant truncate on table "public"."observations" to "anon";

grant update on table "public"."observations" to "anon";

grant delete on table "public"."observations" to "authenticated";

grant insert on table "public"."observations" to "authenticated";

grant references on table "public"."observations" to "authenticated";

grant select on table "public"."observations" to "authenticated";

grant trigger on table "public"."observations" to "authenticated";

grant truncate on table "public"."observations" to "authenticated";

grant update on table "public"."observations" to "authenticated";

grant delete on table "public"."observations" to "service_role";

grant insert on table "public"."observations" to "service_role";

grant references on table "public"."observations" to "service_role";

grant select on table "public"."observations" to "service_role";

grant trigger on table "public"."observations" to "service_role";

grant truncate on table "public"."observations" to "service_role";

grant update on table "public"."observations" to "service_role";

create policy "Users can view cached contacts in their organizations"
on "public"."cached_crm_contacts"
as permissive
for select
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


create policy "Admins can manage CRM connections in their organizations"
on "public"."crm_connections"
as permissive
for all
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE ((organization_members.user_id = auth.uid()) AND (organization_members.role = ANY (ARRAY['owner'::organization_role, 'admin'::organization_role]))))));


create policy "Users can view CRM connections in their organizations"
on "public"."crm_connections"
as permissive
for select
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));


create policy "Users can view processing status for their organizations' obser"
on "public"."observation_processing_status"
as permissive
for select
to public
using ((observation_id IN ( SELECT observations.id
   FROM observations
  WHERE (observations.crm_connection_id IN ( SELECT crm_connections.id
           FROM crm_connections
          WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
                   FROM organization_members
                  WHERE (organization_members.user_id = auth.uid()))))))));


create policy "Users can view observations for their organizations' CRM connec"
on "public"."observations"
as permissive
for select
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


CREATE TRIGGER set_updated_at BEFORE UPDATE ON public.cached_crm_contacts FOR EACH ROW EXECUTE FUNCTION set_updated_at();

CREATE TRIGGER set_updated_at BEFORE UPDATE ON public.crm_connections FOR EACH ROW EXECUTE FUNCTION set_updated_at();

CREATE TRIGGER set_updated_at BEFORE UPDATE ON public.observation_processing_status FOR EACH ROW EXECUTE FUNCTION set_updated_at();


