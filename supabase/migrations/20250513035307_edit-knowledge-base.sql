alter table "public"."agent_knowledge_bases" drop constraint "agent_knowledge_bases_agent_id_knowledge_base_id_key";

alter table "public"."agent_knowledge_bases" drop constraint "agent_knowledge_bases_pkey";

drop index if exists "public"."agent_knowledge_bases_agent_id_knowledge_base_id_key";

drop index if exists "public"."agent_knowledge_bases_pkey";

alter table "public"."agent_knowledge_bases" drop column "id";

CREATE UNIQUE INDEX agent_knowledge_bases_pkey ON public.agent_knowledge_bases USING btree (agent_id, knowledge_base_id);

alter table "public"."agent_knowledge_bases" add constraint "agent_knowledge_bases_pkey" PRIMARY KEY using index "agent_knowledge_bases_pkey";


