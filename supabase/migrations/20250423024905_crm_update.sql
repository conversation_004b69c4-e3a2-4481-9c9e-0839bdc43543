create table "public"."cached_crm_companies" (
    "id" uuid not null default gen_random_uuid(),
    "crm_connection_id" uuid not null,
    "crm_entity_id" text not null,
    "name" text not null,
    "domain" text,
    "industry" text,
    "type" text,
    "description" text,
    "linkedin_url" text,
    "annual_revenue" numeric,
    "employee_count" integer,
    "phone" text,
    "street_address" text,
    "city" text,
    "state" text,
    "postal_code" text,
    "country" text,
    "timezone" text,
    "properties" jsonb default '{}'::jsonb,
    "last_synced_at" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."company_industries" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "slug" text not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."company_industries" enable row level security;

alter table "public"."cached_crm_contacts" add column "job_title" text;

alter table "public"."crm_connections" add column "is_synced_companies" boolean default false;

alter table "public"."crm_connections" add column "is_synced_contacts" boolean default false;

alter table "public"."crm_connections" add column "is_synced_deals" boolean default false;

CREATE UNIQUE INDEX cached_crm_companies_connection_entity ON public.cached_crm_companies USING btree (crm_connection_id, crm_entity_id);

CREATE UNIQUE INDEX cached_crm_companies_pkey ON public.cached_crm_companies USING btree (id);

CREATE UNIQUE INDEX company_industries_pkey ON public.company_industries USING btree (id);

CREATE UNIQUE INDEX company_industries_slug_unique ON public.company_industries USING btree (slug);

CREATE INDEX idx_company_industries_slug ON public.company_industries USING btree (slug);

alter table "public"."cached_crm_companies" add constraint "cached_crm_companies_pkey" PRIMARY KEY using index "cached_crm_companies_pkey";

alter table "public"."company_industries" add constraint "company_industries_pkey" PRIMARY KEY using index "company_industries_pkey";

alter table "public"."cached_crm_companies" add constraint "cached_crm_companies_connection_entity" UNIQUE using index "cached_crm_companies_connection_entity";

alter table "public"."cached_crm_companies" add constraint "cached_crm_companies_crm_connection_id_fkey" FOREIGN KEY (crm_connection_id) REFERENCES crm_connections(id) ON DELETE CASCADE not valid;

alter table "public"."cached_crm_companies" validate constraint "cached_crm_companies_crm_connection_id_fkey";

alter table "public"."company_industries" add constraint "company_industries_slug_unique" UNIQUE using index "company_industries_slug_unique";

grant delete on table "public"."cached_crm_companies" to "anon";

grant insert on table "public"."cached_crm_companies" to "anon";

grant references on table "public"."cached_crm_companies" to "anon";

grant select on table "public"."cached_crm_companies" to "anon";

grant trigger on table "public"."cached_crm_companies" to "anon";

grant truncate on table "public"."cached_crm_companies" to "anon";

grant update on table "public"."cached_crm_companies" to "anon";

grant delete on table "public"."cached_crm_companies" to "authenticated";

grant insert on table "public"."cached_crm_companies" to "authenticated";

grant references on table "public"."cached_crm_companies" to "authenticated";

grant select on table "public"."cached_crm_companies" to "authenticated";

grant trigger on table "public"."cached_crm_companies" to "authenticated";

grant truncate on table "public"."cached_crm_companies" to "authenticated";

grant update on table "public"."cached_crm_companies" to "authenticated";

grant delete on table "public"."cached_crm_companies" to "service_role";

grant insert on table "public"."cached_crm_companies" to "service_role";

grant references on table "public"."cached_crm_companies" to "service_role";

grant select on table "public"."cached_crm_companies" to "service_role";

grant trigger on table "public"."cached_crm_companies" to "service_role";

grant truncate on table "public"."cached_crm_companies" to "service_role";

grant update on table "public"."cached_crm_companies" to "service_role";

grant delete on table "public"."company_industries" to "anon";

grant insert on table "public"."company_industries" to "anon";

grant references on table "public"."company_industries" to "anon";

grant select on table "public"."company_industries" to "anon";

grant trigger on table "public"."company_industries" to "anon";

grant truncate on table "public"."company_industries" to "anon";

grant update on table "public"."company_industries" to "anon";

grant delete on table "public"."company_industries" to "authenticated";

grant insert on table "public"."company_industries" to "authenticated";

grant references on table "public"."company_industries" to "authenticated";

grant select on table "public"."company_industries" to "authenticated";

grant trigger on table "public"."company_industries" to "authenticated";

grant truncate on table "public"."company_industries" to "authenticated";

grant update on table "public"."company_industries" to "authenticated";

grant delete on table "public"."company_industries" to "service_role";

grant insert on table "public"."company_industries" to "service_role";

grant references on table "public"."company_industries" to "service_role";

grant select on table "public"."company_industries" to "service_role";

grant trigger on table "public"."company_industries" to "service_role";

grant truncate on table "public"."company_industries" to "service_role";

grant update on table "public"."company_industries" to "service_role";

create policy "Industries are viewable by everyone."
on "public"."company_industries"
as permissive
for select
to public
using (true);


-- Insert predefined industries
insert into public.company_industries (name, slug) values
  ('Accounting', 'ACCOUNTING'),
  ('Airlines/Aviation', 'AIRLINES_AVIATION'),
  ('Alternative Dispute Resolution', 'ALTERNATIVE_DISPUTE_RESOLUTION'),
  ('Alternative Medicine', 'ALTERNATIVE_MEDICINE'),
  ('Animation', 'ANIMATION'),
  ('Apparel & Fashion', 'APPAREL_FASHION'),
  ('Architecture & Planning', 'ARCHITECTURE_PLANNING'),
  ('Arts and Crafts', 'ARTS_AND_CRAFTS'),
  ('Automotive', 'AUTOMOTIVE'),
  ('Aviation & Aerospace', 'AVIATION_AEROSPACE'),
  ('Banking', 'BANKING'),
  ('Biotechnology', 'BIOTECHNOLOGY'),
  ('Broadcast Media', 'BROADCAST_MEDIA'),
  ('Building Materials', 'BUILDING_MATERIALS'),
  ('Business Supplies and Equipment', 'BUSINESS_SUPPLIES_AND_EQUIPMENT'),
  ('Capital Markets', 'CAPITAL_MARKETS'),
  ('Chemicals', 'CHEMICALS'),
  ('Civic & Social Organization', 'CIVIC_SOCIAL_ORGANIZATION'),
  ('Civil Engineering', 'CIVIL_ENGINEERING'),
  ('Commercial Real Estate', 'COMMERCIAL_REAL_ESTATE'),
  ('Computer & Network Security', 'COMPUTER_NETWORK_SECURITY'),
  ('Computer Games', 'COMPUTER_GAMES'),
  ('Computer Hardware', 'COMPUTER_HARDWARE'),
  ('Computer Networking', 'COMPUTER_NETWORKING'),
  ('Computer Software', 'COMPUTER_SOFTWARE'),
  ('Internet', 'INTERNET'),
  ('Construction', 'CONSTRUCTION'),
  ('Consumer Electronics', 'CONSUMER_ELECTRONICS'),
  ('Consumer Goods', 'CONSUMER_GOODS'),
  ('Consumer Services', 'CONSUMER_SERVICES'),
  ('Cosmetics', 'COSMETICS'),
  ('Dairy', 'DAIRY'),
  ('Defense & Space', 'DEFENSE_SPACE'),
  ('Design', 'DESIGN'),
  ('Education Management', 'EDUCATION_MANAGEMENT'),
  ('E-Learning', 'E_LEARNING'),
  ('Electrical/Electronic Manufacturing', 'ELECTRICAL_ELECTRONIC_MANUFACTURING'),
  ('Entertainment', 'ENTERTAINMENT'),
  ('Environmental Services', 'ENVIRONMENTAL_SERVICES'),
  ('Events Services', 'EVENTS_SERVICES'),
  ('Executive Office', 'EXECUTIVE_OFFICE'),
  ('Facilities Services', 'FACILITIES_SERVICES'),
  ('Farming', 'FARMING'),
  ('Financial Services', 'FINANCIAL_SERVICES'),
  ('Fine Art', 'FINE_ART'),
  ('Fishery', 'FISHERY'),
  ('Food & Beverages', 'FOOD_BEVERAGES'),
  ('Food Production', 'FOOD_PRODUCTION'),
  ('Fund-Raising', 'FUND_RAISING'),
  ('Furniture', 'FURNITURE'),
  ('Gambling & Casinos', 'GAMBLING_CASINOS'),
  ('Glass, Ceramics & Concrete', 'GLASS_CERAMICS_CONCRETE'),
  ('Government Administration', 'GOVERNMENT_ADMINISTRATION'),
  ('Government Relations', 'GOVERNMENT_RELATIONS'),
  ('Graphic Design', 'GRAPHIC_DESIGN'),
  ('Health, Wellness and Fitness', 'HEALTH_WELLNESS_AND_FITNESS'),
  ('Higher Education', 'HIGHER_EDUCATION'),
  ('Hospital & Health Care', 'HOSPITAL_HEALTH_CARE'),
  ('Hospitality', 'HOSPITALITY'),
  ('Human Resources', 'HUMAN_RESOURCES'),
  ('Import and Export', 'IMPORT_AND_EXPORT'),
  ('Individual & Family Services', 'INDIVIDUAL_FAMILY_SERVICES'),
  ('Industrial Automation', 'INDUSTRIAL_AUTOMATION'),
  ('Information Services', 'INFORMATION_SERVICES'),
  ('Information Technology and Services', 'INFORMATION_TECHNOLOGY_AND_SERVICES'),
  ('Insurance', 'INSURANCE'),
  ('International Affairs', 'INTERNATIONAL_AFFAIRS'),
  ('International Trade and Development', 'INTERNATIONAL_TRADE_AND_DEVELOPMENT'),
  ('Investment Banking', 'INVESTMENT_BANKING'),
  ('Investment Management', 'INVESTMENT_MANAGEMENT'),
  ('Judiciary', 'JUDICIARY'),
  ('Law Enforcement', 'LAW_ENFORCEMENT'),
  ('Law Practice', 'LAW_PRACTICE'),
  ('Legal Services', 'LEGAL_SERVICES'),
  ('Legislative Office', 'LEGISLATIVE_OFFICE'),
  ('Leisure, Travel & Tourism', 'LEISURE_TRAVEL_TOURISM');

