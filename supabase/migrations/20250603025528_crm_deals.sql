create table "public"."cached_crm_deals" (
    "id" uuid not null default gen_random_uuid(),
    "crm_connection_id" uuid not null,
    "crm_entity_id" text not null,
    "name" text not null,
    "amount" numeric,
    "pipeline" text not null default 'default'::text,
    "stage" text not null,
    "close_date" timestamp with time zone,
    "properties" jsonb default '{}'::jsonb,
    "last_synced_at" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."cached_crm_deals" enable row level security;

CREATE UNIQUE INDEX cached_crm_deals_connection_entity ON public.cached_crm_deals USING btree (crm_connection_id, crm_entity_id);

CREATE UNIQUE INDEX cached_crm_deals_pkey ON public.cached_crm_deals USING btree (id);

CREATE INDEX idx_cached_crm_deals_close_date ON public.cached_crm_deals USING btree (close_date);

CREATE INDEX idx_cached_crm_deals_crm_connection ON public.cached_crm_deals USING btree (crm_connection_id);

CREATE INDEX idx_cached_crm_deals_last_synced ON public.cached_crm_deals USING btree (last_synced_at DESC);

CREATE INDEX idx_cached_crm_deals_pipeline_stage ON public.cached_crm_deals USING btree (pipeline, stage);

alter table "public"."cached_crm_deals" add constraint "cached_crm_deals_pkey" PRIMARY KEY using index "cached_crm_deals_pkey";

alter table "public"."cached_crm_deals" add constraint "cached_crm_deals_connection_entity" UNIQUE using index "cached_crm_deals_connection_entity";

alter table "public"."cached_crm_deals" add constraint "cached_crm_deals_crm_connection_id_fkey" FOREIGN KEY (crm_connection_id) REFERENCES crm_connections(id) ON DELETE CASCADE not valid;

alter table "public"."cached_crm_deals" validate constraint "cached_crm_deals_crm_connection_id_fkey";

grant delete on table "public"."cached_crm_deals" to "anon";

grant insert on table "public"."cached_crm_deals" to "anon";

grant references on table "public"."cached_crm_deals" to "anon";

grant select on table "public"."cached_crm_deals" to "anon";

grant trigger on table "public"."cached_crm_deals" to "anon";

grant truncate on table "public"."cached_crm_deals" to "anon";

grant update on table "public"."cached_crm_deals" to "anon";

grant delete on table "public"."cached_crm_deals" to "authenticated";

grant insert on table "public"."cached_crm_deals" to "authenticated";

grant references on table "public"."cached_crm_deals" to "authenticated";

grant select on table "public"."cached_crm_deals" to "authenticated";

grant trigger on table "public"."cached_crm_deals" to "authenticated";

grant truncate on table "public"."cached_crm_deals" to "authenticated";

grant update on table "public"."cached_crm_deals" to "authenticated";

grant delete on table "public"."cached_crm_deals" to "service_role";

grant insert on table "public"."cached_crm_deals" to "service_role";

grant references on table "public"."cached_crm_deals" to "service_role";

grant select on table "public"."cached_crm_deals" to "service_role";

grant trigger on table "public"."cached_crm_deals" to "service_role";

grant truncate on table "public"."cached_crm_deals" to "service_role";

grant update on table "public"."cached_crm_deals" to "service_role";

create policy "Users can insert/update cached deals in their organizations"
on "public"."cached_crm_deals"
as permissive
for all
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


create policy "Users can view cached deals in their organizations"
on "public"."cached_crm_deals"
as permissive
for select
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


CREATE TRIGGER set_updated_at BEFORE UPDATE ON public.cached_crm_deals FOR EACH ROW EXECUTE FUNCTION set_updated_at();


