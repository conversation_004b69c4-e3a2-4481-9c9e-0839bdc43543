-- Allow authenticated users to upload their own avatar
create policy "Users can upload their own avatar."
  on storage.objects for insert
  with check (
    auth.role() = 'authenticated' AND
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = 'avatars' AND
    -- Ensure users can only upload files with their user ID prefix
    position(auth.uid()::text in name) = 1
  );

-- Allow public access to view avatars
create policy "Avatar images are publicly accessible."
  on storage.objects for select
  using ( bucket_id = 'avatars' AND (storage.foldername(name))[1] = 'avatars' );

create policy "allow all 1oj01fe_1" on storage.objects for insert to authenticated with check (bucket_id = 'avatars');