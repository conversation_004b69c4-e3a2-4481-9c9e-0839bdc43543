create policy "Users can insert/update cached companies in their organizations"
on "public"."cached_crm_companies"
as permissive
for all
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


create policy "Users can view cached companies in their organizations"
on "public"."cached_crm_companies"
as permissive
for select
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));



