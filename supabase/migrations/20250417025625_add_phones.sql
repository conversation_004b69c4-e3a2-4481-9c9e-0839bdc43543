create type "public"."phone_provider" as enum ('twilio', 'sip_trunk');

create table "public"."phones" (
    "id" uuid not null default gen_random_uuid(),
    "eleven_labs_phone_id" text not null,
    "organization_id" uuid not null,
    "phone_number" text not null,
    "label" text not null,
    "sid" text,
    "token" text,
    "provider" phone_provider not null,
    "termination_uri" text,
    "credentials" jsonb default '{"password": null, "username": null}'::jsonb,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone
);


alter table "public"."phones" enable row level security;

CREATE INDEX idx_phones_organization ON public.phones USING btree (organization_id);

CREATE UNIQUE INDEX phones_pkey ON public.phones USING btree (id);

CREATE UNIQUE INDEX unique_phone_number ON public.phones USING btree (phone_number);

alter table "public"."phones" add constraint "phones_pkey" PRIMARY KEY using index "phones_pkey";

alter table "public"."phones" add constraint "phones_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."phones" validate constraint "phones_organization_id_fkey";

alter table "public"."phones" add constraint "unique_phone_number" UNIQUE using index "unique_phone_number";

grant delete on table "public"."phones" to "anon";

grant insert on table "public"."phones" to "anon";

grant references on table "public"."phones" to "anon";

grant select on table "public"."phones" to "anon";

grant trigger on table "public"."phones" to "anon";

grant truncate on table "public"."phones" to "anon";

grant update on table "public"."phones" to "anon";

grant delete on table "public"."phones" to "authenticated";

grant insert on table "public"."phones" to "authenticated";

grant references on table "public"."phones" to "authenticated";

grant select on table "public"."phones" to "authenticated";

grant trigger on table "public"."phones" to "authenticated";

grant truncate on table "public"."phones" to "authenticated";

grant update on table "public"."phones" to "authenticated";

grant delete on table "public"."phones" to "service_role";

grant insert on table "public"."phones" to "service_role";

grant references on table "public"."phones" to "service_role";

grant select on table "public"."phones" to "service_role";

grant trigger on table "public"."phones" to "service_role";

grant truncate on table "public"."phones" to "service_role";

grant update on table "public"."phones" to "service_role";

create policy "Users can insert phones in their organizations"
on "public"."phones"
as permissive
for insert
to public
with check ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE ((organization_members.user_id = auth.uid()) AND (organization_members.role = ANY (ARRAY['owner'::organization_role, 'admin'::organization_role]))))));


create policy "Users can update phones in their organizations"
on "public"."phones"
as permissive
for update
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE ((organization_members.user_id = auth.uid()) AND (organization_members.role = ANY (ARRAY['owner'::organization_role, 'admin'::organization_role]))))));


create policy "Users can view phones in their organizations"
on "public"."phones"
as permissive
for select
to public
using ((organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid()))));



