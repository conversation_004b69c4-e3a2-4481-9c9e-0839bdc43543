set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 R<PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
begin
  insert into public.profiles (id, email, name, provider, avatar)
  values (new.id, new.email, new.raw_user_meta_data ->> 'full_name', new.raw_app_meta_data ->> 'provider', new.raw_user_meta_data ->> 'avatar_url');
  return new;
end;
$function$
;


