create type "public"."message_type" as enum ('ai', 'user');

create table "public"."conversation_threads" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "organization_id" uuid not null,
    "agent_id" text,
    "title" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."conversation_threads" enable row level security;

create table "public"."thread_messages" (
    "id" uuid not null default gen_random_uuid(),
    "thread_id" uuid not null,
    "message_type" message_type not null,
    "content" text not null,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone not null default now(),
    "elevenlabs_conversation_id" text,
    "sequence_number" integer not null
);


alter table "public"."thread_messages" enable row level security;

CREATE UNIQUE INDEX conversation_threads_pkey ON public.conversation_threads USING btree (id);

CREATE INDEX idx_conversation_threads_agent_id ON public.conversation_threads USING btree (agent_id);

CREATE INDEX idx_conversation_threads_organization_id ON public.conversation_threads USING btree (organization_id);

CREATE INDEX idx_conversation_threads_updated_at ON public.conversation_threads USING btree (updated_at DESC);

CREATE INDEX idx_conversation_threads_user_id ON public.conversation_threads USING btree (user_id);

CREATE INDEX idx_thread_messages_created_at ON public.thread_messages USING btree (created_at DESC);

CREATE INDEX idx_thread_messages_sequence ON public.thread_messages USING btree (thread_id, sequence_number);

CREATE INDEX idx_thread_messages_thread_id ON public.thread_messages USING btree (thread_id);

CREATE UNIQUE INDEX thread_messages_pkey ON public.thread_messages USING btree (id);

CREATE UNIQUE INDEX unique_thread_sequence ON public.thread_messages USING btree (thread_id, sequence_number);

CREATE UNIQUE INDEX unique_user_org_thread ON public.conversation_threads USING btree (user_id, organization_id);

alter table "public"."conversation_threads" add constraint "conversation_threads_pkey" PRIMARY KEY using index "conversation_threads_pkey";

alter table "public"."thread_messages" add constraint "thread_messages_pkey" PRIMARY KEY using index "thread_messages_pkey";

alter table "public"."conversation_threads" add constraint "conversation_threads_agent_id_fkey" FOREIGN KEY (agent_id) REFERENCES agents(agent_id) ON DELETE SET NULL not valid;

alter table "public"."conversation_threads" validate constraint "conversation_threads_agent_id_fkey";

alter table "public"."conversation_threads" add constraint "conversation_threads_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."conversation_threads" validate constraint "conversation_threads_organization_id_fkey";

alter table "public"."conversation_threads" add constraint "conversation_threads_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."conversation_threads" validate constraint "conversation_threads_user_id_fkey";

alter table "public"."conversation_threads" add constraint "unique_user_org_thread" UNIQUE using index "unique_user_org_thread";

alter table "public"."thread_messages" add constraint "thread_messages_thread_id_fkey" FOREIGN KEY (thread_id) REFERENCES conversation_threads(id) ON DELETE CASCADE not valid;

alter table "public"."thread_messages" validate constraint "thread_messages_thread_id_fkey";

alter table "public"."thread_messages" add constraint "unique_thread_sequence" UNIQUE using index "unique_thread_sequence";

grant delete on table "public"."conversation_threads" to "anon";

grant insert on table "public"."conversation_threads" to "anon";

grant references on table "public"."conversation_threads" to "anon";

grant select on table "public"."conversation_threads" to "anon";

grant trigger on table "public"."conversation_threads" to "anon";

grant truncate on table "public"."conversation_threads" to "anon";

grant update on table "public"."conversation_threads" to "anon";

grant delete on table "public"."conversation_threads" to "authenticated";

grant insert on table "public"."conversation_threads" to "authenticated";

grant references on table "public"."conversation_threads" to "authenticated";

grant select on table "public"."conversation_threads" to "authenticated";

grant trigger on table "public"."conversation_threads" to "authenticated";

grant truncate on table "public"."conversation_threads" to "authenticated";

grant update on table "public"."conversation_threads" to "authenticated";

grant delete on table "public"."conversation_threads" to "service_role";

grant insert on table "public"."conversation_threads" to "service_role";

grant references on table "public"."conversation_threads" to "service_role";

grant select on table "public"."conversation_threads" to "service_role";

grant trigger on table "public"."conversation_threads" to "service_role";

grant truncate on table "public"."conversation_threads" to "service_role";

grant update on table "public"."conversation_threads" to "service_role";

grant delete on table "public"."thread_messages" to "anon";

grant insert on table "public"."thread_messages" to "anon";

grant references on table "public"."thread_messages" to "anon";

grant select on table "public"."thread_messages" to "anon";

grant trigger on table "public"."thread_messages" to "anon";

grant truncate on table "public"."thread_messages" to "anon";

grant update on table "public"."thread_messages" to "anon";

grant delete on table "public"."thread_messages" to "authenticated";

grant insert on table "public"."thread_messages" to "authenticated";

grant references on table "public"."thread_messages" to "authenticated";

grant select on table "public"."thread_messages" to "authenticated";

grant trigger on table "public"."thread_messages" to "authenticated";

grant truncate on table "public"."thread_messages" to "authenticated";

grant update on table "public"."thread_messages" to "authenticated";

grant delete on table "public"."thread_messages" to "service_role";

grant insert on table "public"."thread_messages" to "service_role";

grant references on table "public"."thread_messages" to "service_role";

grant select on table "public"."thread_messages" to "service_role";

grant trigger on table "public"."thread_messages" to "service_role";

grant truncate on table "public"."thread_messages" to "service_role";

grant update on table "public"."thread_messages" to "service_role";

create policy "Users can create threads in their organizations"
on "public"."conversation_threads"
as permissive
for insert
to public
with check (((user_id = auth.uid()) AND (organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid())))));


create policy "Users can update their own threads"
on "public"."conversation_threads"
as permissive
for update
to public
using ((user_id = auth.uid()));


create policy "Users can view their own threads in their organizations"
on "public"."conversation_threads"
as permissive
for select
to public
using (((user_id = auth.uid()) AND (organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid())))));


create policy "Users can insert messages in their threads"
on "public"."thread_messages"
as permissive
for insert
to public
with check ((thread_id IN ( SELECT conversation_threads.id
   FROM conversation_threads
  WHERE (conversation_threads.user_id = auth.uid()))));


create policy "Users can view messages in their threads"
on "public"."thread_messages"
as permissive
for select
to public
using ((thread_id IN ( SELECT conversation_threads.id
   FROM conversation_threads
  WHERE (conversation_threads.user_id = auth.uid()))));



