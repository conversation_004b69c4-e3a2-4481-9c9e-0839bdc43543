set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.cleanup_crm_connection_data()
 R<PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Delete cached contacts
    DELETE FROM public.cached_crm_contacts 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete cached companies
    DELETE FROM public.cached_crm_companies 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete cached deals
    DELETE FROM public.cached_crm_deals 
    WHERE crm_connection_id = OLD.id;
    
    -- Delete observation processing status first (due to foreign key)
    DELETE FROM public.observation_processing_status 
    WHERE observation_id IN (
        SELECT id FROM public.observations 
        WHERE crm_connection_id = OLD.id
    );
    
    -- Delete observations
    DELETE FROM public.observations 
    WHERE crm_connection_id = OLD.id;
    
    RETURN OLD;
END;
$function$
;

create policy "Users can insert/update cached contacts in their organizations"
on "public"."cached_crm_contacts"
as permissive
for all
to public
using ((crm_connection_id IN ( SELECT crm_connections.id
   FROM crm_connections
  WHERE (crm_connections.organization_id IN ( SELECT organization_members.organization_id
           FROM organization_members
          WHERE (organization_members.user_id = auth.uid()))))));


CREATE TRIGGER cleanup_crm_connection_data_trigger BEFORE DELETE ON public.crm_connections FOR EACH ROW EXECUTE FUNCTION cleanup_crm_connection_data();


