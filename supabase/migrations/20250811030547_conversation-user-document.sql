create extension if not exists "vector" with schema "extensions";


create table "public"."user_documents" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "organization_id" uuid not null,
    "title" text not null,
    "content" text not null,
    "embedding" vector(384),
    "created_at" timestamp with time zone default now()
);


alter table "public"."user_documents" enable row level security;

CREATE INDEX idx_user_documents_embedding ON public.user_documents USING ivfflat (embedding vector_cosine_ops) WITH (lists='100');

CREATE INDEX idx_user_documents_user_org ON public.user_documents USING btree (user_id, organization_id);

CREATE UNIQUE INDEX user_documents_pkey ON public.user_documents USING btree (id);

alter table "public"."user_documents" add constraint "user_documents_pkey" PRIMARY KEY using index "user_documents_pkey";

alter table "public"."user_documents" add constraint "user_documents_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."user_documents" validate constraint "user_documents_organization_id_fkey";

alter table "public"."user_documents" add constraint "user_documents_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."user_documents" validate constraint "user_documents_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.match_user_documents(query_embedding vector, target_user_id uuid, target_organization_id uuid, match_threshold double precision DEFAULT 0.8, match_count integer DEFAULT 5)
 RETURNS TABLE(id uuid, title text, content text, similarity double precision)
 LANGUAGE sql
 STABLE
AS $function$
  SELECT
    user_documents.id,
    user_documents.title,
    user_documents.content,
    1 - (user_documents.embedding <=> query_embedding) as similarity
  FROM user_documents
  WHERE 
    user_documents.user_id = target_user_id
    AND user_documents.organization_id = target_organization_id
    AND user_documents.embedding IS NOT NULL
    AND 1 - (user_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY (user_documents.embedding <=> query_embedding) ASC
  LIMIT match_count;
$function$
;

grant delete on table "public"."user_documents" to "anon";

grant insert on table "public"."user_documents" to "anon";

grant references on table "public"."user_documents" to "anon";

grant select on table "public"."user_documents" to "anon";

grant trigger on table "public"."user_documents" to "anon";

grant truncate on table "public"."user_documents" to "anon";

grant update on table "public"."user_documents" to "anon";

grant delete on table "public"."user_documents" to "authenticated";

grant insert on table "public"."user_documents" to "authenticated";

grant references on table "public"."user_documents" to "authenticated";

grant select on table "public"."user_documents" to "authenticated";

grant trigger on table "public"."user_documents" to "authenticated";

grant truncate on table "public"."user_documents" to "authenticated";

grant update on table "public"."user_documents" to "authenticated";

grant delete on table "public"."user_documents" to "service_role";

grant insert on table "public"."user_documents" to "service_role";

grant references on table "public"."user_documents" to "service_role";

grant select on table "public"."user_documents" to "service_role";

grant trigger on table "public"."user_documents" to "service_role";

grant truncate on table "public"."user_documents" to "service_role";

grant update on table "public"."user_documents" to "service_role";

create policy "Users can insert documents"
on "public"."user_documents"
as permissive
for insert
to public
with check (((user_id = auth.uid()) AND (organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid())))));


create policy "Users can view documents in their organizations"
on "public"."user_documents"
as permissive
for select
to public
using (((user_id = auth.uid()) AND (organization_id IN ( SELECT organization_members.organization_id
   FROM organization_members
  WHERE (organization_members.user_id = auth.uid())))));



