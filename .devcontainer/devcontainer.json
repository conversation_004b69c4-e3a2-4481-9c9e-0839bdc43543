{"name": "Devbox Remote Container", "build": {"dockerfile": "./Dockerfile", "context": ".."}, "customizations": {"vscode": {"settings": {}, "extensions": ["jetpack-io.devbox", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss"]}}, "remoteUser": "devbox", "features": {"ghcr.io/devcontainers/features/docker-in-docker:2": {}}, "forwardPorts": [3000, 54321, 54322, 54323, 54324, 54327]}